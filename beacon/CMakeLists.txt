cmake_minimum_required(VERSION 3.10)
project(beacon)

set(target_name beacon)
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

set(VCPKG_TARGET_TRIPLET "x64-osx")

message(STATUS ${CMAKE_SYSTEM_NAME} " " ${CMAKE_BUILD_TYPE})

if(${CMAKE_SYSTEM_NAME} MATCHES "Linux" OR ${CMAKE_SYSTEM_NAME} MATCHES "Darwin" )
    set (UINX TRUE)
    message(STATUS "set (UNIX TRUE)")
endif()

if(${CMAKE_SYSTEM_NAME} MATCHES "Darwin")
    add_definitions(-D_Darwin)
    set(CMAKE_CXX_FLAGS "-std=c++11")
endif()

if(${CMAKE_SYSTEM_NAME} MATCHES "Linux")
    set(CMAKE_CXX_FLAGS "-std=c++11")
    add_definitions(-D_LINUX)
endif()

if(${CMAKE_BUILD_TYPE} MATCHES "Debug")
    add_definitions(-DDEBUG -D_DEBUG -DOUTPUT_LOG)
    message(STATUS "defind DEBUG _DEBUG OUTPUT_LOG")
endif()

add_definitions(-DYASIO_HEADER_ONLY)

# Define the paths to the vcpkg installed libraries
set(VCPKG_ROOT "/Users/<USER>/vcpkg")
set(VCPKG_TRIPLET "x64-osx")  # Set this to the appropriate triplet
set(VCPKG_INSTALLED_DIR "${VCPKG_ROOT}/installed/${VCPKG_TRIPLET}")

# Set the include directories and library directories
include_directories(${VCPKG_INSTALLED_DIR}/include)
link_directories(${VCPKG_INSTALLED_DIR}/lib)

set(BEACON_SRC_DIR ${CMAKE_CURRENT_SOURCE_DIR})
set(PROTOBUF_CPP_DIR ${BEACON_SRC_DIR}/../proto_autogen_cpp)

set(BEACON_COMM_CORE ${BEACON_SRC_DIR}/main.cpp
    ${BEACON_SRC_DIR}/body/beacon/beacon.cpp
    ${BEACON_SRC_DIR}/body/netio/netio.cpp
    ${BEACON_SRC_DIR}/body/msghander/msg.cpp
    ${BEACON_SRC_DIR}/body/msghander/queue.cpp
    ${BEACON_SRC_DIR}/body/msghander/msghandler.cpp
    ${BEACON_SRC_DIR}/base/crypto/crypto.cpp
    ${BEACON_SRC_DIR}/base/log/log.cpp)

set(BEACON_WIN_SRC ${BEACON_SRC_DIR}/body/cmdexec/cmdexec_win.cpp
                    ${BEACON_SRC_DIR}/base/win/winutil.cpp)

set(BEACON_LINUX_SRC ${BEACON_SRC_DIR}/body/cmdexec/cmdexec_inx.cpp
                      ${BEACON_SRC_DIR}/base/inx/inxutil.cpp)

# 使用简化版本，不需要.cc文件
set(PROTOBUF_CPP_SRC)

set(YASIO_DIR ${BEACON_SRC_DIR}/yasio/)
set(COMMON_INC_DIR ${BEACON_SRC_DIR}/common/)

include_directories("${YASIO_DIR}")
include_directories("${COMMON_INC_DIR}")
include_directories(${BEACON_SRC_DIR}/body)
include_directories(${PROTOBUF_CPP_DIR})

if(MSVC)     
    # Use the static C library for all build types
    foreach(var 
        CMAKE_C_FLAGS CMAKE_C_FLAGS_DEBUG CMAKE_C_FLAGS_RELEASE
        CMAKE_C_FLAGS_MINSIZEREL CMAKE_C_FLAGS_RELWITHDEBINFO
        CMAKE_CXX_FLAGS CMAKE_CXX_FLAGS_DEBUG CMAKE_CXX_FLAGS_RELEASE
        CMAKE_CXX_FLAGS_MINSIZEREL CMAKE_CXX_FLAGS_RELWITHDEBINFO
      )
      if(${var} MATCHES "/MD")
        string(REGEX REPLACE "/MD" "/MT" ${var} "${${var}}")
      endif()
    endforeach()    
endif(MSVC)

# set(VCPKG_TARGET_TRIPLET "x64-osx")
# find_package(Protobuf CONFIG REQUIRED)
# set(VCPKG_TARGET_TRIPLET "arm64-osx")
# find_package(Protobuf CONFIG REQUIRED)

# set(VCPKG_TARGET_TRIPLET "x64-osx")
# find_package(cryptopp CONFIG REQUIRED)
# set(VCPKG_TARGET_TRIPLET "arm64-osx")
# find_package(cryptopp CONFIG REQUIRED)

# find_package(absl CONFIG REQUIRED)
find_package(cryptopp CONFIG REQUIRED)
find_package(Protobuf CONFIG REQUIRED)

if(UINX)
    set(CMAKE_CXX_FLAGS_MINSIZEREL  "${CMAKE_CXX_FLAGS_MINSIZEREL} -Os -s")
    set(CMAKE_CXX_FLAGS_RELEASE  "${CMAKE_CXX_FLAGS_RELEASE} -Os -s")

    add_executable(${target_name} ${BEACON_COMM_CORE} ${BEACON_LINUX_SRC} ${PROTOBUF_CPP_SRC})
    target_link_libraries(${target_name}
        PRIVATE cryptopp::cryptopp
        PRIVATE protobuf::libprotobuf-lite
        PRIVATE dl
        PRIVATE pthread
    )
else(UINX)
    add_executable(${target_name} ${BEACON_COMM_CORE} ${BEACON_WIN_SRC} ${PROTOBUF_CPP_SRC})
    target_link_libraries(${target_name}
        PRIVATE cryptopp::cryptopp
        PRIVATE protobuf::libprotobuf-lite
    )
endif(UINX)
