/*
 * Copyright (c) 2021.  https://github.com/geemion
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "inxutil.h"
#include <stdio.h>
#include <sys/ioctl.h>
#include <net/if.h>
#include <netinet/in.h>
#include <unistd.h>
#include <string.h>
#include <sys/wait.h>
#include <pthread.h>
#include <time.h>
#include <signal.h>

#ifdef _Darwin
#include <ifaddrs.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <net/if_dl.h>
// #include <cstring>
// #include <string>
#endif

static int timeout = 30;

bool inx::get_mac(std::string &mac)
#ifdef _Darwin
{
    auto trim = [&](std::string& str){
        if (str.empty())
            return str;
       return  str.substr(0,str.find_first_of("\n"));
    };
    auto ret = execute_cmd("ifconfig en0 | awk '/ether/{print $2}'","/", mac);
    mac = trim(mac);
    return ret;
    //     struct ifaddrs *ifaddr, *ifa;
    // bool success = false;
    // if (getifaddrs(&ifaddr) == -1) {return false;}
    // for (ifa = ifaddr; ifa != NULL; ifa = ifa->ifa_next) {
    //     if (ifa->ifa_addr == NULL)
    //         continue;

    //     if (ifa->ifa_addr->sa_family == AF_LINK && strcmp(ifa->ifa_name, "en0") == 0) {
    //         struct sockaddr_dl *sdl = (struct sockaddr_dl *)ifa->ifa_addr;
    //         unsigned char *ptr = (unsigned char *)LLADDR(sdl);
    //         char temp[18] = {0};
    //         sprintf(temp, "%02X:%02X:%02X:%02X:%02X:%02X",
    //                 ptr[0], ptr[1], ptr[2], ptr[3], ptr[4], ptr[5]);
    //         mac = temp;
    //         success = true;
    //         break;
    //     }
    // }

    // freeifaddrs(ifaddr);
    // return success;
}
#else
{
    struct ifreq ifr;
    struct ifconf ifc;
    char buf[1024];
    bool success = false;

    int sock = socket(AF_INET, SOCK_DGRAM, IPPROTO_IP);
    if (sock == -1)
        return false;

    ifc.ifc_len = sizeof(buf);
    ifc.ifc_buf = buf;
    if (ioctl(sock, SIOCGIFCONF, &ifc) == -1)
    {
        close(sock);
        return false;
    }

    struct ifreq* it = ifc.ifc_req;
    const struct ifreq* const end = it + (ifc.ifc_len / sizeof(struct ifreq));

    for (; it != end; ++it)
    {
        strcpy(ifr.ifr_name, it->ifr_name);
        if (ioctl(sock, SIOCGIFFLAGS, &ifr) == 0)
        {
            if (!(ifr.ifr_flags & IFF_LOOPBACK))
            {
                if (ioctl(sock, SIOCGIFHWADDR, &ifr) == 0)
                {
                    success = true;
                    break;
                }
            }
        }
    }

    if (success)
    {
        char temp[32] = {0};
        sprintf(temp, "%02X-%02X-%02X-%02X-%02X-%02X",
                uint8_t(ifr.ifr_addr.sa_data[0]),
                uint8_t(ifr.ifr_addr.sa_data[1]),
                uint8_t(ifr.ifr_addr.sa_data[2]),
                uint8_t(ifr.ifr_addr.sa_data[3]),
                uint8_t(ifr.ifr_addr.sa_data[4]),
                uint8_t(ifr.ifr_addr.sa_data[5]));
        mac = temp;
        close(sock);
        return true;
    }
    close(sock);
    return false;
}
#endif

/* 定义线程pthread */
static void * pthread(void *arg)       
{

    pid_t pid = (pid_t)*(pid_t *)arg;
    waitpid(pid,NULL,0);
    
     return NULL;
}
bool inx::execute_cmd(const std::string &cmd_line, const std::string &current_dir, std::string &output) 
#ifdef _Darwin
{
    char old_path[100] = { 0 };
    const int buf_size = 1024;
    char buffer[buf_size] = { 0 };
    int pipe_fd[2];
    pid_t pid;
    struct timeval tv;
    fd_set readfds;
    int status;
    bool result = true;
    char temp[buf_size] = { 0 };
    
	getcwd(old_path, sizeof(old_path));
	chdir(current_dir.c_str());

    if (pipe(pipe_fd) == -1) {
        perror("pipe");
        exit(EXIT_FAILURE);
    }

    // 创建子进程
    pid = fork();
    if (pid == -1) {
        perror("fork");
        close(pipe_fd[0]);
        close(pipe_fd[1]);
        exit(EXIT_FAILURE);
    }

    if (pid == 0) { // 子进程
        close(pipe_fd[0]); // 关闭读端
        setenv("PATH", "/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/bin:/sbin", 1);
        dup2(pipe_fd[1], STDOUT_FILENO); // 重定向输出到管道
        dup2(pipe_fd[1], STDERR_FILENO); // 重定向错误输出到管道
        execl("/bin/sh", "sh", "-c", cmd_line.c_str(), (char *)NULL);
        perror("execl"); // 如果execl返回，则出现错误
        exit(EXIT_FAILURE);
    }

    // 父进程
    close(pipe_fd[1]); // 关闭写端
    FD_ZERO(&readfds);
    FD_SET(pipe_fd[0], &readfds);

    int startTime = time(NULL);
    int elapsed;

    // 循环直到超时或命令完成
    while (1) {
        elapsed = time(NULL) - startTime;
        if (elapsed >= timeout) {
            // printf("命令执行超时。\n");
            kill(pid, SIGKILL); // 终止子进程
            break;
        }

        tv.tv_sec = timeout - elapsed; // 更新超时时间
        tv.tv_usec = 0;

        status = select(pipe_fd[0] + 1, &readfds, NULL, NULL, &tv);
        if (status == -1) {
            perror("select");
            break;
        } else if (status == 0) {
            continue; // 重新循环直到超时
        } else if (FD_ISSET(pipe_fd[0], &readfds)) {
            ssize_t bytes_read = read(pipe_fd[0], buffer, sizeof(buffer) - 1);
            if (bytes_read > 0) {
                buffer[bytes_read] = '\0'; // 保证字符串以null结束
                // strncat(output, buffer, sizeof(output) - strlen(output) - 1);
                output += buffer;
            } else if (bytes_read == 0) {
                // 子进程已经关闭管道
                break;
            } else {
                // 读取错误
                if (errno != EAGAIN && errno != EINTR) {
                    perror("read");
                    break;
                }
            }
        }
    }

    close(pipe_fd[0]);

    pid_t wpid;
    int childExitStatus;
    while ((wpid = waitpid(pid, &childExitStatus, 0)) > 0) {
        if (WIFEXITED(childExitStatus)) {
            result = true;
        } else if (WIFSIGNALED(childExitStatus)) {

        }
    }


    if(!output.empty()) {
        result = true;
    } else {
        result = false;
    }
    return result;



}
#else

#endif
