# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/project/mac/beacon

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/project/mac/beacon/buildmk_Release

# Include any dependencies generated for this target.
include CMakeFiles/beacon.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/beacon.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/beacon.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/beacon.dir/flags.make

CMakeFiles/beacon.dir/codegen:
.PHONY : CMakeFiles/beacon.dir/codegen

CMakeFiles/beacon.dir/main.cpp.o: CMakeFiles/beacon.dir/flags.make
CMakeFiles/beacon.dir/main.cpp.o: /Users/<USER>/project/mac/beacon/main.cpp
CMakeFiles/beacon.dir/main.cpp.o: CMakeFiles/beacon.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/project/mac/beacon/buildmk_Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/beacon.dir/main.cpp.o"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/beacon.dir/main.cpp.o -MF CMakeFiles/beacon.dir/main.cpp.o.d -o CMakeFiles/beacon.dir/main.cpp.o -c /Users/<USER>/project/mac/beacon/main.cpp

CMakeFiles/beacon.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/beacon.dir/main.cpp.i"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/project/mac/beacon/main.cpp > CMakeFiles/beacon.dir/main.cpp.i

CMakeFiles/beacon.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/beacon.dir/main.cpp.s"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/project/mac/beacon/main.cpp -o CMakeFiles/beacon.dir/main.cpp.s

CMakeFiles/beacon.dir/body/beacon/beacon.cpp.o: CMakeFiles/beacon.dir/flags.make
CMakeFiles/beacon.dir/body/beacon/beacon.cpp.o: /Users/<USER>/project/mac/beacon/body/beacon/beacon.cpp
CMakeFiles/beacon.dir/body/beacon/beacon.cpp.o: CMakeFiles/beacon.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/project/mac/beacon/buildmk_Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/beacon.dir/body/beacon/beacon.cpp.o"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/beacon.dir/body/beacon/beacon.cpp.o -MF CMakeFiles/beacon.dir/body/beacon/beacon.cpp.o.d -o CMakeFiles/beacon.dir/body/beacon/beacon.cpp.o -c /Users/<USER>/project/mac/beacon/body/beacon/beacon.cpp

CMakeFiles/beacon.dir/body/beacon/beacon.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/beacon.dir/body/beacon/beacon.cpp.i"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/project/mac/beacon/body/beacon/beacon.cpp > CMakeFiles/beacon.dir/body/beacon/beacon.cpp.i

CMakeFiles/beacon.dir/body/beacon/beacon.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/beacon.dir/body/beacon/beacon.cpp.s"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/project/mac/beacon/body/beacon/beacon.cpp -o CMakeFiles/beacon.dir/body/beacon/beacon.cpp.s

CMakeFiles/beacon.dir/body/netio/netio.cpp.o: CMakeFiles/beacon.dir/flags.make
CMakeFiles/beacon.dir/body/netio/netio.cpp.o: /Users/<USER>/project/mac/beacon/body/netio/netio.cpp
CMakeFiles/beacon.dir/body/netio/netio.cpp.o: CMakeFiles/beacon.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/project/mac/beacon/buildmk_Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/beacon.dir/body/netio/netio.cpp.o"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/beacon.dir/body/netio/netio.cpp.o -MF CMakeFiles/beacon.dir/body/netio/netio.cpp.o.d -o CMakeFiles/beacon.dir/body/netio/netio.cpp.o -c /Users/<USER>/project/mac/beacon/body/netio/netio.cpp

CMakeFiles/beacon.dir/body/netio/netio.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/beacon.dir/body/netio/netio.cpp.i"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/project/mac/beacon/body/netio/netio.cpp > CMakeFiles/beacon.dir/body/netio/netio.cpp.i

CMakeFiles/beacon.dir/body/netio/netio.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/beacon.dir/body/netio/netio.cpp.s"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/project/mac/beacon/body/netio/netio.cpp -o CMakeFiles/beacon.dir/body/netio/netio.cpp.s

CMakeFiles/beacon.dir/body/msghander/msg.cpp.o: CMakeFiles/beacon.dir/flags.make
CMakeFiles/beacon.dir/body/msghander/msg.cpp.o: /Users/<USER>/project/mac/beacon/body/msghander/msg.cpp
CMakeFiles/beacon.dir/body/msghander/msg.cpp.o: CMakeFiles/beacon.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/project/mac/beacon/buildmk_Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/beacon.dir/body/msghander/msg.cpp.o"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/beacon.dir/body/msghander/msg.cpp.o -MF CMakeFiles/beacon.dir/body/msghander/msg.cpp.o.d -o CMakeFiles/beacon.dir/body/msghander/msg.cpp.o -c /Users/<USER>/project/mac/beacon/body/msghander/msg.cpp

CMakeFiles/beacon.dir/body/msghander/msg.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/beacon.dir/body/msghander/msg.cpp.i"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/project/mac/beacon/body/msghander/msg.cpp > CMakeFiles/beacon.dir/body/msghander/msg.cpp.i

CMakeFiles/beacon.dir/body/msghander/msg.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/beacon.dir/body/msghander/msg.cpp.s"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/project/mac/beacon/body/msghander/msg.cpp -o CMakeFiles/beacon.dir/body/msghander/msg.cpp.s

CMakeFiles/beacon.dir/body/msghander/queue.cpp.o: CMakeFiles/beacon.dir/flags.make
CMakeFiles/beacon.dir/body/msghander/queue.cpp.o: /Users/<USER>/project/mac/beacon/body/msghander/queue.cpp
CMakeFiles/beacon.dir/body/msghander/queue.cpp.o: CMakeFiles/beacon.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/project/mac/beacon/buildmk_Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/beacon.dir/body/msghander/queue.cpp.o"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/beacon.dir/body/msghander/queue.cpp.o -MF CMakeFiles/beacon.dir/body/msghander/queue.cpp.o.d -o CMakeFiles/beacon.dir/body/msghander/queue.cpp.o -c /Users/<USER>/project/mac/beacon/body/msghander/queue.cpp

CMakeFiles/beacon.dir/body/msghander/queue.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/beacon.dir/body/msghander/queue.cpp.i"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/project/mac/beacon/body/msghander/queue.cpp > CMakeFiles/beacon.dir/body/msghander/queue.cpp.i

CMakeFiles/beacon.dir/body/msghander/queue.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/beacon.dir/body/msghander/queue.cpp.s"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/project/mac/beacon/body/msghander/queue.cpp -o CMakeFiles/beacon.dir/body/msghander/queue.cpp.s

CMakeFiles/beacon.dir/body/msghander/msghandler.cpp.o: CMakeFiles/beacon.dir/flags.make
CMakeFiles/beacon.dir/body/msghander/msghandler.cpp.o: /Users/<USER>/project/mac/beacon/body/msghander/msghandler.cpp
CMakeFiles/beacon.dir/body/msghander/msghandler.cpp.o: CMakeFiles/beacon.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/project/mac/beacon/buildmk_Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/beacon.dir/body/msghander/msghandler.cpp.o"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/beacon.dir/body/msghander/msghandler.cpp.o -MF CMakeFiles/beacon.dir/body/msghander/msghandler.cpp.o.d -o CMakeFiles/beacon.dir/body/msghander/msghandler.cpp.o -c /Users/<USER>/project/mac/beacon/body/msghander/msghandler.cpp

CMakeFiles/beacon.dir/body/msghander/msghandler.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/beacon.dir/body/msghander/msghandler.cpp.i"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/project/mac/beacon/body/msghander/msghandler.cpp > CMakeFiles/beacon.dir/body/msghander/msghandler.cpp.i

CMakeFiles/beacon.dir/body/msghander/msghandler.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/beacon.dir/body/msghander/msghandler.cpp.s"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/project/mac/beacon/body/msghander/msghandler.cpp -o CMakeFiles/beacon.dir/body/msghander/msghandler.cpp.s

CMakeFiles/beacon.dir/base/crypto/crypto.cpp.o: CMakeFiles/beacon.dir/flags.make
CMakeFiles/beacon.dir/base/crypto/crypto.cpp.o: /Users/<USER>/project/mac/beacon/base/crypto/crypto.cpp
CMakeFiles/beacon.dir/base/crypto/crypto.cpp.o: CMakeFiles/beacon.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/project/mac/beacon/buildmk_Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/beacon.dir/base/crypto/crypto.cpp.o"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/beacon.dir/base/crypto/crypto.cpp.o -MF CMakeFiles/beacon.dir/base/crypto/crypto.cpp.o.d -o CMakeFiles/beacon.dir/base/crypto/crypto.cpp.o -c /Users/<USER>/project/mac/beacon/base/crypto/crypto.cpp

CMakeFiles/beacon.dir/base/crypto/crypto.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/beacon.dir/base/crypto/crypto.cpp.i"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/project/mac/beacon/base/crypto/crypto.cpp > CMakeFiles/beacon.dir/base/crypto/crypto.cpp.i

CMakeFiles/beacon.dir/base/crypto/crypto.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/beacon.dir/base/crypto/crypto.cpp.s"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/project/mac/beacon/base/crypto/crypto.cpp -o CMakeFiles/beacon.dir/base/crypto/crypto.cpp.s

CMakeFiles/beacon.dir/base/log/log.cpp.o: CMakeFiles/beacon.dir/flags.make
CMakeFiles/beacon.dir/base/log/log.cpp.o: /Users/<USER>/project/mac/beacon/base/log/log.cpp
CMakeFiles/beacon.dir/base/log/log.cpp.o: CMakeFiles/beacon.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/project/mac/beacon/buildmk_Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/beacon.dir/base/log/log.cpp.o"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/beacon.dir/base/log/log.cpp.o -MF CMakeFiles/beacon.dir/base/log/log.cpp.o.d -o CMakeFiles/beacon.dir/base/log/log.cpp.o -c /Users/<USER>/project/mac/beacon/base/log/log.cpp

CMakeFiles/beacon.dir/base/log/log.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/beacon.dir/base/log/log.cpp.i"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/project/mac/beacon/base/log/log.cpp > CMakeFiles/beacon.dir/base/log/log.cpp.i

CMakeFiles/beacon.dir/base/log/log.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/beacon.dir/base/log/log.cpp.s"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/project/mac/beacon/base/log/log.cpp -o CMakeFiles/beacon.dir/base/log/log.cpp.s

CMakeFiles/beacon.dir/body/cmdexec/cmdexec_inx.cpp.o: CMakeFiles/beacon.dir/flags.make
CMakeFiles/beacon.dir/body/cmdexec/cmdexec_inx.cpp.o: /Users/<USER>/project/mac/beacon/body/cmdexec/cmdexec_inx.cpp
CMakeFiles/beacon.dir/body/cmdexec/cmdexec_inx.cpp.o: CMakeFiles/beacon.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/project/mac/beacon/buildmk_Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/beacon.dir/body/cmdexec/cmdexec_inx.cpp.o"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/beacon.dir/body/cmdexec/cmdexec_inx.cpp.o -MF CMakeFiles/beacon.dir/body/cmdexec/cmdexec_inx.cpp.o.d -o CMakeFiles/beacon.dir/body/cmdexec/cmdexec_inx.cpp.o -c /Users/<USER>/project/mac/beacon/body/cmdexec/cmdexec_inx.cpp

CMakeFiles/beacon.dir/body/cmdexec/cmdexec_inx.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/beacon.dir/body/cmdexec/cmdexec_inx.cpp.i"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/project/mac/beacon/body/cmdexec/cmdexec_inx.cpp > CMakeFiles/beacon.dir/body/cmdexec/cmdexec_inx.cpp.i

CMakeFiles/beacon.dir/body/cmdexec/cmdexec_inx.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/beacon.dir/body/cmdexec/cmdexec_inx.cpp.s"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/project/mac/beacon/body/cmdexec/cmdexec_inx.cpp -o CMakeFiles/beacon.dir/body/cmdexec/cmdexec_inx.cpp.s

CMakeFiles/beacon.dir/base/inx/inxutil.cpp.o: CMakeFiles/beacon.dir/flags.make
CMakeFiles/beacon.dir/base/inx/inxutil.cpp.o: /Users/<USER>/project/mac/beacon/base/inx/inxutil.cpp
CMakeFiles/beacon.dir/base/inx/inxutil.cpp.o: CMakeFiles/beacon.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/project/mac/beacon/buildmk_Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object CMakeFiles/beacon.dir/base/inx/inxutil.cpp.o"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/beacon.dir/base/inx/inxutil.cpp.o -MF CMakeFiles/beacon.dir/base/inx/inxutil.cpp.o.d -o CMakeFiles/beacon.dir/base/inx/inxutil.cpp.o -c /Users/<USER>/project/mac/beacon/base/inx/inxutil.cpp

CMakeFiles/beacon.dir/base/inx/inxutil.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/beacon.dir/base/inx/inxutil.cpp.i"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/project/mac/beacon/base/inx/inxutil.cpp > CMakeFiles/beacon.dir/base/inx/inxutil.cpp.i

CMakeFiles/beacon.dir/base/inx/inxutil.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/beacon.dir/base/inx/inxutil.cpp.s"
	/usr/bin/clang++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/project/mac/beacon/base/inx/inxutil.cpp -o CMakeFiles/beacon.dir/base/inx/inxutil.cpp.s

# Object files for target beacon
beacon_OBJECTS = \
"CMakeFiles/beacon.dir/main.cpp.o" \
"CMakeFiles/beacon.dir/body/beacon/beacon.cpp.o" \
"CMakeFiles/beacon.dir/body/netio/netio.cpp.o" \
"CMakeFiles/beacon.dir/body/msghander/msg.cpp.o" \
"CMakeFiles/beacon.dir/body/msghander/queue.cpp.o" \
"CMakeFiles/beacon.dir/body/msghander/msghandler.cpp.o" \
"CMakeFiles/beacon.dir/base/crypto/crypto.cpp.o" \
"CMakeFiles/beacon.dir/base/log/log.cpp.o" \
"CMakeFiles/beacon.dir/body/cmdexec/cmdexec_inx.cpp.o" \
"CMakeFiles/beacon.dir/base/inx/inxutil.cpp.o"

# External object files for target beacon
beacon_EXTERNAL_OBJECTS =

beacon: CMakeFiles/beacon.dir/main.cpp.o
beacon: CMakeFiles/beacon.dir/body/beacon/beacon.cpp.o
beacon: CMakeFiles/beacon.dir/body/netio/netio.cpp.o
beacon: CMakeFiles/beacon.dir/body/msghander/msg.cpp.o
beacon: CMakeFiles/beacon.dir/body/msghander/queue.cpp.o
beacon: CMakeFiles/beacon.dir/body/msghander/msghandler.cpp.o
beacon: CMakeFiles/beacon.dir/base/crypto/crypto.cpp.o
beacon: CMakeFiles/beacon.dir/base/log/log.cpp.o
beacon: CMakeFiles/beacon.dir/body/cmdexec/cmdexec_inx.cpp.o
beacon: CMakeFiles/beacon.dir/base/inx/inxutil.cpp.o
beacon: CMakeFiles/beacon.dir/build.make
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libcryptopp.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libprotobuf-lite.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_internal_check_op.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_die_if_null.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_internal_conditions.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_internal_message.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_internal_nullguard.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_examine_stack.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_internal_format.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_internal_structured_proto.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_internal_proto.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_internal_log_sink_set.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_sink.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_entry.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_flags_internal.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_flags_marshalling.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_flags_reflection.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_flags_config.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_flags_program_name.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_flags_private_handle_accessor.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_flags_commandlineflag.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_flags_commandlineflag_internal.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_initialize.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_internal_globals.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_globals.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_vlog_config_internal.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_internal_fnmatch.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_raw_hash_set.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_hash.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_city.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_low_level_hash.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_hashtablez_sampler.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_random_distributions.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_random_seed_sequences.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_random_internal_pool_urbg.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_random_internal_randen.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_random_internal_randen_hwaes.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_random_internal_randen_hwaes_impl.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_random_internal_randen_slow.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_random_internal_platform.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_random_internal_seed_material.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_random_seed_gen_exception.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_statusor.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_status.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_cord.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_cordz_info.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_cord_internal.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_cordz_functions.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_exponential_biased.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_cordz_handle.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_crc_cord_state.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_crc32c.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_crc_internal.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_crc_cpu_detect.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_bad_optional_access.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_leak_check.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_strerror.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_str_format_internal.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_synchronization.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_stacktrace.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_symbolize.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_debugging_internal.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_demangle_internal.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_demangle_rust.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_decode_rust_punycode.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_utf8_for_code_point.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_graphcycles_internal.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_kernel_timeout_internal.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_malloc_internal.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_tracing_internal.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_time.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_civil_time.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_time_zone.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_bad_variant_access.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libutf8_validity.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_strings.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_int128.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_strings_internal.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_string_view.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_base.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_spinlock_wait.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_throw_delegate.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_raw_logging_internal.a
beacon: /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_severity.a
beacon: CMakeFiles/beacon.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/project/mac/beacon/buildmk_Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Linking CXX executable beacon"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/beacon.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/beacon.dir/build: beacon
.PHONY : CMakeFiles/beacon.dir/build

CMakeFiles/beacon.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/beacon.dir/cmake_clean.cmake
.PHONY : CMakeFiles/beacon.dir/clean

CMakeFiles/beacon.dir/depend:
	cd /Users/<USER>/project/mac/beacon/buildmk_Release && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/project/mac/beacon /Users/<USER>/project/mac/beacon /Users/<USER>/project/mac/beacon/buildmk_Release /Users/<USER>/project/mac/beacon/buildmk_Release /Users/<USER>/project/mac/beacon/buildmk_Release/CMakeFiles/beacon.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/beacon.dir/depend

