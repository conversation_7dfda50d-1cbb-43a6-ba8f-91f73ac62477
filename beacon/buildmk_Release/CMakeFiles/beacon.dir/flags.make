# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# compile CXX with /usr/bin/clang++
CXX_DEFINES = -DCRYPTOPP_INCLUDE_PREFIX=cryptopp -DYASIO_HEADER_ONLY -D_<PERSON>

CXX_INCLUDES = -I/Users/<USER>/vcpkg/installed/x64-osx/include -I/Users/<USER>/project/mac/beacon/yasio -I/Users/<USER>/project/mac/beacon/common -I/Users/<USER>/project/mac/beacon/body -I/Users/<USER>/project/mac/beacon/../proto_autogen_cpp -isystem /Users/<USER>/vcpkg/installed/arm64-osx/include

CXX_FLAGSarm64 = -std=c++11 -O3 -DNDEBUG -Os -s -std=c++14 -arch arm64

CXX_FLAGS = -std=c++11 -O3 -DNDEBUG -Os -s -std=c++14 -arch arm64

