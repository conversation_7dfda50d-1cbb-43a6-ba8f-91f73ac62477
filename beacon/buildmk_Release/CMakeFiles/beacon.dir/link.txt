/usr/bin/clang++ -std=c++11 -O3 -DNDEBUG -Os -s -arch arm64 -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/beacon.dir/main.cpp.o CMakeFiles/beacon.dir/body/beacon/beacon.cpp.o CMakeFiles/beacon.dir/body/netio/netio.cpp.o CMakeFiles/beacon.dir/body/msghander/msg.cpp.o CMakeFiles/beacon.dir/body/msghander/queue.cpp.o CMakeFiles/beacon.dir/body/msghander/msghandler.cpp.o CMakeFiles/beacon.dir/base/crypto/crypto.cpp.o CMakeFiles/beacon.dir/base/log/log.cpp.o CMakeFiles/beacon.dir/body/cmdexec/cmdexec_inx.cpp.o CMakeFiles/beacon.dir/base/inx/inxutil.cpp.o -o beacon   -L/Users/<USER>/vcpkg/installed/x64-osx/lib  -Wl,-rpath,/Users/<USER>/vcpkg/installed/x64-osx/lib /Users/<USER>/vcpkg/installed/arm64-osx/lib/libcryptopp.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libprotobuf-lite.a -ldl -lpthread /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_internal_check_op.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_die_if_null.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_internal_conditions.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_internal_message.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_internal_nullguard.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_examine_stack.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_internal_format.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_internal_structured_proto.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_internal_proto.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_internal_log_sink_set.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_sink.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_entry.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_flags_internal.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_flags_marshalling.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_flags_reflection.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_flags_config.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_flags_program_name.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_flags_private_handle_accessor.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_flags_commandlineflag.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_flags_commandlineflag_internal.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_initialize.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_internal_globals.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_globals.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_vlog_config_internal.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_internal_fnmatch.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_raw_hash_set.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_hash.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_city.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_low_level_hash.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_hashtablez_sampler.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_random_distributions.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_random_seed_sequences.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_random_internal_pool_urbg.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_random_internal_randen.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_random_internal_randen_hwaes.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_random_internal_randen_hwaes_impl.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_random_internal_randen_slow.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_random_internal_platform.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_random_internal_seed_material.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_random_seed_gen_exception.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_statusor.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_status.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_cord.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_cordz_info.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_cord_internal.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_cordz_functions.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_exponential_biased.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_cordz_handle.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_crc_cord_state.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_crc32c.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_crc_internal.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_crc_cpu_detect.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_bad_optional_access.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_leak_check.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_strerror.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_str_format_internal.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_synchronization.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_stacktrace.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_symbolize.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_debugging_internal.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_demangle_internal.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_demangle_rust.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_decode_rust_punycode.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_utf8_for_code_point.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_graphcycles_internal.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_kernel_timeout_internal.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_malloc_internal.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_tracing_internal.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_time.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_civil_time.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_time_zone.a -Wl,-framework,CoreFoundation /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_bad_variant_access.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libutf8_validity.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_strings.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_int128.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_strings_internal.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_string_view.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_base.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_spinlock_wait.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_throw_delegate.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_raw_logging_internal.a /Users/<USER>/vcpkg/installed/arm64-osx/lib/libabsl_log_severity.a
