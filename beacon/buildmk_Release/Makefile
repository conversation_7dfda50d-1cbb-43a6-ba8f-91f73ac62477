# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/project/mac/beacon

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/project/mac/beacon/buildmk_Release

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/opt/homebrew/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/opt/homebrew/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/project/mac/beacon/buildmk_Release/CMakeFiles /Users/<USER>/project/mac/beacon/buildmk_Release//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/project/mac/beacon/buildmk_Release/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named beacon

# Build rule for target.
beacon: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 beacon
.PHONY : beacon

# fast build rule for target.
beacon/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/build
.PHONY : beacon/fast

base/crypto/crypto.o: base/crypto/crypto.cpp.o
.PHONY : base/crypto/crypto.o

# target to build an object file
base/crypto/crypto.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/base/crypto/crypto.cpp.o
.PHONY : base/crypto/crypto.cpp.o

base/crypto/crypto.i: base/crypto/crypto.cpp.i
.PHONY : base/crypto/crypto.i

# target to preprocess a source file
base/crypto/crypto.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/base/crypto/crypto.cpp.i
.PHONY : base/crypto/crypto.cpp.i

base/crypto/crypto.s: base/crypto/crypto.cpp.s
.PHONY : base/crypto/crypto.s

# target to generate assembly for a file
base/crypto/crypto.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/base/crypto/crypto.cpp.s
.PHONY : base/crypto/crypto.cpp.s

base/inx/inxutil.o: base/inx/inxutil.cpp.o
.PHONY : base/inx/inxutil.o

# target to build an object file
base/inx/inxutil.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/base/inx/inxutil.cpp.o
.PHONY : base/inx/inxutil.cpp.o

base/inx/inxutil.i: base/inx/inxutil.cpp.i
.PHONY : base/inx/inxutil.i

# target to preprocess a source file
base/inx/inxutil.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/base/inx/inxutil.cpp.i
.PHONY : base/inx/inxutil.cpp.i

base/inx/inxutil.s: base/inx/inxutil.cpp.s
.PHONY : base/inx/inxutil.s

# target to generate assembly for a file
base/inx/inxutil.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/base/inx/inxutil.cpp.s
.PHONY : base/inx/inxutil.cpp.s

base/log/log.o: base/log/log.cpp.o
.PHONY : base/log/log.o

# target to build an object file
base/log/log.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/base/log/log.cpp.o
.PHONY : base/log/log.cpp.o

base/log/log.i: base/log/log.cpp.i
.PHONY : base/log/log.i

# target to preprocess a source file
base/log/log.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/base/log/log.cpp.i
.PHONY : base/log/log.cpp.i

base/log/log.s: base/log/log.cpp.s
.PHONY : base/log/log.s

# target to generate assembly for a file
base/log/log.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/base/log/log.cpp.s
.PHONY : base/log/log.cpp.s

body/beacon/beacon.o: body/beacon/beacon.cpp.o
.PHONY : body/beacon/beacon.o

# target to build an object file
body/beacon/beacon.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/body/beacon/beacon.cpp.o
.PHONY : body/beacon/beacon.cpp.o

body/beacon/beacon.i: body/beacon/beacon.cpp.i
.PHONY : body/beacon/beacon.i

# target to preprocess a source file
body/beacon/beacon.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/body/beacon/beacon.cpp.i
.PHONY : body/beacon/beacon.cpp.i

body/beacon/beacon.s: body/beacon/beacon.cpp.s
.PHONY : body/beacon/beacon.s

# target to generate assembly for a file
body/beacon/beacon.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/body/beacon/beacon.cpp.s
.PHONY : body/beacon/beacon.cpp.s

body/cmdexec/cmdexec_inx.o: body/cmdexec/cmdexec_inx.cpp.o
.PHONY : body/cmdexec/cmdexec_inx.o

# target to build an object file
body/cmdexec/cmdexec_inx.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/body/cmdexec/cmdexec_inx.cpp.o
.PHONY : body/cmdexec/cmdexec_inx.cpp.o

body/cmdexec/cmdexec_inx.i: body/cmdexec/cmdexec_inx.cpp.i
.PHONY : body/cmdexec/cmdexec_inx.i

# target to preprocess a source file
body/cmdexec/cmdexec_inx.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/body/cmdexec/cmdexec_inx.cpp.i
.PHONY : body/cmdexec/cmdexec_inx.cpp.i

body/cmdexec/cmdexec_inx.s: body/cmdexec/cmdexec_inx.cpp.s
.PHONY : body/cmdexec/cmdexec_inx.s

# target to generate assembly for a file
body/cmdexec/cmdexec_inx.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/body/cmdexec/cmdexec_inx.cpp.s
.PHONY : body/cmdexec/cmdexec_inx.cpp.s

body/msghander/msg.o: body/msghander/msg.cpp.o
.PHONY : body/msghander/msg.o

# target to build an object file
body/msghander/msg.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/body/msghander/msg.cpp.o
.PHONY : body/msghander/msg.cpp.o

body/msghander/msg.i: body/msghander/msg.cpp.i
.PHONY : body/msghander/msg.i

# target to preprocess a source file
body/msghander/msg.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/body/msghander/msg.cpp.i
.PHONY : body/msghander/msg.cpp.i

body/msghander/msg.s: body/msghander/msg.cpp.s
.PHONY : body/msghander/msg.s

# target to generate assembly for a file
body/msghander/msg.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/body/msghander/msg.cpp.s
.PHONY : body/msghander/msg.cpp.s

body/msghander/msghandler.o: body/msghander/msghandler.cpp.o
.PHONY : body/msghander/msghandler.o

# target to build an object file
body/msghander/msghandler.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/body/msghander/msghandler.cpp.o
.PHONY : body/msghander/msghandler.cpp.o

body/msghander/msghandler.i: body/msghander/msghandler.cpp.i
.PHONY : body/msghander/msghandler.i

# target to preprocess a source file
body/msghander/msghandler.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/body/msghander/msghandler.cpp.i
.PHONY : body/msghander/msghandler.cpp.i

body/msghander/msghandler.s: body/msghander/msghandler.cpp.s
.PHONY : body/msghander/msghandler.s

# target to generate assembly for a file
body/msghander/msghandler.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/body/msghander/msghandler.cpp.s
.PHONY : body/msghander/msghandler.cpp.s

body/msghander/queue.o: body/msghander/queue.cpp.o
.PHONY : body/msghander/queue.o

# target to build an object file
body/msghander/queue.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/body/msghander/queue.cpp.o
.PHONY : body/msghander/queue.cpp.o

body/msghander/queue.i: body/msghander/queue.cpp.i
.PHONY : body/msghander/queue.i

# target to preprocess a source file
body/msghander/queue.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/body/msghander/queue.cpp.i
.PHONY : body/msghander/queue.cpp.i

body/msghander/queue.s: body/msghander/queue.cpp.s
.PHONY : body/msghander/queue.s

# target to generate assembly for a file
body/msghander/queue.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/body/msghander/queue.cpp.s
.PHONY : body/msghander/queue.cpp.s

body/netio/netio.o: body/netio/netio.cpp.o
.PHONY : body/netio/netio.o

# target to build an object file
body/netio/netio.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/body/netio/netio.cpp.o
.PHONY : body/netio/netio.cpp.o

body/netio/netio.i: body/netio/netio.cpp.i
.PHONY : body/netio/netio.i

# target to preprocess a source file
body/netio/netio.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/body/netio/netio.cpp.i
.PHONY : body/netio/netio.cpp.i

body/netio/netio.s: body/netio/netio.cpp.s
.PHONY : body/netio/netio.s

# target to generate assembly for a file
body/netio/netio.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/body/netio/netio.cpp.s
.PHONY : body/netio/netio.cpp.s

main.o: main.cpp.o
.PHONY : main.o

# target to build an object file
main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/main.cpp.o
.PHONY : main.cpp.o

main.i: main.cpp.i
.PHONY : main.i

# target to preprocess a source file
main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/main.cpp.i
.PHONY : main.cpp.i

main.s: main.cpp.s
.PHONY : main.s

# target to generate assembly for a file
main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/beacon.dir/build.make CMakeFiles/beacon.dir/main.cpp.s
.PHONY : main.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... beacon"
	@echo "... base/crypto/crypto.o"
	@echo "... base/crypto/crypto.i"
	@echo "... base/crypto/crypto.s"
	@echo "... base/inx/inxutil.o"
	@echo "... base/inx/inxutil.i"
	@echo "... base/inx/inxutil.s"
	@echo "... base/log/log.o"
	@echo "... base/log/log.i"
	@echo "... base/log/log.s"
	@echo "... body/beacon/beacon.o"
	@echo "... body/beacon/beacon.i"
	@echo "... body/beacon/beacon.s"
	@echo "... body/cmdexec/cmdexec_inx.o"
	@echo "... body/cmdexec/cmdexec_inx.i"
	@echo "... body/cmdexec/cmdexec_inx.s"
	@echo "... body/msghander/msg.o"
	@echo "... body/msghander/msg.i"
	@echo "... body/msghander/msg.s"
	@echo "... body/msghander/msghandler.o"
	@echo "... body/msghander/msghandler.i"
	@echo "... body/msghander/msghandler.s"
	@echo "... body/msghander/queue.o"
	@echo "... body/msghander/queue.i"
	@echo "... body/msghander/queue.s"
	@echo "... body/netio/netio.o"
	@echo "... body/netio/netio.i"
	@echo "... body/netio/netio.s"
	@echo "... main.o"
	@echo "... main.i"
	@echo "... main.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

