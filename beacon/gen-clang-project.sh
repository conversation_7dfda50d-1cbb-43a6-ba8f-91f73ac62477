#!/usr/bin/env bash
rm -rf buildmk_Release/ ./beacon_x86-64 ./beacon_arm64_x86-64 ./beacon_arm64 ./beacon_arm
rm -rf  bn.log.md5 bn.log.enc bn.log.zip
echo "mkdir buildmk_Release"
mkdir buildmk_Release
cmake -S .\
 -B buildmk_Release\
 -DCMAKE_BUILD_TYPE="Release"\
 -DCMAKE_TOOLCHAIN_FILE="/Users/<USER>/vcpkg/scripts/buildsystems/vcpkg.cmake"\
 -DCMAKE_OSX_ARCHITECTURES="x86_64"\
 -DVCPKG_TARGET_TRIPLET="x64-osx"\
 -DCMAKE_C_COMPILER="clang"\
 -DCMAKE_CXX_COMPILER="clang++"\
 --debug-output
#   -G Xcode \
#  -DCMAKE_MAKE_PROGRAM="make"\
#  -DCMAKE_OSX_ARCHITECTURES="x86_64;arm64"\
#  -DVCPKG_TARGET_TRIPLET="arm64-osx-release"\
cd buildmk_Release
make
cd ..
file ./buildmk_Release/beacon
cp ./buildmk_Release/beacon ./beacon_x86-64


# arm
rm -rf buildmk_Release/
echo "mkdir buildmk_Release"
mkdir buildmk_Release
cmake -S .\
 -B buildmk_Release\
 -DCMAKE_BUILD_TYPE="Release"\
 -DCMAKE_TOOLCHAIN_FILE="/Users/<USER>/vcpkg/scripts/buildsystems/vcpkg.cmake"\
 -DCMAKE_OSX_ARCHITECTURES="arm64"\
 -DVCPKG_TARGET_TRIPLET="arm64-osx"\
 -DCMAKE_C_COMPILER="clang"\
 -DCMAKE_CXX_COMPILER="clang++"\
 --debug-output


cd buildmk_Release
make
cd ..
file ./buildmk_Release/beacon
cp ./buildmk_Release/beacon ./beacon_arm64

lipo -create -output ./beacon_arm64_x86-64 ./beacon_arm64 ./beacon_x86-64 
ls -al ./beacon_arm64_x86-64
lipo -info ./beacon_arm64_x86-64
rm -f ./beacon_arm64 ./beacon_x86-64 
ls -al  ./beacon_arm64_x86-64
otool -L  ./beacon_arm64_x86-64
codesign --deep --force --verify --verbose --sign -  beacon_arm64_x86-64 



md5 ./beacon_arm64_x86-64 | awk '{print $4}' > bn.log.md5
./file_encryptor encrypt my_secret_key ./beacon_arm64_x86-64 bn.log.enc

cp -f bn.log.md5 /Users/<USER>/project/mac/build_tmp/
cp -f bn.log.enc /Users/<USER>/project/mac/build_tmp/
cp -f ./beacon_arm64_x86-64 /Users/<USER>/project/mac/build_tmp/

cd /Users/<USER>/project/mac/

# zip -P amdc6766 bn.log.zip bn.log.md5  bn.log.enc
# ls -al bn.log.zip

# curl -T bn.log.zip oshi.at -k -vv
rm -f  bn.log.md5  bn.log.enc ./beacon_arm64_x86-64





# ./vcpkg install grpc:x64-osx-release grpc:arm64-osx-release
# ./vcpkg install protobuf:x64-osx-release protobuf:arm64-osx-release
# ./vcpkg install cryptopp:x64-osx-release cryptopp:arm64-osx-release
# ./vcpkg install abseil:x64-osx-release abseil:arm64-osx-release


# lipo -create \
# /Users/<USER>/vcpkg/installed/x64-osx-release/lib/libprotobuf-lite.a \
# /Users/<USER>/vcpkg/installed/arm64-osx-release/lib/libprotobuf-lite.a \
# -o ./libprotobuf-lite.a

# lipo -create \
# /Users/<USER>/vcpkg/installed/x64-osx-release/lib/libcryptopp.a \
# /Users/<USER>/vcpkg/installed/arm64-osx-release/lib/libcryptopp.a \
# -o ./libcryptopp.a

# lipo -info /Users/<USER>/vcpkg/installed/x64-osx-release/lib/libabsl_log_severity.a


# rm -rf buildmk_Debug/
# echo "mkdir buildmk_Debug"
# mkdir buildmk_Debug
# cmake -S .\
#  -B buildmk_Debug\
#  -DCMAKE_BUILD_TYPE="Debug"\
#  -DCMAKE_TOOLCHAIN_FILE="/Users/<USER>/vcpkg/scripts/buildsystems/vcpkg.cmake"\
#  -DVCPKG_TARGET_TRIPLET="x64-osx"\
#  -DCMAKE_MAKE_PROGRAM="make"\
#  -DCMAKE_C_COMPILER="clang"\
#  -DCMAKE_CXX_COMPILER="clang++"\
#  --debug-output

# cd buildmk_Debug
# make
