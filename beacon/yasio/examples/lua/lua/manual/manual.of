@Ci{$Id: manual.of $}
@C{[(-------------------------------------------------------------------------}
@manual{

@sect1{@title{Introduction}

Lua is a powerful, efficient, lightweight, embeddable scripting language.
It supports procedural programming,
object-oriented programming, functional programming,
data-driven programming, and data description.

<PERSON><PERSON> combines simple procedural syntax with powerful data description
constructs based on associative arrays and extensible semantics.
<PERSON><PERSON> is dynamically typed,
runs by interpreting bytecode with a register-based
virtual machine,
and has automatic memory management with
a generational garbage collection,
making it ideal for configuration, scripting,
and rapid prototyping.

Lua is implemented as a library, written in @emphx{clean C},
the common subset of @N{Standard C} and C++.
The Lua distribution includes a host program called @id{lua},
which uses the Lua library to offer a complete,
standalone Lua interpreter,
for interactive or batch use.
Lua is intended to be used both as a powerful, lightweight,
embeddable scripting language for any program that needs one,
and as a powerful but lightweight and efficient stand-alone language.

As an extension language, <PERSON><PERSON> has no notion of a @Q{main} program:
it works @emph{embedded} in a host client,
called the @emph{embedding program} or simply the @emphx{host}.
(Frequently, this host is the stand-alone @id{lua} program.)
The host program can invoke functions to execute a piece of Lua code,
can write and read Lua variables,
and can register @N{C functions} to be called by Lua code.
Through the use of @N{C functions}, Lua can be augmented to cope with
a wide range of different domains,
thus creating customized programming languages sharing a syntactical framework.

Lua is free software,
and is provided as usual with no guarantees,
as stated in its license.
The implementation described in this manual is available
at Lua's official web site, @id{www.lua.org}.

Like any other reference manual,
this document is dry in places.
For a discussion of the decisions behind the design of Lua,
see the technical papers available at Lua's web site.
For a detailed introduction to programming in Lua,
see Roberto's book, @emphx{Programming in Lua}.

}


@C{-------------------------------------------------------------------------}
@sect1{basic| @title{Basic Concepts}

@simplesect{

This section describes the basic concepts of the language.

}

@sect2{TypesSec| @title{Values and Types}

Lua is a dynamically typed language.
This means that
variables do not have types; only values do.
There are no type definitions in the language.
All values carry their own type.

All values in Lua are first-class values.
This means that all values can be stored in variables,
passed as arguments to other functions, and returned as results.

There are eight @x{basic types} in Lua:
@def{nil}, @def{boolean}, @def{number},
@def{string}, @def{function}, @def{userdata},
@def{thread}, and @def{table}.
The type @emph{nil} has one single value, @nil,
whose main property is to be different from any other value;
it often represents the absence of a useful value.
The type @emph{boolean} has two values, @false and @true.
Both @nil and @false make a condition false;
they are collectively called @def{false values}.
Any other value makes a condition true.

The type @emph{number} represents both
integer numbers and real (floating-point) numbers,
using two @x{subtypes}: @def{integer} and @def{float}.
Standard Lua uses 64-bit integers and double-precision (64-bit) floats,
but you can also compile Lua so that it
uses 32-bit integers and/or single-precision (32-bit) floats.
The option with 32 bits for both integers and floats
is particularly attractive
for small machines and embedded systems.
(See macro @id{LUA_32BITS} in file @id{luaconf.h}.)

Unless stated otherwise,
any overflow when manipulating integer values @def{wrap around},
according to the usual rules of two-complement arithmetic.
(In other words,
the actual result is the unique representable integer
that is equal modulo @M{2@sp{n}} to the mathematical result,
where @M{n} is the number of bits of the integer type.)

Lua has explicit rules about when each subtype is used,
but it also converts between them automatically as needed @see{coercion}.
Therefore,
the programmer may choose to mostly ignore the difference
between integers and floats
or to assume complete control over the representation of each number.

The type @emph{string} represents immutable sequences of bytes.
@index{eight-bit clean}
Lua is 8-bit clean:
strings can contain any 8-bit value,
including @x{embedded zeros} (@Char{\0}).
Lua is also encoding-agnostic;
it makes no assumptions about the contents of a string.
The length of any string in Lua must fit in a Lua integer.

Lua can call (and manipulate) functions written in Lua and
functions written in C @see{functioncall}.
Both are represented by the type @emph{function}.

The type @emph{userdata} is provided to allow arbitrary @N{C data} to
be stored in Lua variables.
A userdata value represents a block of raw memory.
There are two kinds of userdata:
@emphx{full userdata},
which is an object with a block of memory managed by Lua,
and @emphx{light userdata},
which is simply a @N{C pointer} value.
Userdata has no predefined operations in Lua,
except assignment and identity test.
By using @emph{metatables},
the programmer can define operations for full userdata values
@see{metatable}.
Userdata values cannot be created or modified in Lua,
only through the @N{C API}.
This guarantees the integrity of data owned by
the host program and @N{C libraries}.

The type @def{thread} represents independent threads of execution
and it is used to implement coroutines @see{coroutine}.
Lua threads are not related to operating-system threads.
Lua supports coroutines on all systems,
even those that do not support threads natively.

The type @emph{table} implements @x{associative arrays},
that is, @x{arrays} that can have as indices not only numbers,
but any Lua value except @nil and @x{NaN}.
(@emphx{Not a Number} is a special floating-point value
used by the @x{IEEE 754} standard to represent
undefined numerical results, such as @T{0/0}.)
Tables can be @emph{heterogeneous};
that is, they can contain values of all types (except @nil).
Any key associated to the value @nil is not considered part of the table.
Conversely, any key that is not part of a table has
an associated value @nil.

Tables are the sole data-structuring mechanism in Lua;
they can be used to represent ordinary arrays, lists,
symbol tables, sets, records, graphs, trees, etc.
To represent @x{records}, Lua uses the field name as an index.
The language supports this representation by
providing @id{a.name} as syntactic sugar for @T{a["name"]}.
There are several convenient ways to create tables in Lua
@see{tableconstructor}.

Like indices,
the values of table fields can be of any type.
In particular,
because functions are first-class values,
table fields can contain functions.
Thus tables can also carry @emph{methods} @see{func-def}.

The indexing of tables follows
the definition of raw equality in the language.
The expressions @T{a[i]} and @T{a[j]}
denote the same table element
if and only if @id{i} and @id{j} are raw equal
(that is, equal without metamethods).
In particular, floats with integral values
are equal to their respective integers
(e.g., @T{1.0 == 1}).
To avoid ambiguities,
any float used as a key that is equal to an integer
is converted to that integer.
For instance, if you write @T{a[2.0] = true},
the actual key inserted into the table will be the integer @T{2}.


Tables, functions, threads, and (full) userdata values are @emph{objects}:
variables do not actually @emph{contain} these values,
only @emph{references} to them.
Assignment, parameter passing, and function returns
always manipulate references to such values;
these operations do not imply any kind of copy.

The library function @Lid{type} returns a string describing the type
of a given value @seeF{type}.

}

@sect2{globalenv| @title{Environments and the Global Environment}

As we will discuss further in @refsec{variables} and @refsec{assignment},
any reference to a free name
(that is, a name not bound to any declaration) @id{var}
is syntactically translated to @T{_ENV.var}.
Moreover, every chunk is compiled in the scope of
an external local variable named @id{_ENV} @see{chunks},
so @id{_ENV} itself is never a free name in a chunk.

Despite the existence of this external @id{_ENV} variable and
the translation of free names,
@id{_ENV} is a completely regular name.
In particular,
you can define new variables and parameters with that name.
Each reference to a free name uses the @id{_ENV} that is
visible at that point in the program,
following the usual visibility rules of Lua @see{visibility}.

Any table used as the value of @id{_ENV} is called an @def{environment}.

Lua keeps a distinguished environment called the @def{global environment}.
This value is kept at a special index in the C registry @see{registry}.
In Lua, the global variable @Lid{_G} is initialized with this same value.
(@Lid{_G} is never used internally,
so changing its value will affect only your own code.)

When Lua loads a chunk,
the default value for its @id{_ENV} variable
is the global environment @seeF{load}.
Therefore, by default,
free names in Lua code refer to entries in the global environment
and, therefore, they are also called @def{global variables}.
Moreover, all standard libraries are loaded in the global environment
and some functions there operate on that environment.
You can use @Lid{load} (or @Lid{loadfile})
to load a chunk with a different environment.
(In C, you have to load the chunk and then change the value
of its first upvalue; see @See{lua_setupvalue}.)

}

@sect2{error| @title{Error Handling}

Several operations in Lua can @emph{raise} an error.
An error interrupts the normal flow of the program,
which can continue by @emph{catching} the error.

Lua code can explicitly raise an error by calling the
@Lid{error} function.
(This function never returns.)

To catch errors in Lua,
you can do a @def{protected call},
using @Lid{pcall} (or @Lid{xpcall}).
The function @Lid{pcall} calls a given function in @def{protected mode}.
Any error while running the function stops its execution,
and control returns immediately to @id{pcall},
which returns a status code.

Because Lua is an embedded extension language,
Lua code starts running by a call
from @N{C code} in the host program.
(When you use Lua standalone,
the @id{lua} application is the host program.)
Usually, this call is protected;
so, when an otherwise unprotected error occurs during
the compilation or execution of a Lua chunk,
control returns to the host,
which can take appropriate measures,
such as printing an error message.

Whenever there is an error,
an @def{error object}
is propagated with information about the error.
Lua itself only generates errors whose error object is a string,
but programs may generate errors with
any value as the error object.
It is up to the Lua program or its host to handle such error objects.
For historical reasons,
an error object is often called an @def{error message},
even though it does not have to be a string.


When you use @Lid{xpcall} (or @Lid{lua_pcall}, in C)
you may give a @def{message handler}
to be called in case of errors.
This function is called with the original error object
and returns a new error object.
It is called before the error unwinds the stack,
so that it can gather more information about the error,
for instance by inspecting the stack and creating a stack traceback.
This message handler is still protected by the protected call;
so, an error inside the message handler
will call the message handler again.
If this loop goes on for too long,
Lua breaks it and returns an appropriate message.
The message handler is called only for regular runtime errors.
It is not called for memory-allocation errors
nor for errors while running finalizers or other message handlers.

Lua also offers a system of @emph{warnings} @seeF{warn}.
Unlike errors, warnings do not interfere
in any way with program execution.
They typically only generate a message to the user,
although this behavior can be adapted from C @seeC{lua_setwarnf}.

}

@sect2{metatable| @title{Metatables and Metamethods}

Every value in Lua can have a @emph{metatable}.
This @def{metatable} is an ordinary Lua table
that defines the behavior of the original value
under certain events.
You can change several aspects of the behavior
of a value by setting specific fields in its metatable.
For instance, when a non-numeric value is the operand of an addition,
Lua checks for a function in the field @St{__add} of the value's metatable.
If it finds one,
Lua calls this function to perform the addition.

The key for each event in a metatable is a string
with the event name prefixed by two underscores;
the corresponding value is called a @def{metavalue}.
For most events, the metavalue must be a function,
which is then called a @def{metamethod}.
In the previous example, the key is the string @St{__add}
and the metamethod is the function that performs the addition.
Unless stated otherwise,
a metamethod may in fact be any @x{callable value},
which is either a function or a value with a @id{__call} metamethod.

You can query the metatable of any value
using the @Lid{getmetatable} function.
Lua queries metamethods in metatables using a raw access @seeF{rawget}.

You can replace the metatable of tables
using the @Lid{setmetatable} function.
You cannot change the metatable of other types from Lua code,
except by using the @link{debuglib|debug library}.

Tables and full userdata have individual metatables,
although multiple tables and userdata can share their metatables.
Values of all other types share one single metatable per type;
that is, there is one single metatable for all numbers,
one for all strings, etc.
By default, a value has no metatable,
but the string library sets a metatable for the string type @see{strlib}.

A detailed list of operations controlled by metatables is given next.
Each event is identified by its corresponding key.
By convention, all metatable keys used by Lua are composed by
two underscores followed by lowercase Latin letters.

@description{

@item{@idx{__add}|
the addition (@T{+}) operation.
If any operand for an addition is not a number,
Lua will try to call a metamethod.
It starts by checking the first operand (even if it is a number);
if that operand does not define a metamethod for @idx{__add},
then Lua will check the second operand.
If Lua can find a metamethod,
it calls the metamethod with the two operands as arguments,
and the result of the call
(adjusted to one value)
is the result of the operation.
Otherwise, if no metamethod is found,
Lua raises an error.
}

@item{@idx{__sub}|
the subtraction (@T{-}) operation.
Behavior similar to the addition operation.
}

@item{@idx{__mul}|
the multiplication (@T{*}) operation.
Behavior similar to the addition operation.
}

@item{@idx{__div}|
the division (@T{/}) operation.
Behavior similar to the addition operation.
}

@item{@idx{__mod}|
the modulo (@T{%}) operation.
Behavior similar to the addition operation.
}

@item{@idx{__pow}|
the exponentiation (@T{^}) operation.
Behavior similar to the addition operation.
}

@item{@idx{__unm}|
the negation (unary @T{-}) operation.
Behavior similar to the addition operation.
}

@item{@idx{__idiv}|
the floor division (@T{//}) operation.
Behavior similar to the addition operation.
}

@item{@idx{__band}|
the bitwise AND (@T{&}) operation.
Behavior similar to the addition operation,
except that Lua will try a metamethod
if any operand is neither an integer
nor a float coercible to an integer @see{coercion}.
}

@item{@idx{__bor}|
the bitwise OR (@T{|}) operation.
Behavior similar to the bitwise AND operation.
}

@item{@idx{__bxor}|
the bitwise exclusive OR (binary @T{~}) operation.
Behavior similar to the bitwise AND operation.
}

@item{@idx{__bnot}|
the bitwise NOT (unary @T{~}) operation.
Behavior similar to the bitwise AND operation.
}

@item{@idx{__shl}|
the bitwise left shift (@T{<<}) operation.
Behavior similar to the bitwise AND operation.
}

@item{@idx{__shr}|
the bitwise right shift (@T{>>}) operation.
Behavior similar to the bitwise AND operation.
}

@item{@idx{__concat}|
the concatenation (@T{..}) operation.
Behavior similar to the addition operation,
except that Lua will try a metamethod
if any operand is neither a string nor a number
(which is always coercible to a string).
}

@item{@idx{__len}|
the length (@T{#}) operation.
If the object is not a string,
Lua will try its metamethod.
If there is a metamethod,
Lua calls it with the object as argument,
and the result of the call
(always adjusted to one value)
is the result of the operation.
If there is no metamethod but the object is a table,
then Lua uses the table length operation @see{len-op}.
Otherwise, Lua raises an error.
}

@item{@idx{__eq}|
the equal (@T{==}) operation.
Behavior similar to the addition operation,
except that Lua will try a metamethod only when the values
being compared are either both tables or both full userdata
and they are not primitively equal.
The result of the call is always converted to a boolean.
}

@item{@idx{__lt}|
the less than (@T{<}) operation.
Behavior similar to the addition operation,
except that Lua will try a metamethod only when the values
being compared are neither both numbers nor both strings.
Moreover, the result of the call is always converted to a boolean.
}

@item{@idx{__le}|
the less equal (@T{<=}) operation.
Behavior similar to the less than operation.
}

@item{@idx{__index}|
The indexing access operation @T{table[key]}.
This event happens when @id{table} is not a table or
when @id{key} is not present in @id{table}.
The metavalue is looked up in the metatable of @id{table}.

The metavalue for this event can be either a function, a table,
or any value with an @id{__index} metavalue.
If it is a function,
it is called with @id{table} and @id{key} as arguments,
and the result of the call
(adjusted to one value)
is the result of the operation.
Otherwise,
the final result is the result of indexing this metavalue with @id{key}.
This indexing is regular, not raw,
and therefore can trigger another @id{__index} metavalue.
}

@item{@idx{__newindex}|
The indexing assignment @T{table[key] = value}.
Like the index event,
this event happens when @id{table} is not a table or
when @id{key} is not present in @id{table}.
The metavalue is looked up in the metatable of @id{table}.

Like with indexing,
the metavalue for this event can be either a function, a table,
or any value with an @id{__newindex} metavalue.
If it is a function,
it is called with @id{table}, @id{key}, and @id{value} as arguments.
Otherwise,
Lua repeats the indexing assignment over this metavalue
with the same key and value.
This assignment is regular, not raw,
and therefore can trigger another @id{__newindex} metavalue.

Whenever a @idx{__newindex} metavalue is invoked,
Lua does not perform the primitive assignment.
If needed,
the metamethod itself can call @Lid{rawset}
to do the assignment.
}

@item{@idx{__call}|
The call operation @T{func(args)}.
This event happens when Lua tries to call a non-function value
(that is, @id{func} is not a function).
The metamethod is looked up in @id{func}.
If present,
the metamethod is called with @id{func} as its first argument,
followed by the arguments of the original call (@id{args}).
All results of the call
are the results of the operation.
This is the only metamethod that allows multiple results.
}

}

In addition to the previous list,
the interpreter also respects the following keys in metatables:
@idx{__gc} @see{finalizers},
@idx{__close} @see{to-be-closed},
@idx{__mode} @see{weak-table},
and @idx{__name}.
(The entry @idx{__name},
when it contains a string,
may be used by @Lid{tostring} and in error messages.)

For the unary operators (negation, length, and bitwise NOT),
the metamethod is computed and called with a dummy second operand,
equal to the first one.
This extra operand is only to simplify Lua's internals
(by making these operators behave like a binary operation)
and may be removed in future versions.
For most uses this extra operand is irrelevant.

Because metatables are regular tables,
they can contain arbitrary fields,
not only the event names defined above.
Some functions in the standard library
(e.g., @Lid{tostring})
use other fields in metatables for their own purposes.

It is a good practice to add all needed metamethods to a table
before setting it as a metatable of some object.
In particular, the @idx{__gc} metamethod works only when this order
is followed @see{finalizers}.
It is also a good practice to set the metatable of an object
right after its creation.

}

@sect2{GC| @title{Garbage Collection}

@simplesect{

Lua performs automatic memory management.
This means that
you do not have to worry about allocating memory for new objects
or freeing it when the objects are no longer needed.
Lua manages memory automatically by running
a @def{garbage collector} to collect all @emph{dead} objects.
All memory used by Lua is subject to automatic management:
strings, tables, userdata, functions, threads, internal structures, etc.

An object is considered @def{dead}
as soon as the collector can be sure the object
will not be accessed again in the normal execution of the program.
(@Q{Normal execution} here excludes finalizers,
which can resurrect dead objects @see{finalizers},
and excludes also operations using the debug library.)
Note that the time when the collector can be sure that an object
is dead may not coincide with the programmer's expectations.
The only guarantees are that Lua will not collect an object
that may still be accessed in the normal execution of the program,
and it will eventually collect an object
that is inaccessible from Lua.
(Here,
@emph{inaccessible from Lua} means that neither a variable nor
another live object refer to the object.)
Because Lua has no knowledge about @N{C code},
it never collects objects accessible through the registry @see{registry},
which includes the global environment @see{globalenv}.


The garbage collector (GC) in Lua can work in two modes:
incremental and generational.

The default GC mode with the default parameters
are adequate for most uses.
However, programs that waste a large proportion of their time
allocating and freeing memory can benefit from other settings.
Keep in mind that the GC behavior is non-portable
both across platforms and across different Lua releases;
therefore, optimal settings are also non-portable.

You can change the GC mode and parameters by calling
@Lid{lua_gc} @N{in C}
or @Lid{collectgarbage} in Lua.
You can also use these functions to control
the collector directly (e.g., to stop and restart it).

}

@sect3{incmode| @title{Incremental Garbage Collection}

In incremental mode,
each GC cycle performs a mark-and-sweep collection in small steps
interleaved with the program's execution.
In this mode,
the collector uses three numbers to control its garbage-collection cycles:
the @def{garbage-collector pause},
the @def{garbage-collector step multiplier},
and the @def{garbage-collector step size}.

The garbage-collector pause
controls how long the collector waits before starting a new cycle.
The collector starts a new cycle when the use of memory
hits @M{n%} of the use after the previous collection.
Larger values make the collector less aggressive.
Values equal to or less than 100 mean the collector will not wait to
start a new cycle.
A value of 200 means that the collector waits for the total memory in use
to double before starting a new cycle.
The default value is 200; the maximum value is 1000.

The garbage-collector step multiplier
controls the speed of the collector relative to
memory allocation,
that is,
how many elements it marks or sweeps for each
kilobyte of memory allocated.
Larger values make the collector more aggressive but also increase
the size of each incremental step.
You should not use values less than 100,
because they make the collector too slow and
can result in the collector never finishing a cycle.
The default value is 100;  the maximum value is 1000.

The garbage-collector step size controls the
size of each incremental step,
specifically how many bytes the interpreter allocates
before performing a step.
This parameter is logarithmic:
A value of @M{n} means the interpreter will allocate @M{2@sp{n}}
bytes between steps and perform equivalent work during the step.
A large value (e.g., 60) makes the collector a stop-the-world
(non-incremental) collector.
The default value is 13,
which means steps of approximately @N{8 Kbytes}.

}

@sect3{genmode| @title{Generational Garbage Collection}

In generational mode,
the collector does frequent @emph{minor} collections,
which traverses only objects recently created.
If after a minor collection the use of memory is still above a limit,
the collector does a stop-the-world @emph{major} collection,
which traverses all objects.
The generational mode uses two parameters:
the @def{minor multiplier} and the @def{the major multiplier}.

The minor multiplier controls the frequency of minor collections.
For a minor multiplier @M{x},
a new minor collection will be done when memory
grows @M{x%} larger than the memory in use after the previous major
collection.
For instance, for a multiplier of 20,
the collector will do a minor collection when the use of memory
gets 20% larger than the use after the previous major collection.
The default value is 20; the maximum value is 200.

The major multiplier controls the frequency of major collections.
For a major multiplier @M{x},
a new major collection will be done when memory
grows @M{x%} larger than the memory in use after the previous major
collection.
For instance, for a multiplier of 100,
the collector will do a major collection when the use of memory
gets larger than twice the use after the previous collection.
The default value is 100; the maximum value is 1000.

}

@sect3{finalizers| @title{Garbage-Collection Metamethods}

You can set garbage-collector metamethods for tables
and, using the @N{C API},
for full userdata @see{metatable}.
These metamethods, called @def{finalizers},
are called when the garbage collector detects that the
corresponding table or userdata is dead.
Finalizers allow you to coordinate Lua's garbage collection
with external resource management such as closing files,
network or database connections,
or freeing your own memory.

For an object (table or userdata) to be finalized when collected,
you must @emph{mark} it for finalization.
@index{mark (for finalization)}
You mark an object for finalization when you set its metatable
and the metatable has a field indexed by the string @St{__gc}.
Note that if you set a metatable without a @idx{__gc} field
and later create that field in the metatable,
the object will not be marked for finalization.

When a marked object becomes dead,
it is not collected immediately by the garbage collector.
Instead, Lua puts it in a list.
After the collection,
Lua goes through that list.
For each object in the list,
it checks the object's @idx{__gc} metamethod:
If it is present,
Lua calls it with the object as its single argument.

At the end of each garbage-collection cycle,
the finalizers are called in
the reverse order that the objects were marked for finalization,
among those collected in that cycle;
that is, the first finalizer to be called is the one associated
with the object marked last in the program.
The execution of each finalizer may occur at any point during
the execution of the regular code.

Because the object being collected must still be used by the finalizer,
that object (and other objects accessible only through it)
must be @emph{resurrected} by Lua.@index{resurrection}
Usually, this resurrection is transient,
and the object memory is freed in the next garbage-collection cycle.
However, if the finalizer stores the object in some global place
(e.g., a global variable),
then the resurrection is permanent.
Moreover, if the finalizer marks a finalizing object for finalization again,
its finalizer will be called again in the next cycle where the
object is dead.
In any case,
the object memory is freed only in a GC cycle where
the object is dead and not marked for finalization.

When you close a state @seeF{lua_close},
Lua calls the finalizers of all objects marked for finalization,
following the reverse order that they were marked.
If any finalizer marks objects for collection during that phase,
these marks have no effect.

Finalizers cannot yield.
Except for that, they can do anything,
such as raise errors, create new objects,
or even run the garbage collector.
However, because they can run in unpredictable times,
it is good practice to restrict each finalizer
to the minimum necessary to properly release
its associated resource.

Any error while running a finalizer generates a warning;
the error is not propagated.

}

@sect3{weak-table| @title{Weak Tables}

A @def{weak table} is a table whose elements are
@def{weak references}.
A weak reference is ignored by the garbage collector.
In other words,
if the only references to an object are weak references,
then the garbage collector will collect that object.

A weak table can have weak keys, weak values, or both.
A table with weak values allows the collection of its values,
but prevents the collection of its keys.
A table with both weak keys and weak values allows the collection of
both keys and values.
In any case, if either the key or the value is collected,
the whole pair is removed from the table.
The weakness of a table is controlled by the
@idx{__mode} field of its metatable.
This metavalue, if present, must be one of the following strings:
@St{k}, for a table with weak keys;
@St{v}, for a table with weak values;
or @St{kv}, for a table with both weak keys and values.

A table with weak keys and strong values
is also called an @def{ephemeron table}.
In an ephemeron table,
a value is considered reachable only if its key is reachable.
In particular,
if the only reference to a key comes through its value,
the pair is removed.

Any change in the weakness of a table may take effect only
at the next collect cycle.
In particular, if you change the weakness to a stronger mode,
Lua may still collect some items from that table
before the change takes effect.

Only objects that have an explicit construction
are removed from weak tables.
Values, such as numbers and @x{light @N{C functions}},
are not subject to garbage collection,
and therefore are not removed from weak tables
(unless their associated values are collected).
Although strings are subject to garbage collection,
they do not have an explicit construction and
their equality is by value;
they behave more like values than like objects.
Therefore, they are not removed from weak tables.

Resurrected objects
(that is, objects being finalized
and objects accessible only through objects being finalized)
have a special behavior in weak tables.
They are removed from weak values before running their finalizers,
but are removed from weak keys only in the next collection
after running their finalizers, when such objects are actually freed.
This behavior allows the finalizer to access properties
associated with the object through weak tables.

If a weak table is among the resurrected objects in a collection cycle,
it may not be properly cleared until the next cycle.

}

}

@sect2{coroutine| @title{Coroutines}

Lua supports coroutines,
also called @emphx{collaborative multithreading}.
A coroutine in Lua represents an independent thread of execution.
Unlike threads in multithread systems, however,
a coroutine only suspends its execution by explicitly calling
a yield function.

You create a coroutine by calling @Lid{coroutine.create}.
Its sole argument is a function
that is the main function of the coroutine.
The @id{create} function only creates a new coroutine and
returns a handle to it (an object of type @emph{thread});
it does not start the coroutine.

You execute a coroutine by calling @Lid{coroutine.resume}.
When you first call @Lid{coroutine.resume},
passing as its first argument
a thread returned by @Lid{coroutine.create},
the coroutine starts its execution by
calling its main function.
Extra arguments passed to @Lid{coroutine.resume} are passed
as arguments to that function.
After the coroutine starts running,
it runs until it terminates or @emph{yields}.

A coroutine can terminate its execution in two ways:
normally, when its main function returns
(explicitly or implicitly, after the last instruction);
and abnormally, if there is an unprotected error.
In case of normal termination,
@Lid{coroutine.resume} returns @true,
plus any values returned by the coroutine main function.
In case of errors, @Lid{coroutine.resume} returns @false
plus the error object.
In this case, the coroutine does not unwind its stack,
so that it is possible to inspect it after the error
with the debug API.

A coroutine yields by calling @Lid{coroutine.yield}.
When a coroutine yields,
the corresponding @Lid{coroutine.resume} returns immediately,
even if the yield happens inside nested function calls
(that is, not in the main function,
but in a function directly or indirectly called by the main function).
In the case of a yield, @Lid{coroutine.resume} also returns @true,
plus any values passed to @Lid{coroutine.yield}.
The next time you resume the same coroutine,
it continues its execution from the point where it yielded,
with the call to @Lid{coroutine.yield} returning any extra
arguments passed to @Lid{coroutine.resume}.

Like @Lid{coroutine.create},
the @Lid{coroutine.wrap} function also creates a coroutine,
but instead of returning the coroutine itself,
it returns a function that, when called, resumes the coroutine.
Any arguments passed to this function
go as extra arguments to @Lid{coroutine.resume}.
@Lid{coroutine.wrap} returns all the values returned by @Lid{coroutine.resume},
except the first one (the boolean error code).
Unlike @Lid{coroutine.resume},
the function created by @Lid{coroutine.wrap}
propagates any error to the caller.
In this case,
the function also closes the coroutine @seeF{coroutine.close}.

As an example of how coroutines work,
consider the following code:
@verbatim{
function foo (a)
  print("foo", a)
  return coroutine.yield(2*a)
end

co = coroutine.create(function (a,b)
      print("co-body", a, b)
      local r = foo(a+1)
      print("co-body", r)
      local r, s = coroutine.yield(a+b, a-b)
      print("co-body", r, s)
      return b, "end"
end)

print("main", coroutine.resume(co, 1, 10))
print("main", coroutine.resume(co, "r"))
print("main", coroutine.resume(co, "x", "y"))
print("main", coroutine.resume(co, "x", "y"))
}
When you run it, it produces the following output:
@verbatim{
co-body 1       10
foo     2
main    true    4
co-body r
main    true    11      -9
co-body x       y
main    true    10      end
main    false   cannot resume dead coroutine
}

You can also create and manipulate coroutines through the C API:
see functions @Lid{lua_newthread}, @Lid{lua_resume},
and @Lid{lua_yield}.

}

}


@C{-------------------------------------------------------------------------}
@sect1{language| @title{The Language}

@simplesect{

This section describes the lexis, the syntax, and the semantics of Lua.
In other words,
this section describes
which tokens are valid,
how they can be combined,
and what their combinations mean.

Language constructs will be explained using the usual extended BNF notation,
in which
@N{@bnfrep{@rep{a}} means 0} or more @rep{a}'s, and
@N{@bnfopt{@rep{a}} means} an optional @rep{a}.
Non-terminals are shown like @bnfNter{non-terminal},
keywords are shown like @rw{kword},
and other terminal symbols are shown like @bnfter{=}.
The complete syntax of Lua can be found in @refsec{BNF}
at the end of this manual.

}

@sect2{lexical| @title{Lexical Conventions}

Lua is a @x{free-form} language.
It ignores spaces and comments between lexical elements (@x{tokens}),
except as delimiters between two tokens.
In source code,
Lua recognizes as spaces the standard ASCII whitespace
characters space, form feed, newline,
carriage return, horizontal tab, and vertical tab.

@def{Names}
(also called @def{identifiers})
in Lua can be any string of Latin letters,
Arabic-Indic digits, and underscores,
not beginning with a digit and
not being a reserved word.
Identifiers are used to name variables, table fields, and labels.

The following @def{keywords} are reserved
and cannot be used as names:
@index{reserved words}
@verbatim{
and       break     do        else      elseif    end
false     for       function  goto      if        in
local     nil       not       or        repeat    return
then      true      until     while
}

Lua is a case-sensitive language:
@id{and} is a reserved word, but @id{And} and @id{AND}
are two different, valid names.
As a convention,
programs should avoid creating
names that start with an underscore followed by
one or more uppercase letters (such as @Lid{_VERSION}).

The following strings denote other @x{tokens}:
@verbatim{
+     -     *     /     %     ^     #
&     ~     |     <<    >>    //
==    ~=    <=    >=    <     >     =
(     )     {     }     [     ]     ::
;     :     ,     .     ..    ...
}

A @def{short literal string}
can be delimited by matching single or double quotes,
and can contain the following C-like escape sequences:
@Char{\a} (bell),
@Char{\b} (backspace),
@Char{\f} (form feed),
@Char{\n} (newline),
@Char{\r} (carriage return),
@Char{\t} (horizontal tab),
@Char{\v} (vertical tab),
@Char{\\} (backslash),
@Char{\"} (quotation mark [double quote]),
and @Char{\'} (apostrophe [single quote]).
A backslash followed by a line break
results in a newline in the string.
The escape sequence @Char{\z} skips the following span
of whitespace characters,
including line breaks;
it is particularly useful to break and indent a long literal string
into multiple lines without adding the newlines and spaces
into the string contents.
A short literal string cannot contain unescaped line breaks
nor escapes not forming a valid escape sequence.

We can specify any byte in a short literal string,
including @x{embedded zeros},
by its numeric value.
This can be done
with the escape sequence @T{\x@rep{XX}},
where @rep{XX} is a sequence of exactly two hexadecimal digits,
or with the escape sequence @T{\@rep{ddd}},
where @rep{ddd} is a sequence of up to three decimal digits.
(Note that if a decimal escape sequence is to be followed by a digit,
it must be expressed using exactly three digits.)

The @x{UTF-8} encoding of a @x{Unicode} character
can be inserted in a literal string with
the escape sequence @T{\u{@rep{XXX}}}
(with mandatory enclosing braces),
where @rep{XXX} is a sequence of one or more hexadecimal digits
representing the character code point.
This code point can be any value less than @M{2@sp{31}}.
(Lua uses the original UTF-8 specification here,
which is not restricted to valid Unicode code points.)

Literal strings can also be defined using a long format
enclosed by @def{long brackets}.
We define an @def{opening long bracket of level @rep{n}} as an opening
square bracket followed by @rep{n} equal signs followed by another
opening square bracket.
So, an opening long bracket of @N{level 0} is written as @T{[[}, @C{]]}
an opening long bracket of @N{level 1} is written as @T{[=[}, @C{]]}
and so on.
A @emph{closing long bracket} is defined similarly;
for instance,
a closing long bracket of @N{level 4} is written as @C{[[} @T{]====]}.
A @def{long literal} starts with an opening long bracket of any level and
ends at the first closing long bracket of the same level.
It can contain any text except a closing bracket of the same level.
Literals in this bracketed form can run for several lines,
do not interpret any escape sequences,
and ignore long brackets of any other level.
Any kind of end-of-line sequence
(carriage return, newline, carriage return followed by newline,
or newline followed by carriage return)
is converted to a simple newline.
When the opening long bracket is immediately followed by a newline,
the newline is not included in the string.

As an example, in a system using ASCII
(in which @Char{a} is coded @N{as 97},
newline is coded @N{as 10}, and @Char{1} is coded @N{as 49}),
the five literal strings below denote the same string:
@verbatim{
a = 'alo\n123"'
a = "alo\n123\""
a = '\97lo\10\04923"'
a = [[alo
123"]]
a = [==[
alo
123"]==]
}

Any byte in a literal string not
explicitly affected by the previous rules represents itself.
However, Lua opens files for parsing in text mode,
and the system's file functions may have problems with
some control characters.
So, it is safer to represent
binary data as a quoted literal with
explicit escape sequences for the non-text characters.

A @def{numeric constant} (or @def{numeral})
can be written with an optional fractional part
and an optional decimal exponent,
marked by a letter @Char{e} or @Char{E}.
Lua also accepts @x{hexadecimal constants},
which start with @T{0x} or @T{0X}.
Hexadecimal constants also accept an optional fractional part
plus an optional binary exponent,
marked by a letter @Char{p} or @Char{P}.

A numeric constant with a radix point or an exponent
denotes a float;
otherwise,
if its value fits in an integer or it is a hexadecimal constant,
it denotes an integer;
otherwise (that is, a decimal integer numeral that overflows),
it denotes a float.
Hexadecimal numerals with neither a radix point nor an exponent
always denote an integer value;
if the value overflows, it @emph{wraps around}
to fit into a valid integer.

Examples of valid integer constants are
@verbatim{
3   345   0xff   0xBEBADA
}
Examples of valid float constants are
@verbatim{
3.0     3.1416     314.16e-2     0.31416E1     34e1
0x0.1E  0xA23p-4   0X1.921FB54442D18P+1
}

A @def{comment} starts with a double hyphen (@T{--})
anywhere outside a string.
If the text immediately after @T{--} is not an opening long bracket,
the comment is a @def{short comment},
which runs until the end of the line.
Otherwise, it is a @def{long comment},
which runs until the corresponding closing long bracket.

}

@sect2{variables| @title{Variables}

Variables are places that store values.
There are three kinds of variables in Lua:
global variables, local variables, and table fields.

A single name can denote a global variable or a local variable
(or a function's formal parameter,
which is a particular kind of local variable):
@Produc{
@producname{var}@producbody{@bnfNter{Name}}
}
@bnfNter{Name} denotes identifiers @see{lexical}.

Any variable name is assumed to be global unless explicitly declared
as a local @see{localvar}.
@x{Local variables} are @emph{lexically scoped}:
local variables can be freely accessed by functions
defined inside their scope @see{visibility}.

Before the first assignment to a variable, its value is @nil.

Square brackets are used to index a table:
@Produc{
@producname{var}@producbody{prefixexp @bnfter{[} exp @bnfter{]}}
}
The meaning of accesses to table fields can be changed via metatables
@see{metatable}.

The syntax @id{var.Name} is just syntactic sugar for
@T{var["Name"]}:
@Produc{
@producname{var}@producbody{prefixexp @bnfter{.} @bnfNter{Name}}
}

An access to a global variable @id{x}
is equivalent to @id{_ENV.x}.
Due to the way that chunks are compiled,
the variable @id{_ENV} itself is never global @see{globalenv}.

}

@sect2{stats| @title{Statements}

@simplesect{

Lua supports an almost conventional set of @x{statements},
similar to those in other conventional languages.
This set includes
blocks, assignments, control structures, function calls,
and variable declarations.

}

@sect3{@title{Blocks}

A @x{block} is a list of statements,
which are executed sequentially:
@Produc{
@producname{block}@producbody{@bnfrep{stat}}
}
Lua has @def{empty statements}
that allow you to separate statements with semicolons,
start a block with a semicolon
or write two semicolons in sequence:
@Produc{
@producname{stat}@producbody{@bnfter{;}}
}

Both function calls and assignments
can start with an open parenthesis.
This possibility leads to an ambiguity in Lua's grammar.
Consider the following fragment:
@verbatim{
a = b + c
(print or io.write)('done')
}
The grammar could see this fragment in two ways:
@verbatim{
a = b + c(print or io.write)('done')

a = b + c; (print or io.write)('done')
}
The current parser always sees such constructions
in the first way,
interpreting the open parenthesis
as the start of the arguments to a call.
To avoid this ambiguity,
it is a good practice to always precede with a semicolon
statements that start with a parenthesis:
@verbatim{
;(print or io.write)('done')
}

A block can be explicitly delimited to produce a single statement:
@Produc{
@producname{stat}@producbody{@Rw{do} block @Rw{end}}
}
Explicit blocks are useful
to control the scope of variable declarations.
Explicit blocks are also sometimes used to
add a @Rw{return} statement in the middle
of another block @see{control}.

}

@sect3{chunks| @title{Chunks}

The unit of compilation of Lua is called a @def{chunk}.
Syntactically,
a chunk is simply a block:
@Produc{
@producname{chunk}@producbody{block}
}

Lua handles a chunk as the body of an anonymous function
with a variable number of arguments
@see{func-def}.
As such, chunks can define local variables,
receive arguments, and return values.
Moreover, such anonymous function is compiled as in the
scope of an external local variable called @id{_ENV} @see{globalenv}.
The resulting function always has @id{_ENV} as its only external variable,
even if it does not use that variable.

A chunk can be stored in a file or in a string inside the host program.
To execute a chunk,
Lua first @emph{loads} it,
precompiling the chunk's code into instructions for a virtual machine,
and then Lua executes the compiled code
with an interpreter for the virtual machine.

Chunks can also be precompiled into binary form;
see the program @idx{luac} and the function @Lid{string.dump} for details.
Programs in source and compiled forms are interchangeable;
Lua automatically detects the file type and acts accordingly @seeF{load}.

}

@sect3{assignment| @title{Assignment}

Lua allows @x{multiple assignments}.
Therefore, the syntax for assignment
defines a list of variables on the left side
and a list of expressions on the right side.
The elements in both lists are separated by commas:
@Produc{
@producname{stat}@producbody{varlist @bnfter{=} explist}
@producname{varlist}@producbody{var @bnfrep{@bnfter{,} var}}
@producname{explist}@producbody{exp @bnfrep{@bnfter{,} exp}}
}
Expressions are discussed in @See{expressions}.

Before the assignment,
the list of values is @emph{adjusted} to the length of
the list of variables.@index{adjustment}
If there are more values than needed,
the excess values are thrown away.
If there are fewer values than needed,
the list is extended with @nil's.
If the list of expressions ends with a function call,
then all values returned by that call enter the list of values,
before the adjustment
(except when the call is enclosed in parentheses; see @See{expressions}).

The assignment statement first evaluates all its expressions
and only then the assignments are performed.
Thus the code
@verbatim{
i = 3
i, a[i] = i+1, 20
}
sets @T{a[3]} to 20, without affecting @T{a[4]}
because the @id{i} in @T{a[i]} is evaluated (to 3)
before it is @N{assigned 4}.
Similarly, the line
@verbatim{
x, y = y, x
}
exchanges the values of @id{x} and @id{y},
and
@verbatim{
x, y, z = y, z, x
}
cyclically permutes the values of @id{x}, @id{y}, and @id{z}.

An assignment to a global name @T{x = val}
is equivalent to the assignment
@T{_ENV.x = val} @see{globalenv}.

The meaning of assignments to table fields and
global variables (which are actually table fields, too)
can be changed via metatables @see{metatable}.

}

@sect3{control| @title{Control Structures}
The control structures
@Rw{if}, @Rw{while}, and @Rw{repeat} have the usual meaning and
familiar syntax:
@index{while-do statement}
@index{repeat-until statement}
@index{if-then-else statement}
@Produc{
@producname{stat}@producbody{@Rw{while} exp @Rw{do} block @Rw{end}}
@producname{stat}@producbody{@Rw{repeat} block @Rw{until} exp}
@producname{stat}@producbody{@Rw{if} exp @Rw{then} block
  @bnfrep{@Rw{elseif} exp @Rw{then} block}
   @bnfopt{@Rw{else} block} @Rw{end}}
}
Lua also has a @Rw{for} statement, in two flavors @see{for}.

The @x{condition expression} of a
control structure can return any value.
Both @false and @nil test false.
All values different from @nil and @false test true.
In particular, the number 0 and the empty string also test true.

In the @Rw{repeat}@En@Rw{until} loop,
the inner block does not end at the @Rw{until} keyword,
but only after the condition.
So, the condition can refer to local variables
declared inside the loop block.

The @Rw{goto} statement transfers the program control to a label.
For syntactical reasons,
labels in Lua are considered statements too:
@index{goto statement}
@index{label}
@Produc{
@producname{stat}@producbody{@Rw{goto} Name}
@producname{stat}@producbody{label}
@producname{label}@producbody{@bnfter{::} Name @bnfter{::}}
}

A label is visible in the entire block where it is defined,
except inside nested functions.
A goto may jump to any visible label as long as it does not
enter into the scope of a local variable.
A label should not be declared
where a label with the same name is visible,
even if this other label has been declared in an enclosing block.

Labels and empty statements are called @def{void statements},
as they perform no actions.

The @Rw{break} statement terminates the execution of a
@Rw{while}, @Rw{repeat}, or @Rw{for} loop,
skipping to the next statement after the loop:
@index{break statement}
@Produc{
@producname{stat}@producbody{@Rw{break}}
}
A @Rw{break} ends the innermost enclosing loop.

The @Rw{return} statement is used to return values
from a function or a chunk
(which is handled as an anonymous function).
@index{return statement}
Functions can return more than one value,
so the syntax for the @Rw{return} statement is
@Produc{
@producname{stat}@producbody{@Rw{return} @bnfopt{explist} @bnfopt{@bnfter{;}}}
}

The @Rw{return} statement can only be written
as the last statement of a block.
If it is necessary to @Rw{return} in the middle of a block,
then an explicit inner block can be used,
as in the idiom @T{do return end},
because now @Rw{return} is the last statement in its (inner) block.

}

@sect3{for| @title{For Statement}

@index{for statement}
The @Rw{for} statement has two forms:
one numerical and one generic.

@sect4{@title{The numerical @Rw{for} loop}

The numerical @Rw{for} loop repeats a block of code while a
control variable goes through an arithmetic progression.
It has the following syntax:
@Produc{
@producname{stat}@producbody{@Rw{for} @bnfNter{Name} @bnfter{=}
  exp @bnfter{,} exp @bnfopt{@bnfter{,} exp} @Rw{do} block @Rw{end}}
}
The given identifier (@bnfNter{Name}) defines the control variable,
which is a new variable local to the loop body (@emph{block}).

The loop starts by evaluating once the three control expressions.
Their values are called respectively
the @emph{initial value}, the @emph{limit}, and the @emph{step}.
If the step is absent, it defaults @N{to 1}.

If both the initial value and the step are integers,
the loop is done with integers;
note that the limit may not be an integer.
Otherwise, the three values are converted to
floats and the loop is done with floats.
Beware of floating-point accuracy in this case.

After that initialization,
the loop body is repeated with the value of the control variable
going through an arithmetic progression,
starting at the initial value,
with a common difference given by the step.
A negative step makes a decreasing sequence;
a step equal to zero raises an error.
The loop continues while the value is less than
or equal to the limit
(greater than or equal to for a negative step).
If the initial value is already greater than the limit
(or less than, if the step is negative),
the body is not executed.

For integer loops,
the control variable never wraps around;
instead, the loop ends in case of an overflow.

You should not change the value of the control variable
during the loop.
If you need its value after the loop,
assign it to another variable before exiting the loop.

}

@sect4{@title{The generic @Rw{for} loop}


The generic @Rw{for} statement works over functions,
called @def{iterators}.
On each iteration, the iterator function is called to produce a new value,
stopping when this new value is @nil.
The generic @Rw{for} loop has the following syntax:
@Produc{
@producname{stat}@producbody{@Rw{for} namelist @Rw{in} explist
                    @Rw{do} block @Rw{end}}
@producname{namelist}@producbody{@bnfNter{Name} @bnfrep{@bnfter{,} @bnfNter{Name}}}
}
A @Rw{for} statement like
@verbatim{
for @rep{var_1}, @Cdots, @rep{var_n} in @rep{explist} do @rep{body} end
}
works as follows.

The names @rep{var_i} declare loop variables local to the loop body.
The first of these variables is the @emph{control variable}.

The loop starts by evaluating @rep{explist}
to produce four values:
an @emph{iterator function},
a @emph{state},
an initial value for the control variable,
and a @emph{closing value}.

Then, at each iteration,
Lua calls the iterator function with two arguments:
the state and the control variable.
The results from this call are then assigned to the loop variables,
following the rules of multiple assignments @see{assignment}.
If the control variable becomes @nil,
the loop terminates.
Otherwise, the body is executed and the loop goes
to the next iteration.

The closing value behaves like a
to-be-closed variable @see{to-be-closed},
which can be used to release resources when the loop ends.
Otherwise, it does not interfere with the loop.

You should not change the value of the control variable
during the loop.

}

}

@sect3{funcstat| @title{Function Calls as Statements}
To allow possible side-effects,
function calls can be executed as statements:
@Produc{
@producname{stat}@producbody{functioncall}
}
In this case, all returned values are thrown away.
Function calls are explained in @See{functioncall}.

}

@sect3{localvar| @title{Local Declarations}
@x{Local variables} can be declared anywhere inside a block.
The declaration can include an initialization:
@Produc{
@producname{stat}@producbody{@Rw{local} attnamelist @bnfopt{@bnfter{=} explist}}
@producname{attnamelist}@producbody{
  @bnfNter{Name} attrib @bnfrep{@bnfter{,} @bnfNter{Name} attrib}}
}
If present, an initial assignment has the same semantics
of a multiple assignment @see{assignment}.
Otherwise, all variables are initialized with @nil.

Each variable name may be postfixed by an attribute
(a name between angle brackets):
@Produc{
@producname{attrib}@producbody{@bnfopt{@bnfter{<} @bnfNter{Name} @bnfter{>}}}
}
There are two possible attributes:
@id{const}, which declares a @x{constant variable},
that is, a variable that cannot be assigned to
after its initialization;
and @id{close}, which declares a to-be-closed variable @see{to-be-closed}.
A list of variables can contain at most one to-be-closed variable.

A chunk is also a block @see{chunks},
and so local variables can be declared in a chunk outside any explicit block.

The visibility rules for local variables are explained in @See{visibility}.

}

@sect3{to-be-closed| @title{To-be-closed Variables}

A to-be-closed variable behaves like a constant local variable,
except that its value is @emph{closed} whenever the variable
goes out of scope, including normal block termination,
exiting its block by @Rw{break}/@Rw{goto}/@Rw{return},
or exiting by an error.

Here, to @emph{close} a value means
to call its @idx{__close} metamethod.
When calling the metamethod,
the value itself is passed as the first argument
and the error object that caused the exit (if any)
is passed as a second argument;
if there was no error, the second argument is @nil.

The value assigned to a to-be-closed variable
must have a @idx{__close} metamethod
or be a false value.
(@nil and @false are ignored as to-be-closed values.)

If several to-be-closed variables go out of scope at the same event,
they are closed in the reverse order that they were declared.

If there is any error while running a closing method,
that error is handled like an error in the regular code
where the variable was defined.
However, Lua may call the method one more time.

After an error,
the other pending closing methods will still be called.
Errors in these methods
interrupt the respective method and generate a warning,
but are otherwise ignored;
the error reported is only the original one.

If a coroutine yields and is never resumed again,
some variables may never go out of scope,
and therefore they will never be closed.
(These variables are the ones created inside the coroutine
and in scope at the point where the coroutine yielded.)
Similarly, if a coroutine ends with an error,
it does not unwind its stack,
so it does not close any variable.
In both cases,
you can either use finalizers
or call @Lid{coroutine.close} to close the variables.
However, if the coroutine was created
through @Lid{coroutine.wrap},
then its corresponding function will close the coroutine
in case of errors.

}

}

@sect2{expressions| @title{Expressions}

@simplesect{

The basic expressions in Lua are the following:
@Produc{
@producname{exp}@producbody{prefixexp}
@producname{exp}@producbody{@Rw{nil} @Or @Rw{false} @Or @Rw{true}}
@producname{exp}@producbody{@bnfNter{Numeral}}
@producname{exp}@producbody{@bnfNter{LiteralString}}
@producname{exp}@producbody{functiondef}
@producname{exp}@producbody{tableconstructor}
@producname{exp}@producbody{@bnfter{...}}
@producname{exp}@producbody{exp binop exp}
@producname{exp}@producbody{unop exp}
@producname{prefixexp}@producbody{var @Or functioncall @Or
                                  @bnfter{(} exp @bnfter{)}}
}

Numerals and literal strings are explained in @See{lexical};
variables are explained in @See{variables};
function definitions are explained in @See{func-def};
function calls are explained in @See{functioncall};
table constructors are explained in @See{tableconstructor}.
Vararg expressions,
denoted by three dots (@Char{...}), can only be used when
directly inside a vararg function;
they are explained in @See{func-def}.

Binary operators comprise arithmetic operators @see{arith},
bitwise operators @see{bitwise},
relational operators @see{rel-ops}, logical operators @see{logic},
and the concatenation operator @see{concat}.
Unary operators comprise the unary minus @see{arith},
the unary bitwise NOT @see{bitwise},
the unary logical @Rw{not} @see{logic},
and the unary @def{length operator} @see{len-op}.

Both function calls and vararg expressions can result in multiple values.
If a function call is used as a statement @see{funcstat},
then its return list is adjusted to zero elements,
thus discarding all returned values.
If an expression is used as the last (or the only) element
of a list of expressions,
then no adjustment is made
(unless the expression is enclosed in parentheses).
In all other contexts,
Lua adjusts the result list to one element,
either discarding all values except the first one
or adding a single @nil if there are no values.

Here are some examples:
@verbatim{
f()                -- adjusted to 0 results
g(f(), x)          -- f() is adjusted to 1 result
g(x, f())          -- g gets x plus all results from f()
a,b,c = f(), x     -- f() is adjusted to 1 result (c gets nil)
a,b = ...          -- a gets the first vararg argument, b gets
                   -- the second (both a and b can get nil if there
                   -- is no corresponding vararg argument)

a,b,c = x, f()     -- f() is adjusted to 2 results
a,b,c = f()        -- f() is adjusted to 3 results
return f()         -- returns all results from f()
return ...         -- returns all received vararg arguments
return x,y,f()     -- returns x, y, and all results from f()
{f()}              -- creates a list with all results from f()
{...}              -- creates a list with all vararg arguments
{f(), nil}         -- f() is adjusted to 1 result
}

Any expression enclosed in parentheses always results in only one value.
Thus,
@T{(f(x,y,z))} is always a single value,
even if @id{f} returns several values.
(The value of @T{(f(x,y,z))} is the first value returned by @id{f}
or @nil if @id{f} does not return any values.)

}


@sect3{arith| @title{Arithmetic Operators}
Lua supports the following @x{arithmetic operators}:
@description{
@item{@T{+}|addition}
@item{@T{-}|subtraction}
@item{@T{*}|multiplication}
@item{@T{/}|float division}
@item{@T{//}|floor division}
@item{@T{%}|modulo}
@item{@T{^}|exponentiation}
@item{@T{-}|unary minus}
}

With the exception of exponentiation and float division,
the arithmetic operators work as follows:
If both operands are integers,
the operation is performed over integers and the result is an integer.
Otherwise, if both operands are numbers,
then they are converted to floats,
the operation is performed following the machine's rules
for floating-point arithmetic
(usually the @x{IEEE 754} standard),
and the result is a float.
(The string library coerces strings to numbers in
arithmetic operations; see @See{coercion} for details.)

Exponentiation and float division (@T{/})
always convert their operands to floats
and the result is always a float.
Exponentiation uses the @ANSI{pow},
so that it works for non-integer exponents too.

Floor division (@T{//}) is a division
that rounds the quotient towards minus infinity,
resulting in the floor of the division of its operands.

Modulo is defined as the remainder of a division
that rounds the quotient towards minus infinity (floor division).

In case of overflows in integer arithmetic,
all operations @emphx{wrap around}.
}

@sect3{bitwise| @title{Bitwise Operators}
Lua supports the following @x{bitwise operators}:
@description{
@item{@T{&}|bitwise AND}
@item{@T{@VerBar}|bitwise OR}
@item{@T{~}|bitwise exclusive OR}
@item{@T{>>}|right shift}
@item{@T{<<}|left shift}
@item{@T{~}|unary bitwise NOT}
}

All bitwise operations convert its operands to integers
@see{coercion},
operate on all bits of those integers,
and result in an integer.

Both right and left shifts fill the vacant bits with zeros.
Negative displacements shift to the other direction;
displacements with absolute values equal to or higher than
the number of bits in an integer
result in zero (as all bits are shifted out).

}

@sect3{coercion| @title{Coercions and Conversions}
Lua provides some automatic conversions between some
types and representations at run time.
Bitwise operators always convert float operands to integers.
Exponentiation and float division
always convert integer operands to floats.
All other arithmetic operations applied to mixed numbers
(integers and floats) convert the integer operand to a float.
The C API also converts both integers to floats and
floats to integers, as needed.
Moreover, string concatenation accepts numbers as arguments,
besides strings.

In a conversion from integer to float,
if the integer value has an exact representation as a float,
that is the result.
Otherwise,
the conversion gets the nearest higher or
the nearest lower representable value.
This kind of conversion never fails.

The conversion from float to integer
checks whether the float has an exact representation as an integer
(that is, the float has an integral value and
it is in the range of integer representation).
If it does, that representation is the result.
Otherwise, the conversion fails.

Several places in Lua coerce strings to numbers when necessary.
In particular,
the string library sets metamethods that try to coerce
strings to numbers in all arithmetic operations.
If the conversion fails,
the library calls the metamethod of the other operand
(if present) or it raises an error.
Note that bitwise operators do not do this coercion.

Nonetheless, it is always a good practice not to rely on these
implicit coercions, as they are not always applied;
in particular, @T{"1"==1} is false and @T{"1"<1} raises an error
@see{rel-ops}.
These coercions exist mainly for compatibility and may be removed
in future versions of the language.

A string is converted to an integer or a float
following its syntax and the rules of the Lua lexer.
The string may have also leading and trailing whitespaces and a sign.
All conversions from strings to numbers
accept both a dot and the current locale mark
as the radix character.
(The Lua lexer, however, accepts only a dot.)
If the string is not a valid numeral,
the conversion fails.
If necessary, the result of this first step is then converted
to a specific number subtype following the previous rules
for conversions between floats and integers.

The conversion from numbers to strings uses a
non-specified human-readable format.
To convert numbers to strings in any specific way,
use the function @Lid{string.format}.

}

@sect3{rel-ops| @title{Relational Operators}
Lua supports the following @x{relational operators}:
@description{
@item{@T{==}|equality}
@item{@T{~=}|inequality}
@item{@T{<}|less than}
@item{@T{>}|greater than}
@item{@T{<=}|less or equal}
@item{@T{>=}|greater or equal}
}
These operators always result in @false or @true.

Equality (@T{==}) first compares the type of its operands.
If the types are different, then the result is @false.
Otherwise, the values of the operands are compared.
Strings are equal if they have the same byte content.
Numbers are equal if they denote the same mathematical value.

Tables, userdata, and threads
are compared by reference:
two objects are considered equal only if they are the same object.
Every time you create a new object
(a table, a userdata, or a thread),
this new object is different from any previously existing object.
A function is always equal to itself.
Functions with any detectable difference
(different behavior, different definition) are always different.
Functions created at different times but with no detectable differences
may be classified as equal or not
(depending on internal caching details).

You can change the way that Lua compares tables and userdata
by using the @idx{__eq} metamethod @see{metatable}.

Equality comparisons do not convert strings to numbers
or vice versa.
Thus, @T{"0"==0} evaluates to @false,
and @T{t[0]} and @T{t["0"]} denote different
entries in a table.

The operator @T{~=} is exactly the negation of equality (@T{==}).

The order operators work as follows.
If both arguments are numbers,
then they are compared according to their mathematical values,
regardless of their subtypes.
Otherwise, if both arguments are strings,
then their values are compared according to the current locale.
Otherwise, Lua tries to call the @idx{__lt} or the @idx{__le}
metamethod @see{metatable}.
A comparison @T{a > b} is translated to @T{b < a}
and @T{a >= b} is translated to @T{b <= a}.

Following the @x{IEEE 754} standard,
the special value @x{NaN} is considered neither less than,
nor equal to, nor greater than any value, including itself.

}

@sect3{logic| @title{Logical Operators}
The @x{logical operators} in Lua are
@Rw{and}, @Rw{or}, and @Rw{not}.
Like the control structures @see{control},
all logical operators consider both @false and @nil as false
and anything else as true.

The negation operator @Rw{not} always returns @false or @true.
The conjunction operator @Rw{and} returns its first argument
if this value is @false or @nil;
otherwise, @Rw{and} returns its second argument.
The disjunction operator @Rw{or} returns its first argument
if this value is different from @nil and @false;
otherwise, @Rw{or} returns its second argument.
Both @Rw{and} and @Rw{or} use @x{short-circuit evaluation};
that is,
the second operand is evaluated only if necessary.
Here are some examples:
@verbatim{
10 or 20            --> 10
10 or error()       --> 10
nil or "a"          --> "a"
nil and 10          --> nil
false and error()   --> false
false and nil       --> false
false or nil        --> nil
10 and 20           --> 20
}

}

@sect3{concat| @title{Concatenation}
The string @x{concatenation} operator in Lua is
denoted by two dots (@Char{..}).
If both operands are strings or numbers,
then the numbers are converted to strings
in a non-specified format @see{coercion}.
Otherwise, the @idx{__concat} metamethod is called @see{metatable}.

}

@sect3{len-op| @title{The Length Operator}

The length operator is denoted by the unary prefix operator @T{#}.

The length of a string is its number of bytes.
(That is the usual meaning of string length when each
character is one byte.)

The length operator applied on a table
returns a @x{border} in that table.
A @def{border} in a table @id{t} is any natural number
that satisfies the following condition:
@verbatim{
(border == 0 or t[border] ~= nil) and t[border + 1] == nil
}
In words,
a border is any (natural) index present in the table
that is followed by an absent index
(or zero, when index 1 is absent).

A table with exactly one border is called a @def{sequence}.
For instance, the table @T{{10, 20, 30, 40, 50}} is a sequence,
as it has only one border (5).
The table @T{{10, 20, 30, nil, 50}} has two borders (3 and 5),
and therefore it is not a sequence.
(The @nil at index 4 is called a @emphx{hole}.)
The table @T{{nil, 20, 30, nil, nil, 60, nil}}
has three borders (0, 3, and 6) and three holes
(at indices 1, 4, and 5),
so it is not a sequence, too.
The table @T{{}} is a sequence with border 0.
Note that non-natural keys do not interfere
with whether a table is a sequence.

When @id{t} is a sequence,
@T{#t} returns its only border,
which corresponds to the intuitive notion of the length of the sequence.
When @id{t} is not a sequence,
@T{#t} can return any of its borders.
(The exact one depends on details of
the internal representation of the table,
which in turn can depend on how the table was populated and
the memory addresses of its non-numeric keys.)

The computation of the length of a table
has a guaranteed worst time of @M{O(log n)},
where @M{n} is the largest natural key in the table.

A program can modify the behavior of the length operator for
any value but strings through the @idx{__len} metamethod @see{metatable}.

}

@sect3{prec| @title{Precedence}
@x{Operator precedence} in Lua follows the table below,
from lower to higher priority:
@verbatim{
or
and
<     >     <=    >=    ~=    ==
|
~
&
<<    >>
..
+     -
*     /     //    %
unary operators (not   #     -     ~)
^
}
As usual,
you can use parentheses to change the precedences of an expression.
The concatenation (@Char{..}) and exponentiation (@Char{^})
operators are right associative.
All other binary operators are left associative.

}

@sect3{tableconstructor| @title{Table Constructors}
Table @x{constructors} are expressions that create tables.
Every time a constructor is evaluated, a new table is created.
A constructor can be used to create an empty table
or to create a table and initialize some of its fields.
The general syntax for constructors is
@Produc{
@producname{tableconstructor}@producbody{@bnfter{@Open} @bnfopt{fieldlist} @bnfter{@Close}}
@producname{fieldlist}@producbody{field @bnfrep{fieldsep field} @bnfopt{fieldsep}}
@producname{field}@producbody{@bnfter{[} exp @bnfter{]} @bnfter{=} exp @Or
               @bnfNter{Name} @bnfter{=} exp @Or exp}
@producname{fieldsep}@producbody{@bnfter{,} @Or @bnfter{;}}
}

Each field of the form @T{[exp1] = exp2} adds to the new table an entry
with key @id{exp1} and value @id{exp2}.
A field of the form @T{name = exp} is equivalent to
@T{["name"] = exp}.
Fields of the form @id{exp} are equivalent to
@T{[i] = exp}, where @id{i} are consecutive integers
starting with 1;
fields in the other formats do not affect this counting.
For example,
@verbatim{
a = { [f(1)] = g; "x", "y"; x = 1, f(x), [30] = 23; 45 }
}
is equivalent to
@verbatim{
do
  local t = {}
  t[f(1)] = g
  t[1] = "x"         -- 1st exp
  t[2] = "y"         -- 2nd exp
  t.x = 1            -- t["x"] = 1
  t[3] = f(x)        -- 3rd exp
  t[30] = 23
  t[4] = 45          -- 4th exp
  a = t
end
}

The order of the assignments in a constructor is undefined.
(This order would be relevant only when there are repeated keys.)

If the last field in the list has the form @id{exp}
and the expression is a function call or a vararg expression,
then all values returned by this expression enter the list consecutively
@see{functioncall}.

The field list can have an optional trailing separator,
as a convenience for machine-generated code.

}

@sect3{functioncall| @title{Function Calls}
A @x{function call} in Lua has the following syntax:
@Produc{
@producname{functioncall}@producbody{prefixexp args}
}
In a function call,
first @bnfNter{prefixexp} and @bnfNter{args} are evaluated.
If the value of @bnfNter{prefixexp} has type @emph{function},
then this function is called
with the given arguments.
Otherwise, if present,
the @bnfNter{prefixexp} @idx{__call} metamethod is called:
its first argument is the value of @bnfNter{prefixexp},
followed by the original call arguments
@see{metatable}.

The form
@Produc{
@producname{functioncall}@producbody{prefixexp @bnfter{:} @bnfNter{Name} args}
}
can be used to emulate methods.
A call @T{v:name(@rep{args})}
is syntactic sugar for @T{v.name(v,@rep{args})},
except that @id{v} is evaluated only once.

Arguments have the following syntax:
@Produc{
@producname{args}@producbody{@bnfter{(} @bnfopt{explist} @bnfter{)}}
@producname{args}@producbody{tableconstructor}
@producname{args}@producbody{@bnfNter{LiteralString}}
}
All argument expressions are evaluated before the call.
A call of the form @T{f{@rep{fields}}} is
syntactic sugar for @T{f({@rep{fields}})};
that is, the argument list is a single new table.
A call of the form @T{f'@rep{string}'}
(or @T{f"@rep{string}"} or @T{f[[@rep{string}]]})
is syntactic sugar for @T{f('@rep{string}')};
that is, the argument list is a single literal string.

A call of the form @T{return @rep{functioncall}} not in the
scope of a to-be-closed variable is called a @def{tail call}.
Lua implements @def{proper tail calls}
(or @def{proper tail recursion}):
in a tail call,
the called function reuses the stack entry of the calling function.
Therefore, there is no limit on the number of nested tail calls that
a program can execute.
However, a tail call erases any debug information about the
calling function.
Note that a tail call only happens with a particular syntax,
where the @Rw{return} has one single function call as argument,
and it is outside the scope of any to-be-closed variable.
This syntax makes the calling function return exactly
the returns of the called function,
without any intervening action.
So, none of the following examples are tail calls:
@verbatim{
return (f(x))        -- results adjusted to 1
return 2 * f(x)      -- result multiplied by 2
return x, f(x)       -- additional results
f(x); return         -- results discarded
return x or f(x)     -- results adjusted to 1
}

}

@sect3{func-def| @title{Function Definitions}

The syntax for function definition is
@Produc{
@producname{functiondef}@producbody{@Rw{function} funcbody}
@producname{funcbody}@producbody{@bnfter{(} @bnfopt{parlist} @bnfter{)} block @Rw{end}}
}

The following syntactic sugar simplifies function definitions:
@Produc{
@producname{stat}@producbody{@Rw{function} funcname funcbody}
@producname{stat}@producbody{@Rw{local} @Rw{function} @bnfNter{Name} funcbody}
@producname{funcname}@producbody{@bnfNter{Name} @bnfrep{@bnfter{.} @bnfNter{Name}} @bnfopt{@bnfter{:} @bnfNter{Name}}}
}
The statement
@verbatim{
function f () @rep{body} end
}
translates to
@verbatim{
f = function () @rep{body} end
}
The statement
@verbatim{
function t.a.b.c.f () @rep{body} end
}
translates to
@verbatim{
t.a.b.c.f = function () @rep{body} end
}
The statement
@verbatim{
local function f () @rep{body} end
}
translates to
@verbatim{
local f; f = function () @rep{body} end
}
not to
@verbatim{
local f = function () @rep{body} end
}
(This only makes a difference when the body of the function
contains references to @id{f}.)

A function definition is an executable expression,
whose value has type @emph{function}.
When Lua precompiles a chunk,
all its function bodies are precompiled too,
but they are not created yet.
Then, whenever Lua executes the function definition,
the function is @emph{instantiated} (or @emph{closed}).
This function instance, or @emphx{closure},
is the final value of the expression.

Parameters act as local variables that are
initialized with the argument values:
@Produc{
@producname{parlist}@producbody{namelist @bnfopt{@bnfter{,} @bnfter{...}} @Or
  @bnfter{...}}
}
When a Lua function is called,
it adjusts its list of @x{arguments} to
the length of its list of parameters,
unless the function is a @def{vararg function},
which is indicated by three dots (@Char{...})
at the end of its parameter list.
A vararg function does not adjust its argument list;
instead, it collects all extra arguments and supplies them
to the function through a @def{vararg expression},
which is also written as three dots.
The value of this expression is a list of all actual extra arguments,
similar to a function with multiple results.
If a vararg expression is used inside another expression
or in the middle of a list of expressions,
then its return list is adjusted to one element.
If the expression is used as the last element of a list of expressions,
then no adjustment is made
(unless that last expression is enclosed in parentheses).


As an example, consider the following definitions:
@verbatim{
function f(a, b) end
function g(a, b, ...) end
function r() return 1,2,3 end
}
Then, we have the following mapping from arguments to parameters and
to the vararg expression:
@verbatim{
CALL             PARAMETERS

f(3)             a=3, b=nil
f(3, 4)          a=3, b=4
f(3, 4, 5)       a=3, b=4
f(r(), 10)       a=1, b=10
f(r())           a=1, b=2

g(3)             a=3, b=nil, ... -->  (nothing)
g(3, 4)          a=3, b=4,   ... -->  (nothing)
g(3, 4, 5, 8)    a=3, b=4,   ... -->  5  8
g(5, r())        a=5, b=1,   ... -->  2  3
}

Results are returned using the @Rw{return} statement @see{control}.
If control reaches the end of a function
without encountering a @Rw{return} statement,
then the function returns with no results.

@index{multiple return}
There is a system-dependent limit on the number of values
that a function may return.
This limit is guaranteed to be greater than 1000.

The @emphx{colon} syntax
is used to emulate @def{methods},
adding an implicit extra parameter @idx{self} to the function.
Thus, the statement
@verbatim{
function t.a.b.c:f (@rep{params}) @rep{body} end
}
is syntactic sugar for
@verbatim{
t.a.b.c.f = function (self, @rep{params}) @rep{body} end
}

}

}

@sect2{visibility| @title{Visibility Rules}

@index{visibility}
Lua is a lexically scoped language.
The scope of a local variable begins at the first statement after
its declaration and lasts until the last non-void statement
of the innermost block that includes the declaration.
Consider the following example:
@verbatim{
x = 10                -- global variable
do                    -- new block
  local x = x         -- new 'x', with value 10
  print(x)            --> 10
  x = x+1
  do                  -- another block
    local x = x+1     -- another 'x'
    print(x)          --> 12
  end
  print(x)            --> 11
end
print(x)              --> 10  (the global one)
}

Notice that, in a declaration like @T{local x = x},
the new @id{x} being declared is not in scope yet,
and so the second @id{x} refers to the outside variable.

Because of the @x{lexical scoping} rules,
local variables can be freely accessed by functions
defined inside their scope.
A local variable used by an inner function is called an @def{upvalue}
(or @emphx{external local variable}, or simply @emphx{external variable})
inside the inner function.

Notice that each execution of a @Rw{local} statement
defines new local variables.
Consider the following example:
@verbatim{
a = {}
local x = 20
for i = 1, 10 do
  local y = 0
  a[i] = function () y = y + 1; return x + y end
end
}
The loop creates ten closures
(that is, ten instances of the anonymous function).
Each of these closures uses a different @id{y} variable,
while all of them share the same @id{x}.

}

}


@C{-------------------------------------------------------------------------}
@sect1{API| @title{The Application Program Interface}

@simplesect{

@index{C API}
This section describes the @N{C API} for Lua, that is,
the set of @N{C functions} available to the host program to communicate
with Lua.
All API functions and related types and constants
are declared in the header file @defid{lua.h}.

Even when we use the term @Q{function},
any facility in the API may be provided as a macro instead.
Except where stated otherwise,
all such macros use each of their arguments exactly once
(except for the first argument, which is always a Lua state),
and so do not generate any hidden side-effects.

As in most @N{C libraries},
the Lua API functions do not check their arguments
for validity or consistency.
However, you can change this behavior by compiling Lua
with the macro @defid{LUA_USE_APICHECK} defined.

The Lua library is fully reentrant:
it has no global variables.
It keeps all information it needs in a dynamic structure,
called the @def{Lua state}.

Each Lua state has one or more threads,
which correspond to independent, cooperative lines of execution.
The type @Lid{lua_State} (despite its name) refers to a thread.
(Indirectly, through the thread, it also refers to the
Lua state associated to the thread.)

A pointer to a thread must be passed as the first argument to
every function in the library, except to @Lid{lua_newstate},
which creates a Lua state from scratch and returns a pointer
to the @emph{main thread} in the new state.

}


@sect2{@title{The Stack}

@simplesect{

Lua uses a @emph{virtual stack} to pass values to and from C.
Each element in this stack represents a Lua value
(@nil, number, string, etc.).
Functions in the API can access this stack through the
Lua state parameter that they receive.

Whenever Lua calls C, the called function gets a new stack,
which is independent of previous stacks and of stacks of
@N{C functions} that are still active.
This stack initially contains any arguments to the @N{C function}
and it is where the @N{C function} can store temporary
Lua values and must push its results
to be returned to the caller @seeC{lua_CFunction}.

For convenience,
most query operations in the API do not follow a strict stack discipline.
Instead, they can refer to any element in the stack
by using an @emph{index}:@index{index (API stack)}
A positive index represents an absolute stack position,
starting @N{at 1} as the bottom of the stack;
a negative index represents an offset relative to the top of the stack.
More specifically, if the stack has @rep{n} elements,
then @N{index 1} represents the first element
(that is, the element that was pushed onto the stack first)
and
@N{index @rep{n}} represents the last element;
@N{index @num{-1}} also represents the last element
(that is, the element at @N{the top})
and index @M{-n} represents the first element.

}

@sect3{stacksize| @title{Stack Size}

When you interact with the Lua API,
you are responsible for ensuring consistency.
In particular,
@emph{you are responsible for controlling stack overflow}.
You can use the function @Lid{lua_checkstack}
to ensure that the stack has enough space for pushing new elements.

Whenever Lua calls C,
it ensures that the stack has space for
at least @defid{LUA_MINSTACK} extra elements;
that is, you can safely push up to @id{LUA_MINSTACK} values into it.
@id{LUA_MINSTACK} is defined as 20,
so that usually you do not have to worry about stack space
unless your code has loops pushing elements onto the stack.

When you call a Lua function
without a fixed number of results @seeF{lua_call},
Lua ensures that the stack has enough space for all results,
but it does not ensure any extra space.
So, before pushing anything on the stack after such a call
you should use @Lid{lua_checkstack}.

}

@sect3{@title{Valid and Acceptable Indices}

Any function in the API that receives stack indices
works only with @emphx{valid indices} or @emphx{acceptable indices}.

A @def{valid index} is an index that refers to a
position that stores a modifiable Lua value.
It comprises stack indices @N{between 1} and the stack top
(@T{1 @leq abs(index) @leq top})
@index{stack index}
plus @def{pseudo-indices},
which represent some positions that are accessible to @N{C code}
but that are not in the stack.
Pseudo-indices are used to access the registry @see{registry}
and the upvalues of a @N{C function} @see{c-closure}.

Functions that do not need a specific mutable position,
but only a value (e.g., query functions),
can be called with acceptable indices.
An @def{acceptable index} can be any valid index,
but it also can be any positive index after the stack top
within the space allocated for the stack,
that is, indices up to the stack size.
(Note that 0 is never an acceptable index.)
Indices to upvalues @see{c-closure} greater than the real number
of upvalues in the current @N{C function} are also acceptable (but invalid).
Except when noted otherwise,
functions in the API work with acceptable indices.

Acceptable indices serve to avoid extra tests
against the stack top when querying the stack.
For instance, a @N{C function} can query its third argument
without the need to check whether there is a third argument,
that is, without the need to check whether 3 is a valid index.

For functions that can be called with acceptable indices,
any non-valid index is treated as if it
contains a value of a virtual type @defid{LUA_TNONE},
which behaves like a nil value.

}

@sect3{constchar|@title{Pointers to strings}

Several functions in the API return pointers (@T{const char*})
to Lua strings in the stack.
(See @Lid{lua_pushfstring}, @Lid{lua_pushlstring},
@Lid{lua_pushstring}, and @Lid{lua_tolstring}.
See also @Lid{luaL_checklstring}, @Lid{luaL_checkstring},
and @Lid{luaL_tolstring} in the auxiliary library.)

In general,
Lua's garbage collection can free or move internal memory
and then invalidate pointers to internal strings.
To allow a safe use of these pointers,
The API guarantees that any pointer to a string in a stack index
is valid while the value at that index is neither modified nor popped.
When the index is a pseudo-index (referring to an upvalue),
the pointer is valid while the corresponding call is active and
the corresponding upvalue is not modified.

Some functions in the debug interface
also return pointers to strings,
namely @Lid{lua_getlocal}, @Lid{lua_getupvalue},
@Lid{lua_setlocal}, and @Lid{lua_setupvalue}.
For these functions, the pointer is guaranteed to
be valid while the caller function is active and
the given closure (if one was given) is in the stack.

Except for these guarantees,
the garbage collector is free to invalidate
any pointer to internal strings.

}

}

@sect2{c-closure| @title{C Closures}

When a @N{C function} is created,
it is possible to associate some values with it,
thus creating a @def{@N{C closure}}
@seeC{lua_pushcclosure};
these values are called @def{upvalues} and are
accessible to the function whenever it is called.

Whenever a @N{C function} is called,
its upvalues are located at specific pseudo-indices.
These pseudo-indices are produced by the macro
@Lid{lua_upvalueindex}.
The first upvalue associated with a function is at index
@T{lua_upvalueindex(1)}, and so on.
Any access to @T{lua_upvalueindex(@rep{n})},
where @rep{n} is greater than the number of upvalues of the
current function
(but not greater than 256,
which is one plus the maximum number of upvalues in a closure),
produces an acceptable but invalid index.

A @N{C closure} can also change the values
of its corresponding upvalues.

}

@sect2{registry| @title{Registry}

Lua provides a @def{registry},
a predefined table that can be used by any @N{C code} to
store whatever Lua values it needs to store.
The registry table is always accessible at pseudo-index
@defid{LUA_REGISTRYINDEX}.
Any @N{C library} can store data into this table,
but it must take care to choose keys
that are different from those used
by other libraries, to avoid collisions.
Typically, you should use as key a string containing your library name,
or a light userdata with the address of a @N{C object} in your code,
or any Lua object created by your code.
As with variable names,
string keys starting with an underscore followed by
uppercase letters are reserved for Lua.

The integer keys in the registry are used
by the reference mechanism @seeC{luaL_ref}
and by some predefined values.
Therefore, integer keys in the registry
must not be used for other purposes.

When you create a new Lua state,
its registry comes with some predefined values.
These predefined values are indexed with integer keys
defined as constants in @id{lua.h}.
The following constants are defined:
@description{
@item{@defid{LUA_RIDX_MAINTHREAD}| At this index the registry has
the main thread of the state.
(The main thread is the one created together with the state.)
}

@item{@defid{LUA_RIDX_GLOBALS}| At this index the registry has
the @x{global environment}.
}
}

}

@sect2{C-error|@title{Error Handling in C}

@simplesect{

Internally, Lua uses the C @id{longjmp} facility to handle errors.
(Lua will use exceptions if you compile it as C++;
search for @id{LUAI_THROW} in the source code for details.)
When Lua faces any error,
such as a @x{memory allocation error} or a type error,
it @emph{raises} an error;
that is, it does a long jump.
A @emphx{protected environment} uses @id{setjmp}
to set a recovery point;
any error jumps to the most recent active recovery point.

Inside a @N{C function} you can raise an error explicitly
by calling @Lid{lua_error}.

Most functions in the API can raise an error,
for instance due to a @x{memory allocation error}.
The documentation for each function indicates whether
it can raise errors.

If an error happens outside any protected environment,
Lua calls a @def{panic function} (see @Lid{lua_atpanic})
and then calls @T{abort},
thus exiting the host application.
Your panic function can avoid this exit by
never returning
(e.g., doing a long jump to your own recovery point outside Lua).

The panic function,
as its name implies,
is a mechanism of last resort.
Programs should avoid it.
As a general rule,
when a @N{C function} is called by Lua with a Lua state,
it can do whatever it wants on that Lua state,
as it should be already protected.
However,
when C code operates on other Lua states
(e.g., a Lua-state argument to the function,
a Lua state stored in the registry, or
the result of @Lid{lua_newthread}),
it should use them only in API calls that cannot raise errors.

The panic function runs as if it were a @x{message handler} @see{error};
in particular, the error object is on the top of the stack.
However, there is no guarantee about stack space.
To push anything on the stack,
the panic function must first check the available space @see{stacksize}.

}


@sect3{statuscodes|@title{Status Codes}

Several functions that report errors in the API use the following
status codes to indicate different kinds of errors or other conditions:
@description{

@item{@defid{LUA_OK} (0)| no errors.}

@item{@defid{LUA_ERRRUN}| a runtime error.}

@item{@defid{LUA_ERRMEM}|
@x{memory allocation error}.
For such errors, Lua does not call the @x{message handler}.
}

@item{@defid{LUA_ERRERR}| error while running the @x{message handler}.}

@item{@defid{LUA_ERRSYNTAX}| syntax error during precompilation.}

@item{@defid{LUA_YIELD}| the thread (coroutine) yields.}

@item{@defid{LUA_ERRFILE}| a file-related error;
e.g., it cannot open or read the file.}

}
These constants are defined in the header file @id{lua.h}.

}

}

@sect2{continuations|@title{Handling Yields in C}

Internally, Lua uses the C @id{longjmp} facility to yield a coroutine.
Therefore, if a @N{C function} @id{foo} calls an API function
and this API function yields
(directly or indirectly by calling another function that yields),
Lua cannot return to @id{foo} any more,
because the @id{longjmp} removes its frame from the C stack.

To avoid this kind of problem,
Lua raises an error whenever it tries to yield across an API call,
except for three functions:
@Lid{lua_yieldk}, @Lid{lua_callk}, and @Lid{lua_pcallk}.
All those functions receive a @def{continuation function}
(as a parameter named @id{k}) to continue execution after a yield.

We need to set some terminology to explain continuations.
We have a @N{C function} called from Lua which we will call
the @emph{original function}.
This original function then calls one of those three functions in the C API,
which we will call the @emph{callee function},
that then yields the current thread.
This can happen when the callee function is @Lid{lua_yieldk},
or when the callee function is either @Lid{lua_callk} or @Lid{lua_pcallk}
and the function called by them yields.

Suppose the running thread yields while executing the callee function.
After the thread resumes,
it eventually will finish running the callee function.
However,
the callee function cannot return to the original function,
because its frame in the C stack was destroyed by the yield.
Instead, Lua calls a @def{continuation function},
which was given as an argument to the callee function.
As the name implies,
the continuation function should continue the task
of the original function.

As an illustration, consider the following function:
@verbatim{
int original_function (lua_State *L) {
  ...     /* code 1 */
  status = lua_pcall(L, n, m, h);  /* calls Lua */
  ...     /* code 2 */
}
}
Now we want to allow
the Lua code being run by @Lid{lua_pcall} to yield.
First, we can rewrite our function like here:
@verbatim{
int k (lua_State *L, int status, lua_KContext ctx) {
  ...  /* code 2 */
}

int original_function (lua_State *L) {
  ...     /* code 1 */
  return k(L, lua_pcall(L, n, m, h), ctx);
}
}
In the above code,
the new function @id{k} is a
@emph{continuation function} (with type @Lid{lua_KFunction}),
which should do all the work that the original function
was doing after calling @Lid{lua_pcall}.
Now, we must inform Lua that it must call @id{k} if the Lua code
being executed by @Lid{lua_pcall} gets interrupted in some way
(errors or yielding),
so we rewrite the code as here,
replacing @Lid{lua_pcall} by @Lid{lua_pcallk}:
@verbatim{
int original_function (lua_State *L) {
  ...     /* code 1 */
  return k(L, lua_pcallk(L, n, m, h, ctx2, k), ctx1);
}
}
Note the external, explicit call to the continuation:
Lua will call the continuation only if needed, that is,
in case of errors or resuming after a yield.
If the called function returns normally without ever yielding,
@Lid{lua_pcallk} (and @Lid{lua_callk}) will also return normally.
(Of course, instead of calling the continuation in that case,
you can do the equivalent work directly inside the original function.)

Besides the Lua state,
the continuation function has two other parameters:
the final status of the call and the context value (@id{ctx}) that
was passed originally to @Lid{lua_pcallk}.
Lua does not use this context value;
it only passes this value from the original function to the
continuation function.
For @Lid{lua_pcallk},
the status is the same value that would be returned by @Lid{lua_pcallk},
except that it is @Lid{LUA_YIELD} when being executed after a yield
(instead of @Lid{LUA_OK}).
For @Lid{lua_yieldk} and @Lid{lua_callk},
the status is always @Lid{LUA_YIELD} when Lua calls the continuation.
(For these two functions,
Lua will not call the continuation in case of errors,
because they do not handle errors.)
Similarly, when using @Lid{lua_callk},
you should call the continuation function
with @Lid{LUA_OK} as the status.
(For @Lid{lua_yieldk}, there is not much point in calling
directly the continuation function,
because @Lid{lua_yieldk} usually does not return.)

Lua treats the continuation function as if it were the original function.
The continuation function receives the same Lua stack
from the original function,
in the same state it would be if the callee function had returned.
(For instance,
after a @Lid{lua_callk} the function and its arguments are
removed from the stack and replaced by the results from the call.)
It also has the same upvalues.
Whatever it returns is handled by Lua as if it were the return
of the original function.

}

@sect2{@title{Functions and Types}

Here we list all functions and types from the @N{C API} in
alphabetical order.
Each function has an indicator like this:
@apii{o,p,x}

The first field, @T{o},
is how many elements the function pops from the stack.
The second field, @T{p},
is how many elements the function pushes onto the stack.
(Any function always pushes its results after popping its arguments.)
A field in the form @T{x|y} means the function can push (or pop)
@T{x} or @T{y} elements,
depending on the situation;
an interrogation mark @Char{?} means that
we cannot know how many elements the function pops/pushes
by looking only at its arguments.
(For instance, they may depend on what is in the stack.)
The third field, @T{x},
tells whether the function may raise errors:
@Char{-} means the function never raises any error;
@Char{m} means the function may raise only out-of-memory errors;
@Char{v} means the function may raise the errors explained in the text;
@Char{e} means the function can run arbitrary Lua code,
either directly or through metamethods,
and therefore may raise any errors.


@APIEntry{int lua_absindex (lua_State *L, int idx);|
@apii{0,0,-}

Converts the @x{acceptable index} @id{idx}
into an equivalent @x{absolute index}
(that is, one that does not depend on the stack top).

}


@APIEntry{
typedef void * (*lua_Alloc) (void *ud,
                             void *ptr,
                             size_t osize,
                             size_t nsize);|

The type of the @x{memory-allocation function} used by Lua states.
The allocator function must provide a
functionality similar to @id{realloc},
but not exactly the same.
Its arguments are
@id{ud}, an opaque pointer passed to @Lid{lua_newstate};
@id{ptr}, a pointer to the block being allocated/reallocated/freed;
@id{osize}, the original size of the block or some code about what
is being allocated;
and @id{nsize}, the new size of the block.

When @id{ptr} is not @id{NULL},
@id{osize} is the size of the block pointed by @id{ptr},
that is, the size given when it was allocated or reallocated.

When @id{ptr} is @id{NULL},
@id{osize} encodes the kind of object that Lua is allocating.
@id{osize} is any of
@Lid{LUA_TSTRING}, @Lid{LUA_TTABLE}, @Lid{LUA_TFUNCTION},
@Lid{LUA_TUSERDATA}, or @Lid{LUA_TTHREAD} when (and only when)
Lua is creating a new object of that type.
When @id{osize} is some other value,
Lua is allocating memory for something else.

Lua assumes the following behavior from the allocator function:

When @id{nsize} is zero,
the allocator must behave like @id{free}
and then return @id{NULL}.

When @id{nsize} is not zero,
the allocator must behave like @id{realloc}.
In particular, the allocator returns @id{NULL}
if and only if it cannot fulfill the request.

Here is a simple implementation for the @x{allocator function}.
It is used in the auxiliary library by @Lid{luaL_newstate}.
@verbatim{
static void *l_alloc (void *ud, void *ptr, size_t osize,
                                           size_t nsize) {
  (void)ud;  (void)osize;  /* not used */
  if (nsize == 0) {
    free(ptr);
    return NULL;
  }
  else
    return realloc(ptr, nsize);
}
}
Note that @N{Standard C} ensures
that @T{free(NULL)} has no effect and that
@T{realloc(NULL,size)} is equivalent to @T{malloc(size)}.

}

@APIEntry{void lua_arith (lua_State *L, int op);|
@apii{2|1,1,e}

Performs an arithmetic or bitwise operation over the two values
(or one, in the case of negations)
at the top of the stack,
with the value on the top being the second operand,
pops these values, and pushes the result of the operation.
The function follows the semantics of the corresponding Lua operator
(that is, it may call metamethods).

The value of @id{op} must be one of the following constants:
@description{

@item{@defid{LUA_OPADD}| performs addition (@T{+})}
@item{@defid{LUA_OPSUB}| performs subtraction (@T{-})}
@item{@defid{LUA_OPMUL}| performs multiplication (@T{*})}
@item{@defid{LUA_OPDIV}| performs float division (@T{/})}
@item{@defid{LUA_OPIDIV}| performs floor division (@T{//})}
@item{@defid{LUA_OPMOD}| performs modulo (@T{%})}
@item{@defid{LUA_OPPOW}| performs exponentiation (@T{^})}
@item{@defid{LUA_OPUNM}| performs mathematical negation (unary @T{-})}
@item{@defid{LUA_OPBNOT}| performs bitwise NOT (@T{~})}
@item{@defid{LUA_OPBAND}| performs bitwise AND (@T{&})}
@item{@defid{LUA_OPBOR}| performs bitwise OR (@T{|})}
@item{@defid{LUA_OPBXOR}| performs bitwise exclusive OR (@T{~})}
@item{@defid{LUA_OPSHL}| performs left shift (@T{<<})}
@item{@defid{LUA_OPSHR}| performs right shift (@T{>>})}

}

}

@APIEntry{lua_CFunction lua_atpanic (lua_State *L, lua_CFunction panicf);|
@apii{0,0,-}

Sets a new panic function and returns the old one @see{C-error}.

}

@APIEntry{void lua_call (lua_State *L, int nargs, int nresults);|
@apii{nargs+1,nresults,e}

Calls a function.
Like regular Lua calls,
@id{lua_call} respects the @idx{__call} metamethod.
So, here the word @Q{function}
means any callable value.

To do a call you must use the following protocol:
first, the function to be called is pushed onto the stack;
then, the arguments to the call are pushed
in direct order;
that is, the first argument is pushed first.
Finally you call @Lid{lua_call};
@id{nargs} is the number of arguments that you pushed onto the stack.
When the function returns,
all arguments and the function value are popped
and the call results are pushed onto the stack.
The number of results is adjusted to @id{nresults},
unless @id{nresults} is @defid{LUA_MULTRET}.
In this case, all results from the function are pushed;
Lua takes care that the returned values fit into the stack space,
but it does not ensure any extra space in the stack.
The function results are pushed onto the stack in direct order
(the first result is pushed first),
so that after the call the last result is on the top of the stack.

Any error while calling and running the function is propagated upwards
(with a @id{longjmp}).

The following example shows how the host program can do the
equivalent to this Lua code:
@verbatim{
a = f("how", t.x, 14)
}
Here it is @N{in C}:
@verbatim{
lua_getglobal(L, "f");                  /* function to be called */
lua_pushliteral(L, "how");                       /* 1st argument */
lua_getglobal(L, "t");                    /* table to be indexed */
lua_getfield(L, -1, "x");        /* push result of t.x (2nd arg) */
lua_remove(L, -2);                  /* remove 't' from the stack */
lua_pushinteger(L, 14);                          /* 3rd argument */
lua_call(L, 3, 1);     /* call 'f' with 3 arguments and 1 result */
lua_setglobal(L, "a");                         /* set global 'a' */
}
Note that the code above is @emph{balanced}:
at its end, the stack is back to its original configuration.
This is considered good programming practice.

}

@APIEntry{
void lua_callk (lua_State *L,
                int nargs,
                int nresults,
                lua_KContext ctx,
                lua_KFunction k);|
@apii{nargs + 1,nresults,e}

This function behaves exactly like @Lid{lua_call},
but allows the called function to yield @see{continuations}.

}

@APIEntry{typedef int (*lua_CFunction) (lua_State *L);|

Type for @N{C functions}.

In order to communicate properly with Lua,
a @N{C function} must use the following protocol,
which defines the way parameters and results are passed:
a @N{C function} receives its arguments from Lua in its stack
in direct order (the first argument is pushed first).
So, when the function starts,
@T{lua_gettop(L)} returns the number of arguments received by the function.
The first argument (if any) is at index 1
and its last argument is at index @T{lua_gettop(L)}.
To return values to Lua, a @N{C function} just pushes them onto the stack,
in direct order (the first result is pushed first),
and returns in C the number of results.
Any other value in the stack below the results will be properly
discarded by Lua.
Like a Lua function, a @N{C function} called by Lua can also return
many results.

As an example, the following function receives a variable number
of numeric arguments and returns their average and their sum:
@verbatim{
static int foo (lua_State *L) {
  int n = lua_gettop(L);    /* number of arguments */
  lua_Number sum = 0.0;
  int i;
  for (i = 1; i <= n; i++) {
    if (!lua_isnumber(L, i)) {
      lua_pushliteral(L, "incorrect argument");
      lua_error(L);
    }
    sum += lua_tonumber(L, i);
  }
  lua_pushnumber(L, sum/n);        /* first result */
  lua_pushnumber(L, sum);         /* second result */
  return 2;                   /* number of results */
}
}



}


@APIEntry{int lua_checkstack (lua_State *L, int n);|
@apii{0,0,-}

Ensures that the stack has space for at least @id{n} extra elements,
that is, that you can safely push up to @id{n} values into it.
It returns false if it cannot fulfill the request,
either because it would cause the stack
to be greater than a fixed maximum size
(typically at least several thousand elements) or
because it cannot allocate memory for the extra space.
This function never shrinks the stack;
if the stack already has space for the extra elements,
it is left unchanged.

}

@APIEntry{void lua_close (lua_State *L);|
@apii{0,0,-}

Close all active to-be-closed variables in the main thread,
release all objects in the given Lua state
(calling the corresponding garbage-collection metamethods, if any),
and frees all dynamic memory used by this state.

On several platforms, you may not need to call this function,
because all resources are naturally released when the host program ends.
On the other hand, long-running programs that create multiple states,
such as daemons or web servers,
will probably need to close states as soon as they are not needed.

}

@APIEntry{int lua_compare (lua_State *L, int index1, int index2, int op);|
@apii{0,0,e}

Compares two Lua values.
Returns 1 if the value at index @id{index1} satisfies @id{op}
when compared with the value at index @id{index2},
following the semantics of the corresponding Lua operator
(that is, it may call metamethods).
Otherwise @N{returns 0}.
Also @N{returns 0} if any of the indices is not valid.

The value of @id{op} must be one of the following constants:
@description{

@item{@defid{LUA_OPEQ}| compares for equality (@T{==})}
@item{@defid{LUA_OPLT}| compares for less than (@T{<})}
@item{@defid{LUA_OPLE}| compares for less or equal (@T{<=})}

}

}

@APIEntry{void lua_concat (lua_State *L, int n);|
@apii{n,1,e}

Concatenates the @id{n} values at the top of the stack,
pops them, and leaves the result on the top.
If @N{@T{n} is 1}, the result is the single value on the stack
(that is, the function does nothing);
if @id{n} is 0, the result is the empty string.
Concatenation is performed following the usual semantics of Lua
@see{concat}.

}

@APIEntry{void lua_copy (lua_State *L, int fromidx, int toidx);|
@apii{0,0,-}

Copies the element at index @id{fromidx}
into the valid index @id{toidx},
replacing the value at that position.
Values at other positions are not affected.

}

@APIEntry{void lua_createtable (lua_State *L, int narr, int nrec);|
@apii{0,1,m}

Creates a new empty table and pushes it onto the stack.
Parameter @id{narr} is a hint for how many elements the table
will have as a sequence;
parameter @id{nrec} is a hint for how many other elements
the table will have.
Lua may use these hints to preallocate memory for the new table.
This preallocation may help performance when you know in advance
how many elements the table will have.
Otherwise you can use the function @Lid{lua_newtable}.

}

@APIEntry{int lua_dump (lua_State *L,
                        lua_Writer writer,
                        void *data,
                        int strip);|
@apii{0,0,-}

Dumps a function as a binary chunk.
Receives a Lua function on the top of the stack
and produces a binary chunk that,
if loaded again,
results in a function equivalent to the one dumped.
As it produces parts of the chunk,
@Lid{lua_dump} calls function @id{writer} @seeC{lua_Writer}
with the given @id{data}
to write them.

If @id{strip} is true,
the binary representation may not include all debug information
about the function,
to save space.

The value returned is the error code returned by the last
call to the writer;
@N{0 means} no errors.

This function does not pop the Lua function from the stack.

}

@APIEntry{int lua_error (lua_State *L);|
@apii{1,0,v}

Raises a Lua error,
using the value on the top of the stack as the error object.
This function does a long jump,
and therefore never returns
@seeC{luaL_error}.

}

@APIEntry{int lua_gc (lua_State *L, int what, ...);|
@apii{0,0,-}

Controls the garbage collector.

This function performs several tasks,
according to the value of the parameter @id{what}.
For options that need extra arguments,
they are listed after the option.
@description{

@item{@id{LUA_GCCOLLECT}|
Performs a full garbage-collection cycle.
}

@item{@id{LUA_GCSTOP}|
Stops the garbage collector.
}

@item{@id{LUA_GCRESTART}|
Restarts the garbage collector.
}

@item{@id{LUA_GCCOUNT}|
Returns the current amount of memory (in Kbytes) in use by Lua.
}

@item{@id{LUA_GCCOUNTB}|
Returns the remainder of dividing the current amount of bytes of
memory in use by Lua by 1024.
}

@item{@id{LUA_GCSTEP} @T{(int stepsize)}|
Performs an incremental step of garbage collection,
corresponding to the allocation of @id{stepsize} Kbytes.
}

@item{@id{LUA_GCISRUNNING}|
Returns a boolean that tells whether the collector is running
(i.e., not stopped).
}

@item{@id{LUA_GCINC} (int pause, int stepmul, stepsize)|
Changes the collector to incremental mode
with the given parameters @see{incmode}.
Returns the previous mode (@id{LUA_GCGEN} or @id{LUA_GCINC}).
}

@item{@id{LUA_GCGEN} (int minormul, int majormul)|
Changes the collector to generational mode
with the given parameters @see{genmode}.
Returns the previous mode (@id{LUA_GCGEN} or @id{LUA_GCINC}).
}

}
For more details about these options,
see @Lid{collectgarbage}.

}

@APIEntry{lua_Alloc lua_getallocf (lua_State *L, void **ud);|
@apii{0,0,-}

Returns the @x{memory-allocation function} of a given state.
If @id{ud} is not @id{NULL}, Lua stores in @T{*ud} the
opaque pointer given when the memory-allocator function was set.

}

@APIEntry{int lua_getfield (lua_State *L, int index, const char *k);|
@apii{0,1,e}

Pushes onto the stack the value @T{t[k]},
where @id{t} is the value at the given index.
As in Lua, this function may trigger a metamethod
for the @Q{index} event @see{metatable}.

Returns the type of the pushed value.

}

@APIEntry{void *lua_getextraspace (lua_State *L);|
@apii{0,0,-}

Returns a pointer to a raw memory area associated with the
given Lua state.
The application can use this area for any purpose;
Lua does not use it for anything.

Each new thread has this area initialized with a copy
of the area of the @x{main thread}.

By default, this area has the size of a pointer to void,
but you can recompile Lua with a different size for this area.
(See @id{LUA_EXTRASPACE} in @id{luaconf.h}.)

}

@APIEntry{int lua_getglobal (lua_State *L, const char *name);|
@apii{0,1,e}

Pushes onto the stack the value of the global @id{name}.
Returns the type of that value.

}

@APIEntry{int lua_geti (lua_State *L, int index, lua_Integer i);|
@apii{0,1,e}

Pushes onto the stack the value @T{t[i]},
where @id{t} is the value at the given index.
As in Lua, this function may trigger a metamethod
for the @Q{index} event @see{metatable}.

Returns the type of the pushed value.

}

@APIEntry{int lua_getmetatable (lua_State *L, int index);|
@apii{0,0|1,-}

If the value at the given index has a metatable,
the function pushes that metatable onto the stack and @N{returns 1}.
Otherwise,
the function @N{returns 0} and pushes nothing on the stack.

}

@APIEntry{int lua_gettable (lua_State *L, int index);|
@apii{1,1,e}

Pushes onto the stack the value @T{t[k]},
where @id{t} is the value at the given index
and @id{k} is the value on the top of the stack.

This function pops the key from the stack,
pushing the resulting value in its place.
As in Lua, this function may trigger a metamethod
for the @Q{index} event @see{metatable}.

Returns the type of the pushed value.

}

@APIEntry{int lua_gettop (lua_State *L);|
@apii{0,0,-}

Returns the index of the top element in the stack.
Because indices start @N{at 1},
this result is equal to the number of elements in the stack;
in particular, @N{0 means} an empty stack.

}

@APIEntry{int lua_getiuservalue (lua_State *L, int index, int n);|
@apii{0,1,-}

Pushes onto the stack the @id{n}-th user value associated with the
full userdata at the given index and
returns the type of the pushed value.

If the userdata does not have that value,
pushes @nil and returns @Lid{LUA_TNONE}.

}

@APIEntry{void lua_insert (lua_State *L, int index);|
@apii{1,1,-}

Moves the top element into the given valid index,
shifting up the elements above this index to open space.
This function cannot be called with a pseudo-index,
because a pseudo-index is not an actual stack position.

}

@APIEntry{typedef @ldots lua_Integer;|

The type of integers in Lua.

By default this type is @id{long long},
(usually a 64-bit two-complement integer),
but that can be changed to @id{long} or @id{int}
(usually a 32-bit two-complement integer).
(See @id{LUA_INT_TYPE} in @id{luaconf.h}.)

Lua also defines the constants
@defid{LUA_MININTEGER} and @defid{LUA_MAXINTEGER},
with the minimum and the maximum values that fit in this type.

}

@APIEntry{int lua_isboolean (lua_State *L, int index);|
@apii{0,0,-}

Returns 1 if the value at the given index is a boolean,
and @N{0 otherwise}.

}

@APIEntry{int lua_iscfunction (lua_State *L, int index);|
@apii{0,0,-}

Returns 1 if the value at the given index is a @N{C function},
and @N{0 otherwise}.

}

@APIEntry{int lua_isfunction (lua_State *L, int index);|
@apii{0,0,-}

Returns 1 if the value at the given index is a function
(either C or Lua), and @N{0 otherwise}.

}

@APIEntry{int lua_isinteger (lua_State *L, int index);|
@apii{0,0,-}

Returns 1 if the value at the given index is an integer
(that is, the value is a number and is represented as an integer),
and @N{0 otherwise}.

}

@APIEntry{int lua_islightuserdata (lua_State *L, int index);|
@apii{0,0,-}

Returns 1 if the value at the given index is a light userdata,
and @N{0 otherwise}.

}

@APIEntry{int lua_isnil (lua_State *L, int index);|
@apii{0,0,-}

Returns 1 if the value at the given index is @nil,
and @N{0 otherwise}.

}

@APIEntry{int lua_isnone (lua_State *L, int index);|
@apii{0,0,-}

Returns 1 if the given index is not valid,
and @N{0 otherwise}.

}

@APIEntry{int lua_isnoneornil (lua_State *L, int index);|
@apii{0,0,-}

Returns 1 if the given index is not valid
or if the value at this index is @nil,
and @N{0 otherwise}.

}

@APIEntry{int lua_isnumber (lua_State *L, int index);|
@apii{0,0,-}

Returns 1 if the value at the given index is a number
or a string convertible to a number,
and @N{0 otherwise}.

}

@APIEntry{int lua_isstring (lua_State *L, int index);|
@apii{0,0,-}

Returns 1 if the value at the given index is a string
or a number (which is always convertible to a string),
and @N{0 otherwise}.

}

@APIEntry{int lua_istable (lua_State *L, int index);|
@apii{0,0,-}

Returns 1 if the value at the given index is a table,
and @N{0 otherwise}.

}

@APIEntry{int lua_isthread (lua_State *L, int index);|
@apii{0,0,-}

Returns 1 if the value at the given index is a thread,
and @N{0 otherwise}.

}

@APIEntry{int lua_isuserdata (lua_State *L, int index);|
@apii{0,0,-}

Returns 1 if the value at the given index is a userdata
(either full or light), and @N{0 otherwise}.

}

@APIEntry{int lua_isyieldable (lua_State *L);|
@apii{0,0,-}

Returns 1 if the given coroutine can yield,
and @N{0 otherwise}.

}

@APIEntry{typedef @ldots lua_KContext;|

The type for continuation-function contexts.
It must be a numeric type.
This type is defined as @id{intptr_t}
when @id{intptr_t} is available,
so that it can store pointers too.
Otherwise, it is defined as @id{ptrdiff_t}.

}

@APIEntry{
typedef int (*lua_KFunction) (lua_State *L, int status, lua_KContext ctx);|

Type for continuation functions @see{continuations}.

}

@APIEntry{void lua_len (lua_State *L, int index);|
@apii{0,1,e}

Returns the length of the value at the given index.
It is equivalent to the @Char{#} operator in Lua @see{len-op} and
may trigger a metamethod for the @Q{length} event @see{metatable}.
The result is pushed on the stack.

}

@APIEntry{
int lua_load (lua_State *L,
              lua_Reader reader,
              void *data,
              const char *chunkname,
              const char *mode);|
@apii{0,1,-}

Loads a Lua chunk without running it.
If there are no errors,
@id{lua_load} pushes the compiled chunk as a Lua
function on top of the stack.
Otherwise, it pushes an error message.

The @id{lua_load} function uses a user-supplied @id{reader} function
to read the chunk @seeC{lua_Reader}.
The @id{data} argument is an opaque value passed to the reader function.

The @id{chunkname} argument gives a name to the chunk,
which is used for error messages and in debug information @see{debugI}.

@id{lua_load} automatically detects whether the chunk is text or binary
and loads it accordingly (see program @idx{luac}).
The string @id{mode} works as in function @Lid{load},
with the addition that
a @id{NULL} value is equivalent to the string @St{bt}.

@id{lua_load} uses the stack internally,
so the reader function must always leave the stack
unmodified when returning.

@id{lua_load} can return
@Lid{LUA_OK}, @Lid{LUA_ERRSYNTAX}, or @Lid{LUA_ERRMEM}.
The function may also return other values corresponding to
errors raised by the read function @see{statuscodes}.

If the resulting function has upvalues,
its first upvalue is set to the value of the @x{global environment}
stored at index @id{LUA_RIDX_GLOBALS} in the registry @see{registry}.
When loading main chunks,
this upvalue will be the @id{_ENV} variable @see{globalenv}.
Other upvalues are initialized with @nil.

}

@APIEntry{lua_State *lua_newstate (lua_Alloc f, void *ud);|
@apii{0,0,-}

Creates a new independent state and returns its main thread.
Returns @id{NULL} if it cannot create the state
(due to lack of memory).
The argument @id{f} is the @x{allocator function};
Lua will do all memory allocation for this state
through this function @seeF{lua_Alloc}.
The second argument, @id{ud}, is an opaque pointer that Lua
passes to the allocator in every call.

}

@APIEntry{void lua_newtable (lua_State *L);|
@apii{0,1,m}

Creates a new empty table and pushes it onto the stack.
It is equivalent to @T{lua_createtable(L, 0, 0)}.

}

@APIEntry{lua_State *lua_newthread (lua_State *L);|
@apii{0,1,m}

Creates a new thread, pushes it on the stack,
and returns a pointer to a @Lid{lua_State} that represents this new thread.
The new thread returned by this function shares with the original thread
its global environment,
but has an independent execution stack.

Threads are subject to garbage collection,
like any Lua object.

}

@APIEntry{void *lua_newuserdatauv (lua_State *L, size_t size, int nuvalue);|
@apii{0,1,m}

This function creates and pushes on the stack a new full userdata,
with @id{nuvalue} associated Lua values, called @id{user values},
plus an associated block of raw memory with @id{size} bytes.
(The user values can be set and read with the functions
@Lid{lua_setiuservalue} and @Lid{lua_getiuservalue}.)

The function returns the address of the block of memory.
Lua ensures that this address is valid as long as
the corresponding userdata is alive @see{GC}.
Moreover, if the userdata is marked for finalization @see{finalizers},
its address is valid at least until the call to its finalizer.

}

@APIEntry{int lua_next (lua_State *L, int index);|
@apii{1,2|0,v}

Pops a key from the stack,
and pushes a key@En{}value pair from the table at the given index,
the @Q{next} pair after the given key.
If there are no more elements in the table,
then @Lid{lua_next} returns 0 and pushes nothing.

A typical table traversal looks like this:
@verbatim{
/* table is in the stack at index 't' */
lua_pushnil(L);  /* first key */
while (lua_next(L, t) != 0) {
  /* uses 'key' (at index -2) and 'value' (at index -1) */
  printf("%s - %s\n",
         lua_typename(L, lua_type(L, -2)),
         lua_typename(L, lua_type(L, -1)));
  /* removes 'value'; keeps 'key' for next iteration */
  lua_pop(L, 1);
}
}

While traversing a table,
avoid calling @Lid{lua_tolstring} directly on a key,
unless you know that the key is actually a string.
Recall that @Lid{lua_tolstring} may change
the value at the given index;
this confuses the next call to @Lid{lua_next}.

This function may raise an error if the given key
is neither @nil nor present in the table.
See function @Lid{next} for the caveats of modifying
the table during its traversal.

}

@APIEntry{typedef @ldots lua_Number;|

The type of floats in Lua.

By default this type is double,
but that can be changed to a single float or a long double.
(See @id{LUA_FLOAT_TYPE} in @id{luaconf.h}.)

}

@APIEntry{int lua_numbertointeger (lua_Number n, lua_Integer *p);|

Tries to convert a Lua float to a Lua integer;
the float @id{n} must have an integral value.
If that value is within the range of Lua integers,
it is converted to an integer and assigned to @T{*p}.
The macro results in a boolean indicating whether the
conversion was successful.
(Note that this range test can be tricky to do
correctly without this macro, due to rounding.)

This macro may evaluate its arguments more than once.

}

@APIEntry{int lua_pcall (lua_State *L, int nargs, int nresults, int msgh);|
@apii{nargs + 1,nresults|1,-}

Calls a function (or a callable object) in protected mode.

Both @id{nargs} and @id{nresults} have the same meaning as
in @Lid{lua_call}.
If there are no errors during the call,
@Lid{lua_pcall} behaves exactly like @Lid{lua_call}.
However, if there is any error,
@Lid{lua_pcall} catches it,
pushes a single value on the stack (the error object),
and returns an error code.
Like @Lid{lua_call},
@Lid{lua_pcall} always removes the function
and its arguments from the stack.

If @id{msgh} is 0,
then the error object returned on the stack
is exactly the original error object.
Otherwise, @id{msgh} is the stack index of a
@emph{message handler}.
(This index cannot be a pseudo-index.)
In case of runtime errors,
this handler will be called with the error object
and its return value will be the object
returned on the stack by @Lid{lua_pcall}.

Typically, the message handler is used to add more debug
information to the error object, such as a stack traceback.
Such information cannot be gathered after the return of @Lid{lua_pcall},
since by then the stack has unwound.

The @Lid{lua_pcall} function returns one of the following status codes:
@Lid{LUA_OK}, @Lid{LUA_ERRRUN}, @Lid{LUA_ERRMEM}, or @Lid{LUA_ERRERR}.

}

@APIEntry{
int lua_pcallk (lua_State *L,
                int nargs,
                int nresults,
                int msgh,
                lua_KContext ctx,
                lua_KFunction k);|
@apii{nargs + 1,nresults|1,-}

This function behaves exactly like @Lid{lua_pcall},
except that it allows the called function to yield @see{continuations}.

}

@APIEntry{void lua_pop (lua_State *L, int n);|
@apii{n,0,-}

Pops @id{n} elements from the stack.

}

@APIEntry{void lua_pushboolean (lua_State *L, int b);|
@apii{0,1,-}

Pushes a boolean value with value @id{b} onto the stack.

}

@APIEntry{void lua_pushcclosure (lua_State *L, lua_CFunction fn, int n);|
@apii{n,1,m}

Pushes a new @N{C closure} onto the stack.
This function receives a pointer to a @N{C function}
and pushes onto the stack a Lua value of type @id{function} that,
when called, invokes the corresponding @N{C function}.
The parameter @id{n} tells how many upvalues this function will have
@see{c-closure}.

Any function to be callable by Lua must
follow the correct protocol to receive its parameters
and return its results @seeC{lua_CFunction}.

When a @N{C function} is created,
it is possible to associate some values with it,
the so called upvalues;
these upvalues are then accessible to the function whenever it is called.
This association is called a @x{@N{C closure}} @see{c-closure}.
To create a @N{C closure},
first the initial values for its upvalues must be pushed onto the stack.
(When there are multiple upvalues, the first value is pushed first.)
Then @Lid{lua_pushcclosure}
is called to create and push the @N{C function} onto the stack,
with the argument @id{n} telling how many values will be
associated with the function.
@Lid{lua_pushcclosure} also pops these values from the stack.

The maximum value for @id{n} is 255.

When @id{n} is zero,
this function creates a @def{light @N{C function}},
which is just a pointer to the @N{C function}.
In that case, it never raises a memory error.

}

@APIEntry{void lua_pushcfunction (lua_State *L, lua_CFunction f);|
@apii{0,1,-}

Pushes a @N{C function} onto the stack.
This function is equivalent to @Lid{lua_pushcclosure} with no upvalues.

}

@APIEntry{const char *lua_pushfstring (lua_State *L, const char *fmt, ...);|
@apii{0,1,v}

Pushes onto the stack a formatted string
and returns a pointer to this string @see{constchar}.
It is similar to the @ANSI{sprintf},
but has two important differences.
First,
you do not have to allocate space for the result;
the result is a Lua string and Lua takes care of memory allocation
(and deallocation, through garbage collection).
Second,
the conversion specifiers are quite restricted.
There are no flags, widths, or precisions.
The conversion specifiers can only be
@Char{%%} (inserts the character @Char{%}),
@Char{%s} (inserts a zero-terminated string, with no size restrictions),
@Char{%f} (inserts a @Lid{lua_Number}),
@Char{%I} (inserts a @Lid{lua_Integer}),
@Char{%p} (inserts a pointer),
@Char{%d} (inserts an @T{int}),
@Char{%c} (inserts an @T{int} as a one-byte character), and
@Char{%U} (inserts a @T{long int} as a @x{UTF-8} byte sequence).

This function may raise errors due to memory overflow
or an invalid conversion specifier.

}

@APIEntry{void lua_pushglobaltable (lua_State *L);|
@apii{0,1,-}

Pushes the @x{global environment} onto the stack.

}

@APIEntry{void lua_pushinteger (lua_State *L, lua_Integer n);|
@apii{0,1,-}

Pushes an integer with value @id{n} onto the stack.

}

@APIEntry{void lua_pushlightuserdata (lua_State *L, void *p);|
@apii{0,1,-}

Pushes a light userdata onto the stack.

Userdata represent @N{C values} in Lua.
A @def{light userdata} represents a pointer, a @T{void*}.
It is a value (like a number):
you do not create it, it has no individual metatable,
and it is not collected (as it was never created).
A light userdata is equal to @Q{any}
light userdata with the same @N{C address}.

}

@APIEntry{const char *lua_pushliteral (lua_State *L, const char *s);|
@apii{0,1,m}

This macro is equivalent to @Lid{lua_pushstring},
but should be used only when @id{s} is a literal string.
(Lua may optimize this case.)

}

@APIEntry{const char *lua_pushlstring (lua_State *L, const char *s, size_t len);|
@apii{0,1,m}

Pushes the string pointed to by @id{s} with size @id{len}
onto the stack.
Lua will make or reuse an internal copy of the given string,
so the memory at @id{s} can be freed or reused immediately after
the function returns.
The string can contain any binary data,
including @x{embedded zeros}.

Returns a pointer to the internal copy of the string @see{constchar}.

}

@APIEntry{void lua_pushnil (lua_State *L);|
@apii{0,1,-}

Pushes a nil value onto the stack.

}

@APIEntry{void lua_pushnumber (lua_State *L, lua_Number n);|
@apii{0,1,-}

Pushes a float with value @id{n} onto the stack.

}

@APIEntry{const char *lua_pushstring (lua_State *L, const char *s);|
@apii{0,1,m}

Pushes the zero-terminated string pointed to by @id{s}
onto the stack.
Lua will make or reuse an internal copy of the given string,
so the memory at @id{s} can be freed or reused immediately after
the function returns.

Returns a pointer to the internal copy of the string @see{constchar}.

If @id{s} is @id{NULL}, pushes @nil and returns @id{NULL}.

}

@APIEntry{int lua_pushthread (lua_State *L);|
@apii{0,1,-}

Pushes the thread represented by @id{L} onto the stack.
Returns 1 if this thread is the @x{main thread} of its state.

}

@APIEntry{void lua_pushvalue (lua_State *L, int index);|
@apii{0,1,-}

Pushes a copy of the element at the given index
onto the stack.

}

@APIEntry{
const char *lua_pushvfstring (lua_State *L,
                              const char *fmt,
                              va_list argp);|
@apii{0,1,v}

Equivalent to @Lid{lua_pushfstring}, except that it receives a @id{va_list}
instead of a variable number of arguments.

}

@APIEntry{int lua_rawequal (lua_State *L, int index1, int index2);|
@apii{0,0,-}

Returns 1 if the two values in indices @id{index1} and
@id{index2} are primitively equal
(that is, equal without calling the @idx{__eq} metamethod).
Otherwise @N{returns 0}.
Also @N{returns 0} if any of the indices are not valid.

}

@APIEntry{int lua_rawget (lua_State *L, int index);|
@apii{1,1,-}

Similar to @Lid{lua_gettable}, but does a raw access
(i.e., without metamethods).

}

@APIEntry{int lua_rawgeti (lua_State *L, int index, lua_Integer n);|
@apii{0,1,-}

Pushes onto the stack the value @T{t[n]},
where @id{t} is the table at the given index.
The access is raw,
that is, it does not use the @idx{__index} metavalue.

Returns the type of the pushed value.

}

@APIEntry{int lua_rawgetp (lua_State *L, int index, const void *p);|
@apii{0,1,-}

Pushes onto the stack the value @T{t[k]},
where @id{t} is the table at the given index and
@id{k} is the pointer @id{p} represented as a light userdata.
The access is raw;
that is, it does not use the @idx{__index} metavalue.

Returns the type of the pushed value.

}

@APIEntry{lua_Unsigned lua_rawlen (lua_State *L, int index);|
@apii{0,0,-}

Returns the raw @Q{length} of the value at the given index:
for strings, this is the string length;
for tables, this is the result of the length operator (@Char{#})
with no metamethods;
for userdata, this is the size of the block of memory allocated
for the userdata.
For other values, this call @N{returns 0}.

}

@APIEntry{void lua_rawset (lua_State *L, int index);|
@apii{2,0,m}

Similar to @Lid{lua_settable}, but does a raw assignment
(i.e., without metamethods).

}

@APIEntry{void lua_rawseti (lua_State *L, int index, lua_Integer i);|
@apii{1,0,m}

Does the equivalent of @T{t[i] = v},
where @id{t} is the table at the given index
and @id{v} is the value on the top of the stack.

This function pops the value from the stack.
The assignment is raw,
that is, it does not use the @idx{__newindex} metavalue.

}

@APIEntry{void lua_rawsetp (lua_State *L, int index, const void *p);|
@apii{1,0,m}

Does the equivalent of @T{t[p] = v},
where @id{t} is the table at the given index,
@id{p} is encoded as a light userdata,
and @id{v} is the value on the top of the stack.

This function pops the value from the stack.
The assignment is raw,
that is, it does not use the @idx{__newindex} metavalue.

}

@APIEntry{
typedef const char * (*lua_Reader) (lua_State *L,
                                    void *data,
                                    size_t *size);|

The reader function used by @Lid{lua_load}.
Every time @Lid{lua_load} needs another piece of the chunk,
it calls the reader,
passing along its @id{data} parameter.
The reader must return a pointer to a block of memory
with a new piece of the chunk
and set @id{size} to the block size.
The block must exist until the reader function is called again.
To signal the end of the chunk,
the reader must return @id{NULL} or set @id{size} to zero.
The reader function may return pieces of any size greater than zero.

}

@APIEntry{void lua_register (lua_State *L, const char *name, lua_CFunction f);|
@apii{0,0,e}

Sets the @N{C function} @id{f} as the new value of global @id{name}.
It is defined as a macro:
@verbatim{
#define lua_register(L,n,f) \
       (lua_pushcfunction(L, f), lua_setglobal(L, n))
}

}

@APIEntry{void lua_remove (lua_State *L, int index);|
@apii{1,0,-}

Removes the element at the given valid index,
shifting down the elements above this index to fill the gap.
This function cannot be called with a pseudo-index,
because a pseudo-index is not an actual stack position.

}

@APIEntry{void lua_replace (lua_State *L, int index);|
@apii{1,0,-}

Moves the top element into the given valid index
without shifting any element
(therefore replacing the value at that given index),
and then pops the top element.

}

@APIEntry{int lua_resetthread (lua_State *L);|
@apii{0,?,-}

Resets a thread, cleaning its call stack and closing all pending
to-be-closed variables.
Returns a status code:
@Lid{LUA_OK} for no errors in closing methods,
or an error status otherwise.
In case of error,
leaves the error object on the top of the stack,

}

@APIEntry{int lua_resume (lua_State *L, lua_State *from, int nargs,
                          int *nresults);|
@apii{?,?,-}

Starts and resumes a coroutine in the given thread @id{L}.

To start a coroutine,
you push the main function plus any arguments
onto the empty stack of the thread.
then you call @Lid{lua_resume},
with @id{nargs} being the number of arguments.
This call returns when the coroutine suspends or finishes its execution.
When it returns,
@id{*nresults} is updated and
the top of the stack contains
the @id{*nresults} values passed to @Lid{lua_yield}
or returned by the body function.
@Lid{lua_resume} returns
@Lid{LUA_YIELD} if the coroutine yields,
@Lid{LUA_OK} if the coroutine finishes its execution
without errors,
or an error code in case of errors @see{statuscodes}.
In case of errors,
the error object is on the top of the stack.

To resume a coroutine,
you remove the @id{*nresults} yielded values from its stack,
push the values to be passed as results from @id{yield},
and then call @Lid{lua_resume}.

The parameter @id{from} represents the coroutine that is resuming @id{L}.
If there is no such coroutine,
this parameter can be @id{NULL}.

}

@APIEntry{void lua_rotate (lua_State *L, int idx, int n);|
@apii{0,0,-}

Rotates the stack elements between the valid index @id{idx}
and the top of the stack.
The elements are rotated @id{n} positions in the direction of the top,
for a positive @id{n},
or @T{-n} positions in the direction of the bottom,
for a negative @id{n}.
The absolute value of @id{n} must not be greater than the size
of the slice being rotated.
This function cannot be called with a pseudo-index,
because a pseudo-index is not an actual stack position.

}

@APIEntry{void lua_setallocf (lua_State *L, lua_Alloc f, void *ud);|
@apii{0,0,-}

Changes the @x{allocator function} of a given state to @id{f}
with user data @id{ud}.

}

@APIEntry{void lua_setfield (lua_State *L, int index, const char *k);|
@apii{1,0,e}

Does the equivalent to @T{t[k] = v},
where @id{t} is the value at the given index
and @id{v} is the value on the top of the stack.

This function pops the value from the stack.
As in Lua, this function may trigger a metamethod
for the @Q{newindex} event @see{metatable}.

}

@APIEntry{void lua_setglobal (lua_State *L, const char *name);|
@apii{1,0,e}

Pops a value from the stack and
sets it as the new value of global @id{name}.

}

@APIEntry{void lua_seti (lua_State *L, int index, lua_Integer n);|
@apii{1,0,e}

Does the equivalent to @T{t[n] = v},
where @id{t} is the value at the given index
and @id{v} is the value on the top of the stack.

This function pops the value from the stack.
As in Lua, this function may trigger a metamethod
for the @Q{newindex} event @see{metatable}.

}

@APIEntry{int lua_setiuservalue (lua_State *L, int index, int n);|
@apii{1,0,-}

Pops a value from the stack and sets it as
the new @id{n}-th user value associated to the
full userdata at the given index.
Returns 0 if the userdata does not have that value.

}

@APIEntry{int lua_setmetatable (lua_State *L, int index);|
@apii{1,0,-}

Pops a table or @nil from the stack and
sets that value as the new metatable for the value at the given index.
(@nil means no metatable.)

(For historical reasons, this function returns an @id{int},
which now is always 1.)

}

@APIEntry{void lua_settable (lua_State *L, int index);|
@apii{2,0,e}

Does the equivalent to @T{t[k] = v},
where @id{t} is the value at the given index,
@id{v} is the value on the top of the stack,
and @id{k} is the value just below the top.

This function pops both the key and the value from the stack.
As in Lua, this function may trigger a metamethod
for the @Q{newindex} event @see{metatable}.

}

@APIEntry{void lua_settop (lua_State *L, int index);|
@apii{?,?,-}

Accepts any index, @N{or 0},
and sets the stack top to this index.
If the new top is greater than the old one,
then the new elements are filled with @nil.
If @id{index} @N{is 0}, then all stack elements are removed.

}

@APIEntry{void lua_setwarnf (lua_State *L, lua_WarnFunction f, void *ud);|
@apii{0,0,-}

Sets the @x{warning function} to be used by Lua to emit warnings
@see{lua_WarnFunction}.
The @id{ud} parameter sets the value @id{ud} passed to
the warning function.

}

@APIEntry{typedef struct lua_State lua_State;|

An opaque structure that points to a thread and indirectly
(through the thread) to the whole state of a Lua interpreter.
The Lua library is fully reentrant:
it has no global variables.
All information about a state is accessible through this structure.

A pointer to this structure must be passed as the first argument to
every function in the library, except to @Lid{lua_newstate},
which creates a Lua state from scratch.

}

@APIEntry{int lua_status (lua_State *L);|
@apii{0,0,-}

Returns the status of the thread @id{L}.

The status can be @Lid{LUA_OK} for a normal thread,
an error code if the thread finished the execution
of a @Lid{lua_resume} with an error,
or @Lid{LUA_YIELD} if the thread is suspended.

You can call functions only in threads with status @Lid{LUA_OK}.
You can resume threads with status @Lid{LUA_OK}
(to start a new coroutine) or @Lid{LUA_YIELD}
(to resume a coroutine).

}

@APIEntry{size_t lua_stringtonumber (lua_State *L, const char *s);|
@apii{0,1,-}

Converts the zero-terminated string @id{s} to a number,
pushes that number into the stack,
and returns the total size of the string,
that is, its length plus one.
The conversion can result in an integer or a float,
according to the lexical conventions of Lua @see{lexical}.
The string may have leading and trailing whitespaces and a sign.
If the string is not a valid numeral,
returns 0 and pushes nothing.
(Note that the result can be used as a boolean,
true if the conversion succeeds.)

}

@APIEntry{int lua_toboolean (lua_State *L, int index);|
@apii{0,0,-}

Converts the Lua value at the given index to a @N{C boolean}
value (@N{0 or 1}).
Like all tests in Lua,
@Lid{lua_toboolean} returns true for any Lua value
different from @false and @nil;
otherwise it returns false.
(If you want to accept only actual boolean values,
use @Lid{lua_isboolean} to test the value's type.)

}

@APIEntry{lua_CFunction lua_tocfunction (lua_State *L, int index);|
@apii{0,0,-}

Converts a value at the given index to a @N{C function}.
That value must be a @N{C function};
otherwise, returns @id{NULL}.

}

@APIEntry{void lua_toclose (lua_State *L, int index);|
@apii{0,0,m}

Marks the given index in the stack as a
to-be-closed @Q{variable} @see{to-be-closed}.
Like a to-be-closed variable in Lua,
the value at that index in the stack will be closed
when it goes out of scope.
Here, in the context of a C function,
to go out of scope means that the running function returns to Lua,
there is an error,
or the index is removed from the stack through
@Lid{lua_settop} or @Lid{lua_pop}.
An index marked as to-be-closed should not be removed from the stack
by any other function in the API except @Lid{lua_settop} or @Lid{lua_pop}.

This function should not be called for an index
that is equal to or below an active to-be-closed index.

In the case of an out-of-memory error,
the value in the given index is immediately closed,
as if it was already marked.

Note that, both in case of errors and of a regular return,
by the time the @idx{__close} metamethod runs,
the @N{C stack} was already unwound,
so that any automatic C variable declared in the calling function
will be out of scope.

}

@APIEntry{lua_Integer lua_tointeger (lua_State *L, int index);|
@apii{0,0,-}

Equivalent to @Lid{lua_tointegerx} with @id{isnum} equal to @id{NULL}.

}

@APIEntry{lua_Integer lua_tointegerx (lua_State *L, int index, int *isnum);|
@apii{0,0,-}

Converts the Lua value at the given index
to the signed integral type @Lid{lua_Integer}.
The Lua value must be an integer,
or a number or string convertible to an integer @see{coercion};
otherwise, @id{lua_tointegerx} @N{returns 0}.

If @id{isnum} is not @id{NULL},
its referent is assigned a boolean value that
indicates whether the operation succeeded.

}

@APIEntry{const char *lua_tolstring (lua_State *L, int index, size_t *len);|
@apii{0,0,m}

Converts the Lua value at the given index to a @N{C string}.
If @id{len} is not @id{NULL},
it sets @T{*len} with the string length.
The Lua value must be a string or a number;
otherwise, the function returns @id{NULL}.
If the value is a number,
then @id{lua_tolstring} also
@emph{changes the actual value in the stack to a string}.
(This change confuses @Lid{lua_next}
when @id{lua_tolstring} is applied to keys during a table traversal.)

@id{lua_tolstring} returns a pointer
to a string inside the Lua state @see{constchar}.
This string always has a zero (@Char{\0})
after its last character (as @N{in C}),
but can contain other zeros in its body.

}

@APIEntry{lua_Number lua_tonumber (lua_State *L, int index);|
@apii{0,0,-}

Equivalent to @Lid{lua_tonumberx} with @id{isnum} equal to @id{NULL}.

}

@APIEntry{lua_Number lua_tonumberx (lua_State *L, int index, int *isnum);|
@apii{0,0,-}

Converts the Lua value at the given index
to the @N{C type} @Lid{lua_Number} @seeC{lua_Number}.
The Lua value must be a number or a string convertible to a number
@see{coercion};
otherwise, @Lid{lua_tonumberx} @N{returns 0}.

If @id{isnum} is not @id{NULL},
its referent is assigned a boolean value that
indicates whether the operation succeeded.

}

@APIEntry{const void *lua_topointer (lua_State *L, int index);|
@apii{0,0,-}

Converts the value at the given index to a generic
@N{C pointer} (@T{void*}).
The value can be a userdata, a table, a thread, a string, or a function;
otherwise, @id{lua_topointer} returns @id{NULL}.
Different objects will give different pointers.
There is no way to convert the pointer back to its original value.

Typically this function is used only for hashing and debug information.

}

@APIEntry{const char *lua_tostring (lua_State *L, int index);|
@apii{0,0,m}

Equivalent to @Lid{lua_tolstring} with @id{len} equal to @id{NULL}.

}

@APIEntry{lua_State *lua_tothread (lua_State *L, int index);|
@apii{0,0,-}

Converts the value at the given index to a Lua thread
(represented as @T{lua_State*}).
This value must be a thread;
otherwise, the function returns @id{NULL}.

}

@APIEntry{void *lua_touserdata (lua_State *L, int index);|
@apii{0,0,-}

If the value at the given index is a full userdata,
returns its memory-block address.
If the value is a light userdata,
returns its value (a pointer).
Otherwise, returns @id{NULL}.

}

@APIEntry{int lua_type (lua_State *L, int index);|
@apii{0,0,-}

Returns the type of the value in the given valid index,
or @id{LUA_TNONE} for a non-valid but acceptable index.
The types returned by @Lid{lua_type} are coded by the following constants
defined in @id{lua.h}:
@defid{LUA_TNIL},
@defid{LUA_TNUMBER},
@defid{LUA_TBOOLEAN},
@defid{LUA_TSTRING},
@defid{LUA_TTABLE},
@defid{LUA_TFUNCTION},
@defid{LUA_TUSERDATA},
@defid{LUA_TTHREAD},
and
@defid{LUA_TLIGHTUSERDATA}.

}

@APIEntry{const char *lua_typename (lua_State *L, int tp);|
@apii{0,0,-}

Returns the name of the type encoded by the value @id{tp},
which must be one the values returned by @Lid{lua_type}.

}

@APIEntry{typedef @ldots lua_Unsigned;|

The unsigned version of @Lid{lua_Integer}.

}

@APIEntry{int lua_upvalueindex (int i);|
@apii{0,0,-}

Returns the pseudo-index that represents the @id{i}-th upvalue of
the running function @see{c-closure}.
@id{i} must be in the range @M{[1,256]}.

}

@APIEntry{lua_Number lua_version (lua_State *L);|
@apii{0,0,-}

Returns the version number of this core.

}

@APIEntry{
typedef void (*lua_WarnFunction) (void *ud, const char *msg, int tocont);|

The type of @x{warning function}s, called by Lua to emit warnings.
The first parameter is an opaque pointer
set by @Lid{lua_setwarnf}.
The second parameter is the warning message.
The third parameter is a boolean that
indicates whether the message is
to be continued by the message in the next call.

See @Lid{warn} for more details about warnings.

}

@APIEntry{
void lua_warning (lua_State *L, const char *msg, int tocont);|
@apii{0,0,-}

Emits a warning with the given message.
A message in a call with @id{tocont} true should be
continued in another call to this function.

See @Lid{warn} for more details about warnings.

}

@APIEntry{
typedef int (*lua_Writer) (lua_State *L,
                           const void* p,
                           size_t sz,
                           void* ud);|

The type of the writer function used by @Lid{lua_dump}.
Every time @Lid{lua_dump} produces another piece of chunk,
it calls the writer,
passing along the buffer to be written (@id{p}),
its size (@id{sz}),
and the @id{ud} parameter supplied to @Lid{lua_dump}.

The writer returns an error code:
@N{0 means} no errors;
any other value means an error and stops @Lid{lua_dump} from
calling the writer again.

}

@APIEntry{void lua_xmove (lua_State *from, lua_State *to, int n);|
@apii{?,?,-}

Exchange values between different threads of the same state.

This function pops @id{n} values from the stack @id{from},
and pushes them onto the stack @id{to}.

}

@APIEntry{int lua_yield (lua_State *L, int nresults);|
@apii{?,?,v}

This function is equivalent to @Lid{lua_yieldk},
but it has no continuation @see{continuations}.
Therefore, when the thread resumes,
it continues the function that called
the function calling @id{lua_yield}.
To avoid surprises,
this function should be called only in a tail call.

}


@APIEntry{
int lua_yieldk (lua_State *L,
                int nresults,
                lua_KContext ctx,
                lua_KFunction k);|
@apii{?,?,v}

Yields a coroutine (thread).

When a @N{C function} calls @Lid{lua_yieldk},
the running coroutine suspends its execution,
and the call to @Lid{lua_resume} that started this coroutine returns.
The parameter @id{nresults} is the number of values from the stack
that will be passed as results to @Lid{lua_resume}.

When the coroutine is resumed again,
Lua calls the given @x{continuation function} @id{k} to continue
the execution of the @N{C function} that yielded @see{continuations}.
This continuation function receives the same stack
from the previous function,
with the @id{n} results removed and
replaced by the arguments passed to @Lid{lua_resume}.
Moreover,
the continuation function receives the value @id{ctx}
that was passed to @Lid{lua_yieldk}.

Usually, this function does not return;
when the coroutine eventually resumes,
it continues executing the continuation function.
However, there is one special case,
which is when this function is called
from inside a line or a count hook @see{debugI}.
In that case, @id{lua_yieldk} should be called with no continuation
(probably in the form of @Lid{lua_yield}) and no results,
and the hook should return immediately after the call.
Lua will yield and,
when the coroutine resumes again,
it will continue the normal execution
of the (Lua) function that triggered the hook.

This function can raise an error if it is called from a thread
with a pending C call with no continuation function
(what is called a @emphx{C-call boundary}),
or it is called from a thread that is not running inside a resume
(typically the main thread).

}

}

@sect2{debugI| @title{The Debug Interface}

Lua has no built-in debugging facilities.
Instead, it offers a special interface
by means of functions and @emph{hooks}.
This interface allows the construction of different
kinds of debuggers, profilers, and other tools
that need @Q{inside information} from the interpreter.


@APIEntry{
typedef struct lua_Debug {
  int event;
  const char *name;           /* (n) */
  const char *namewhat;       /* (n) */
  const char *what;           /* (S) */
  const char *source;         /* (S) */
  size_t srclen;              /* (S) */
  int currentline;            /* (l) */
  int linedefined;            /* (S) */
  int lastlinedefined;        /* (S) */
  unsigned char nups;         /* (u) number of upvalues */
  unsigned char nparams;      /* (u) number of parameters */
  char isvararg;              /* (u) */
  char istailcall;            /* (t) */
  unsigned short ftransfer;   /* (r) index of first value transferred */
  unsigned short ntransfer;   /* (r) number of transferred values */
  char short_src[LUA_IDSIZE]; /* (S) */
  /* private part */
  @rep{other fields}
} lua_Debug;
|

A structure used to carry different pieces of
information about a function or an activation record.
@Lid{lua_getstack} fills only the private part
of this structure, for later use.
To fill the other fields of @Lid{lua_Debug} with useful information,
you must call @Lid{lua_getinfo}.

The fields of @Lid{lua_Debug} have the following meaning:
@description{

@item{@id{source}|
the source of the chunk that created the function.
If @T{source} starts with a @Char{@At},
it means that the function was defined in a file where
the file name follows the @Char{@At}.
If @T{source} starts with a @Char{=},
the remainder of its contents describes the source in a user-dependent manner.
Otherwise,
the function was defined in a string where
@T{source} is that string.
}

@item{@id{srclen}|
The length of the string @id{source}.
}

@item{@id{short_src}|
a @Q{printable} version of @T{source}, to be used in error messages.
}

@item{@id{linedefined}|
the line number where the definition of the function starts.
}

@item{@id{lastlinedefined}|
the line number where the definition of the function ends.
}

@item{@id{what}|
the string @T{"Lua"} if the function is a Lua function,
@T{"C"} if it is a @N{C function},
@T{"main"} if it is the main part of a chunk.
}

@item{@id{currentline}|
the current line where the given function is executing.
When no line information is available,
@T{currentline} is set to @num{-1}.
}

@item{@id{name}|
a reasonable name for the given function.
Because functions in Lua are first-class values,
they do not have a fixed name:
some functions can be the value of multiple global variables,
while others can be stored only in a table field.
The @T{lua_getinfo} function checks how the function was
called to find a suitable name.
If it cannot find a name,
then @id{name} is set to @id{NULL}.
}

@item{@id{namewhat}|
explains the @T{name} field.
The value of @T{namewhat} can be
@T{"global"}, @T{"local"}, @T{"method"},
@T{"field"}, @T{"upvalue"}, or @T{""} (the empty string),
according to how the function was called.
(Lua uses the empty string when no other option seems to apply.)
}

@item{@id{istailcall}|
true if this function invocation was called by a tail call.
In this case, the caller of this level is not in the stack.
}

@item{@id{nups}|
the number of upvalues of the function.
}

@item{@id{nparams}|
the number of parameters of the function
(always @N{0 for} @N{C functions}).
}

@item{@id{isvararg}|
true if the function is a vararg function
(always true for @N{C functions}).
}

@item{@id{ftransfer}|
the index in the stack of the first value being @Q{transferred},
that is, parameters in a call or return values in a return.
(The other values are in consecutive indices.)
Using this index, you can access and modify these values
through @Lid{lua_getlocal} and @Lid{lua_setlocal}.
This field is only meaningful during a
call hook, denoting the first parameter,
or a return hook, denoting the first value being returned.
(For call hooks, this value is always 1.)
}

@item{@id{ntransfer}|
The number of values being transferred (see previous item).
(For calls of Lua functions,
this value is always equal to @id{nparams}.)
}

}

}

@APIEntry{lua_Hook lua_gethook (lua_State *L);|
@apii{0,0,-}

Returns the current hook function.

}

@APIEntry{int lua_gethookcount (lua_State *L);|
@apii{0,0,-}

Returns the current hook count.

}

@APIEntry{int lua_gethookmask (lua_State *L);|
@apii{0,0,-}

Returns the current hook mask.

}

@APIEntry{int lua_getinfo (lua_State *L, const char *what, lua_Debug *ar);|
@apii{0|1,0|1|2,m}

Gets information about a specific function or function invocation.

To get information about a function invocation,
the parameter @id{ar} must be a valid activation record that was
filled by a previous call to @Lid{lua_getstack} or
given as argument to a hook @seeC{lua_Hook}.

To get information about a function, you push it onto the stack
and start the @id{what} string with the character @Char{>}.
(In that case,
@id{lua_getinfo} pops the function from the top of the stack.)
For instance, to know in which line a function @id{f} was defined,
you can write the following code:
@verbatim{
lua_Debug ar;
lua_getglobal(L, "f");  /* get global 'f' */
lua_getinfo(L, ">S", &ar);
printf("%d\n", ar.linedefined);
}

Each character in the string @id{what}
selects some fields of the structure @id{ar} to be filled or
a value to be pushed on the stack:
@description{

@item{@Char{n}| fills in the field @id{name} and @id{namewhat};
}

@item{@Char{S}|
fills in the fields @id{source}, @id{short_src},
@id{linedefined}, @id{lastlinedefined}, and @id{what};
}

@item{@Char{l}| fills in the field @id{currentline};
}

@item{@Char{t}| fills in the field @id{istailcall};
}

@item{@Char{u}| fills in the fields
@id{nups}, @id{nparams}, and @id{isvararg};
}

@item{@Char{f}|
pushes onto the stack the function that is
running at the given level;
}

@item{@Char{L}|
pushes onto the stack a table whose indices are the
numbers of the lines that are valid on the function.
(A @emph{valid line} is a line with some associated code,
that is, a line where you can put a break point.
Non-valid lines include empty lines and comments.)

If this option is given together with option @Char{f},
its table is pushed after the function.

This is the only option that can raise a memory error.
}

}

This function returns 0 to signal an invalid option in @id{what};
even then the valid options are handled correctly.

}

@APIEntry{const char *lua_getlocal (lua_State *L, const lua_Debug *ar, int n);|
@apii{0,0|1,-}

Gets information about a local variable or a temporary value
of a given activation record or a given function.

In the first case,
the parameter @id{ar} must be a valid activation record that was
filled by a previous call to @Lid{lua_getstack} or
given as argument to a hook @seeC{lua_Hook}.
The index @id{n} selects which local variable to inspect;
see @Lid{debug.getlocal} for details about variable indices
and names.

@Lid{lua_getlocal} pushes the variable's value onto the stack
and returns its name.

In the second case, @id{ar} must be @id{NULL} and the function
to be inspected must be on the top of the stack.
In this case, only parameters of Lua functions are visible
(as there is no information about what variables are active)
and no values are pushed onto the stack.

Returns @id{NULL} (and pushes nothing)
when the index is greater than
the number of active local variables.

}

@APIEntry{int lua_getstack (lua_State *L, int level, lua_Debug *ar);|
@apii{0,0,-}

Gets information about the interpreter runtime stack.

This function fills parts of a @Lid{lua_Debug} structure with
an identification of the @emph{activation record}
of the function executing at a given level.
@N{Level 0} is the current running function,
whereas level @M{n+1} is the function that has called level @M{n}
(except for tail calls, which do not count in the stack).
When called with a level greater than the stack depth,
@Lid{lua_getstack} returns 0;
otherwise it returns 1.

}

@APIEntry{const char *lua_getupvalue (lua_State *L, int funcindex, int n);|
@apii{0,0|1,-}

Gets information about the @id{n}-th upvalue
of the closure at index @id{funcindex}.
It pushes the upvalue's value onto the stack
and returns its name.
Returns @id{NULL} (and pushes nothing)
when the index @id{n} is greater than the number of upvalues.

See @Lid{debug.getupvalue} for more information about upvalues.

}

@APIEntry{typedef void (*lua_Hook) (lua_State *L, lua_Debug *ar);|

Type for debugging hook functions.

Whenever a hook is called, its @id{ar} argument has its field
@id{event} set to the specific event that triggered the hook.
Lua identifies these events with the following constants:
@defid{LUA_HOOKCALL}, @defid{LUA_HOOKRET},
@defid{LUA_HOOKTAILCALL}, @defid{LUA_HOOKLINE},
and @defid{LUA_HOOKCOUNT}.
Moreover, for line events, the field @id{currentline} is also set.
To get the value of any other field in @id{ar},
the hook must call @Lid{lua_getinfo}.

For call events, @id{event} can be @id{LUA_HOOKCALL},
the normal value, or @id{LUA_HOOKTAILCALL}, for a tail call;
in this case, there will be no corresponding return event.

While Lua is running a hook, it disables other calls to hooks.
Therefore, if a hook calls back Lua to execute a function or a chunk,
this execution occurs without any calls to hooks.

Hook functions cannot have continuations,
that is, they cannot call @Lid{lua_yieldk},
@Lid{lua_pcallk}, or @Lid{lua_callk} with a non-null @id{k}.

Hook functions can yield under the following conditions:
Only count and line events can yield;
to yield, a hook function must finish its execution
calling @Lid{lua_yield} with @id{nresults} equal to zero
(that is, with no values).

}

@APIEntry{int (lua_setcstacklimit) (lua_State *L, unsigned int limit);|
@apii{0,0,-}

Sets a new limit for the C stack.
This limit controls how deeply nested calls can go in Lua,
with the intent of avoiding a stack overflow.
Returns the old limit in case of success,
or zero in case of error.
For more details about this function,
see @Lid{debug.setcstacklimit},
its equivalent in the standard library.

}

@APIEntry{void lua_sethook (lua_State *L, lua_Hook f, int mask, int count);|
@apii{0,0,-}

Sets the debugging hook function.

Argument @id{f} is the hook function.
@id{mask} specifies on which events the hook will be called:
it is formed by a bitwise OR of the constants
@defid{LUA_MASKCALL},
@defid{LUA_MASKRET},
@defid{LUA_MASKLINE},
and @defid{LUA_MASKCOUNT}.
The @id{count} argument is only meaningful when the mask
includes @id{LUA_MASKCOUNT}.
For each event, the hook is called as explained below:
@description{

@item{The call hook| is called when the interpreter calls a function.
The hook is called just after Lua enters the new function.
}

@item{The return hook| is called when the interpreter returns from a function.
The hook is called just before Lua leaves the function.
}

@item{The line hook| is called when the interpreter is about to
start the execution of a new line of code,
or when it jumps back in the code (even to the same line).
This event only happens while Lua is executing a Lua function.
}

@item{The count hook| is called after the interpreter executes every
@T{count} instructions.
This event only happens while Lua is executing a Lua function.
}

}

Hooks are disabled by setting @id{mask} to zero.

}

@APIEntry{const char *lua_setlocal (lua_State *L, const lua_Debug *ar, int n);|
@apii{0|1,0,-}

Sets the value of a local variable of a given activation record.
It assigns the value on the top of the stack
to the variable and returns its name.
It also pops the value from the stack.

Returns @id{NULL} (and pops nothing)
when the index is greater than
the number of active local variables.

Parameters @id{ar} and @id{n} are as in the function @Lid{lua_getlocal}.

}

@APIEntry{const char *lua_setupvalue (lua_State *L, int funcindex, int n);|
@apii{0|1,0,-}

Sets the value of a closure's upvalue.
It assigns the value on the top of the stack
to the upvalue and returns its name.
It also pops the value from the stack.

Returns @id{NULL} (and pops nothing)
when the index @id{n} is greater than the number of upvalues.

Parameters @id{funcindex} and @id{n} are as in
the function @Lid{lua_getupvalue}.

}

@APIEntry{void *lua_upvalueid (lua_State *L, int funcindex, int n);|
@apii{0,0,-}

Returns a unique identifier for the upvalue numbered @id{n}
from the closure at index @id{funcindex}.

These unique identifiers allow a program to check whether different
closures share upvalues.
Lua closures that share an upvalue
(that is, that access a same external local variable)
will return identical ids for those upvalue indices.

Parameters @id{funcindex} and @id{n} are as in
the function @Lid{lua_getupvalue},
but @id{n} cannot be greater than the number of upvalues.

}

@APIEntry{
void lua_upvaluejoin (lua_State *L, int funcindex1, int n1,
                                    int funcindex2, int n2);|
@apii{0,0,-}

Make the @id{n1}-th upvalue of the Lua closure at index @id{funcindex1}
refer to the @id{n2}-th upvalue of the Lua closure at index @id{funcindex2}.

}

}

}


@C{-------------------------------------------------------------------------}
@sect1{auxlib|@title{The Auxiliary Library}

@simplesect{

@index{lauxlib.h}
The @def{auxiliary library} provides several convenient functions
to interface C with Lua.
While the basic API provides the primitive functions for all
interactions between C and Lua,
the auxiliary library provides higher-level functions for some
common tasks.

All functions and types from the auxiliary library
are defined in header file @id{lauxlib.h} and
have a prefix @id{luaL_}.

All functions in the auxiliary library are built on
top of the basic API,
and so they provide nothing that cannot be done with that API.
Nevertheless, the use of the auxiliary library ensures
more consistency to your code.


Several functions in the auxiliary library use internally some
extra stack slots.
When a function in the auxiliary library uses less than five slots,
it does not check the stack size;
it simply assumes that there are enough slots.

Several functions in the auxiliary library are used to
check @N{C function} arguments.
Because the error message is formatted for arguments
(e.g., @St{bad argument #1}),
you should not use these functions for other stack values.

Functions called @id{luaL_check*}
always raise an error if the check is not satisfied.

}


@sect2{@title{Functions and Types}

Here we list all functions and types from the auxiliary library
in alphabetical order.


@APIEntry{void luaL_addchar (luaL_Buffer *B, char c);|
@apii{?,?,m}

Adds the byte @id{c} to the buffer @id{B}
@seeC{luaL_Buffer}.

}

@APIEntry{
const void luaL_addgsub (luaL_Buffer *B, const char *s,
                         const char *p, const char *r);|
@apii{0,0,m}

Adds a copy of the string @id{s} to the buffer @id{B} @seeC{luaL_Buffer},
replacing any occurrence of the string @id{p}
with the string @id{r}.

}

@APIEntry{void luaL_addlstring (luaL_Buffer *B, const char *s, size_t l);|
@apii{?,?,m}

Adds the string pointed to by @id{s} with length @id{l} to
the buffer @id{B}
@seeC{luaL_Buffer}.
The string can contain @x{embedded zeros}.

}

@APIEntry{void luaL_addsize (luaL_Buffer *B, size_t n);|
@apii{?,?,-}

Adds to the buffer @id{B}
a string of length @id{n} previously copied to the
buffer area @seeC{luaL_prepbuffer}.

}

@APIEntry{void luaL_addstring (luaL_Buffer *B, const char *s);|
@apii{?,?,m}

Adds the zero-terminated string pointed to by @id{s}
to the buffer @id{B}
@seeC{luaL_Buffer}.

}

@APIEntry{void luaL_addvalue (luaL_Buffer *B);|
@apii{1,?,m}

Adds the value on the top of the stack
to the buffer @id{B}
@seeC{luaL_Buffer}.
Pops the value.

This is the only function on string buffers that can (and must)
be called with an extra element on the stack,
which is the value to be added to the buffer.

}

@APIEntry{
void luaL_argcheck (lua_State *L,
                    int cond,
                    int arg,
                    const char *extramsg);|
@apii{0,0,v}

Checks whether @id{cond} is true.
If it is not, raises an error with a standard message @seeF{luaL_argerror}.

}

@APIEntry{int luaL_argerror (lua_State *L, int arg, const char *extramsg);|
@apii{0,0,v}

Raises an error reporting a problem with argument @id{arg}
of the @N{C function} that called it,
using a standard message
that includes @id{extramsg} as a comment:
@verbatim{
bad argument #@rep{arg} to '@rep{funcname}' (@rep{extramsg})
}
This function never returns.

}

@APIEntry{
void luaL_argexpected (lua_State *L,
                       int cond,
                       int arg,
                       const char *tname);|
@apii{0,0,v}

Checks whether @id{cond} is true.
If it is not, raises an error about the type of the argument @id{arg}
with a standard message @seeF{luaL_typeerror}.

}

@APIEntry{typedef struct luaL_Buffer luaL_Buffer;|

Type for a @def{string buffer}.

A string buffer allows @N{C code} to build Lua strings piecemeal.
Its pattern of use is as follows:
@itemize{

@item{First declare a variable @id{b} of type @Lid{luaL_Buffer}.}

@item{Then initialize it with a call @T{luaL_buffinit(L, &b)}.}

@item{
Then add string pieces to the buffer calling any of
the @id{luaL_add*} functions.
}

@item{
Finish by calling @T{luaL_pushresult(&b)}.
This call leaves the final string on the top of the stack.
}

}

If you know beforehand the maximum size of the resulting string,
you can use the buffer like this:
@itemize{

@item{First declare a variable @id{b} of type @Lid{luaL_Buffer}.}

@item{Then initialize it and preallocate a space of
size @id{sz} with a call @T{luaL_buffinitsize(L, &b, sz)}.}

@item{Then produce the string into that space.}

@item{
Finish by calling @T{luaL_pushresultsize(&b, sz)},
where @id{sz} is the total size of the resulting string
copied into that space (which may be less than or
equal to the preallocated size).
}

}

During its normal operation,
a string buffer uses a variable number of stack slots.
So, while using a buffer, you cannot assume that you know where
the top of the stack is.
You can use the stack between successive calls to buffer operations
as long as that use is balanced;
that is,
when you call a buffer operation,
the stack is at the same level
it was immediately after the previous buffer operation.
(The only exception to this rule is @Lid{luaL_addvalue}.)
After calling @Lid{luaL_pushresult},
the stack is back to its level when the buffer was initialized,
plus the final string on its top.

}

@APIEntry{char *luaL_buffaddr (luaL_Buffer *B);|
@apii{0,0,-}

Returns the address of the current content of buffer @id{B}
@seeC{luaL_Buffer}.
Note that any addition to the buffer may invalidate this address.

}

@APIEntry{void luaL_buffinit (lua_State *L, luaL_Buffer *B);|
@apii{0,0,-}

Initializes a buffer @id{B}
@seeC{luaL_Buffer}.
This function does not allocate any space;
the buffer must be declared as a variable.

}

@APIEntry{size_t luaL_bufflen (luaL_Buffer *B);|
@apii{0,0,-}

Returns the length of the current content of buffer @id{B}
@seeC{luaL_Buffer}.

}

@APIEntry{char *luaL_buffinitsize (lua_State *L, luaL_Buffer *B, size_t sz);|
@apii{?,?,m}

Equivalent to the sequence
@Lid{luaL_buffinit}, @Lid{luaL_prepbuffsize}.

}

@APIEntry{void luaL_buffsub (luaL_Buffer *B, int n);|
@apii{0,0,-}

Removes @id{n} bytes from the the buffer @id{B}
@seeC{luaL_Buffer}.
The buffer must have at least that many bytes.

}

@APIEntry{int luaL_callmeta (lua_State *L, int obj, const char *e);|
@apii{0,0|1,e}

Calls a metamethod.

If the object at index @id{obj} has a metatable and this
metatable has a field @id{e},
this function calls this field passing the object as its only argument.
In this case this function returns true and pushes onto the
stack the value returned by the call.
If there is no metatable or no metamethod,
this function returns false without pushing any value on the stack.

}

@APIEntry{void luaL_checkany (lua_State *L, int arg);|
@apii{0,0,v}

Checks whether the function has an argument
of any type (including @nil) at position @id{arg}.

}

@APIEntry{lua_Integer luaL_checkinteger (lua_State *L, int arg);|
@apii{0,0,v}

Checks whether the function argument @id{arg} is an integer
(or can be converted to an integer)
and returns this integer.

}

@APIEntry{const char *luaL_checklstring (lua_State *L, int arg, size_t *l);|
@apii{0,0,v}

Checks whether the function argument @id{arg} is a string
and returns this string;
if @id{l} is not @id{NULL} fills its referent
with the string's length.

This function uses @Lid{lua_tolstring} to get its result,
so all conversions and caveats of that function apply here.

}

@APIEntry{lua_Number luaL_checknumber (lua_State *L, int arg);|
@apii{0,0,v}

Checks whether the function argument @id{arg} is a number
and returns this number converted to a @id{lua_Number}.

}

@APIEntry{
int luaL_checkoption (lua_State *L,
                      int arg,
                      const char *def,
                      const char *const lst[]);|
@apii{0,0,v}

Checks whether the function argument @id{arg} is a string and
searches for this string in the array @id{lst}
(which must be NULL-terminated).
Returns the index in the array where the string was found.
Raises an error if the argument is not a string or
if the string cannot be found.

If @id{def} is not @id{NULL},
the function uses @id{def} as a default value when
there is no argument @id{arg} or when this argument is @nil.

This is a useful function for mapping strings to @N{C enums}.
(The usual convention in Lua libraries is
to use strings instead of numbers to select options.)

}

@APIEntry{void luaL_checkstack (lua_State *L, int sz, const char *msg);|
@apii{0,0,v}

Grows the stack size to @T{top + sz} elements,
raising an error if the stack cannot grow to that size.
@id{msg} is an additional text to go into the error message
(or @id{NULL} for no additional text).

}

@APIEntry{const char *luaL_checkstring (lua_State *L, int arg);|
@apii{0,0,v}

Checks whether the function argument @id{arg} is a string
and returns this string.

This function uses @Lid{lua_tolstring} to get its result,
so all conversions and caveats of that function apply here.

}

@APIEntry{void luaL_checktype (lua_State *L, int arg, int t);|
@apii{0,0,v}

Checks whether the function argument @id{arg} has type @id{t}.
See @Lid{lua_type} for the encoding of types for @id{t}.

}

@APIEntry{void *luaL_checkudata (lua_State *L, int arg, const char *tname);|
@apii{0,0,v}

Checks whether the function argument @id{arg} is a userdata
of the type @id{tname} @seeC{luaL_newmetatable} and
returns the userdata's memory-block address @seeC{lua_touserdata}.

}

@APIEntry{void luaL_checkversion (lua_State *L);|
@apii{0,0,v}

Checks whether the code making the call and the Lua library being called
are using the same version of Lua and the same numeric types.

}

@APIEntry{int luaL_dofile (lua_State *L, const char *filename);|
@apii{0,?,m}

Loads and runs the given file.
It is defined as the following macro:
@verbatim{
(luaL_loadfile(L, filename) || lua_pcall(L, 0, LUA_MULTRET, 0))
}
It returns @Lid{LUA_OK} if there are no errors,
or an error code in case of errors @see{statuscodes}.

}

@APIEntry{int luaL_dostring (lua_State *L, const char *str);|
@apii{0,?,-}

Loads and runs the given string.
It is defined as the following macro:
@verbatim{
(luaL_loadstring(L, str) || lua_pcall(L, 0, LUA_MULTRET, 0))
}
It returns @Lid{LUA_OK} if there are no errors,
or an error code in case of errors @see{statuscodes}.

}

@APIEntry{int luaL_error (lua_State *L, const char *fmt, ...);|
@apii{0,0,v}

Raises an error.
The error message format is given by @id{fmt}
plus any extra arguments,
following the same rules of @Lid{lua_pushfstring}.
It also adds at the beginning of the message the file name and
the line number where the error occurred,
if this information is available.

This function never returns,
but it is an idiom to use it in @N{C functions}
as @T{return luaL_error(@rep{args})}.

}

@APIEntry{int luaL_execresult (lua_State *L, int stat);|
@apii{0,3,m}

This function produces the return values for
process-related functions in the standard library
(@Lid{os.execute} and @Lid{io.close}).

}

@APIEntry{
int luaL_fileresult (lua_State *L, int stat, const char *fname);|
@apii{0,1|3,m}

This function produces the return values for
file-related functions in the standard library
(@Lid{io.open}, @Lid{os.rename}, @Lid{file:seek}, etc.).

}

@APIEntry{int luaL_getmetafield (lua_State *L, int obj, const char *e);|
@apii{0,0|1,m}

Pushes onto the stack the field @id{e} from the metatable
of the object at index @id{obj} and returns the type of the pushed value.
If the object does not have a metatable,
or if the metatable does not have this field,
pushes nothing and returns @id{LUA_TNIL}.

}

@APIEntry{int luaL_getmetatable (lua_State *L, const char *tname);|
@apii{0,1,m}

Pushes onto the stack the metatable associated with the name @id{tname}
in the registry @seeC{luaL_newmetatable},
or @nil if there is no metatable associated with that name.
Returns the type of the pushed value.

}

@APIEntry{int luaL_getsubtable (lua_State *L, int idx, const char *fname);|
@apii{0,1,e}

Ensures that the value @T{t[fname]},
where @id{t} is the value at index @id{idx},
is a table,
and pushes that table onto the stack.
Returns true if it finds a previous table there
and false if it creates a new table.

}

@APIEntry{
const char *luaL_gsub (lua_State *L,
                       const char *s,
                       const char *p,
                       const char *r);|
@apii{0,1,m}

Creates a copy of string @id{s},
replacing any occurrence of the string @id{p}
with the string @id{r}.
Pushes the resulting string on the stack and returns it.

}

@APIEntry{lua_Integer luaL_len (lua_State *L, int index);|
@apii{0,0,e}

Returns the @Q{length} of the value at the given index
as a number;
it is equivalent to the @Char{#} operator in Lua @see{len-op}.
Raises an error if the result of the operation is not an integer.
(This case can only happen through metamethods.)

}

@APIEntry{
int luaL_loadbuffer (lua_State *L,
                     const char *buff,
                     size_t sz,
                     const char *name);|
@apii{0,1,-}

Equivalent to @Lid{luaL_loadbufferx} with @id{mode} equal to @id{NULL}.

}


@APIEntry{
int luaL_loadbufferx (lua_State *L,
                      const char *buff,
                      size_t sz,
                      const char *name,
                      const char *mode);|
@apii{0,1,-}

Loads a buffer as a Lua chunk.
This function uses @Lid{lua_load} to load the chunk in the
buffer pointed to by @id{buff} with size @id{sz}.

This function returns the same results as @Lid{lua_load}.
@id{name} is the chunk name,
used for debug information and error messages.
The string @id{mode} works as in the function @Lid{lua_load}.

}


@APIEntry{int luaL_loadfile (lua_State *L, const char *filename);|
@apii{0,1,m}

Equivalent to @Lid{luaL_loadfilex} with @id{mode} equal to @id{NULL}.

}

@APIEntry{int luaL_loadfilex (lua_State *L, const char *filename,
                                            const char *mode);|
@apii{0,1,m}

Loads a file as a Lua chunk.
This function uses @Lid{lua_load} to load the chunk in the file
named @id{filename}.
If @id{filename} is @id{NULL},
then it loads from the standard input.
The first line in the file is ignored if it starts with a @T{#}.

The string @id{mode} works as in the function @Lid{lua_load}.

This function returns the same results as @Lid{lua_load}
or @Lid{LUA_ERRFILE} for file-related errors.

As @Lid{lua_load}, this function only loads the chunk;
it does not run it.

}

@APIEntry{int luaL_loadstring (lua_State *L, const char *s);|
@apii{0,1,-}

Loads a string as a Lua chunk.
This function uses @Lid{lua_load} to load the chunk in
the zero-terminated string @id{s}.

This function returns the same results as @Lid{lua_load}.

Also as @Lid{lua_load}, this function only loads the chunk;
it does not run it.

}


@APIEntry{void luaL_newlib (lua_State *L, const luaL_Reg l[]);|
@apii{0,1,m}

Creates a new table and registers there
the functions in the list @id{l}.

It is implemented as the following macro:
@verbatim{
(luaL_newlibtable(L,l), luaL_setfuncs(L,l,0))
}
The array @id{l} must be the actual array,
not a pointer to it.

}

@APIEntry{void luaL_newlibtable (lua_State *L, const luaL_Reg l[]);|
@apii{0,1,m}

Creates a new table with a size optimized
to store all entries in the array @id{l}
(but does not actually store them).
It is intended to be used in conjunction with @Lid{luaL_setfuncs}
@seeF{luaL_newlib}.

It is implemented as a macro.
The array @id{l} must be the actual array,
not a pointer to it.

}

@APIEntry{int luaL_newmetatable (lua_State *L, const char *tname);|
@apii{0,1,m}

If the registry already has the key @id{tname},
returns 0.
Otherwise,
creates a new table to be used as a metatable for userdata,
adds to this new table the pair @T{__name = tname},
adds to the registry the pair @T{[tname] = new table},
and returns 1.

In both cases,
the function pushes onto the stack the final value associated
with @id{tname} in the registry.

}

@APIEntry{lua_State *luaL_newstate (void);|
@apii{0,0,-}

Creates a new Lua state.
It calls @Lid{lua_newstate} with an
allocator based on the @N{standard C} allocation functions
and then sets a warning function and a panic function @see{C-error}
that print messages to the standard error output.

Returns the new state,
or @id{NULL} if there is a @x{memory allocation error}.

}

@APIEntry{void luaL_openlibs (lua_State *L);|
@apii{0,0,e}

Opens all standard Lua libraries into the given state.

}

@APIEntry{
T luaL_opt (L, func, arg, dflt);|
@apii{0,0,-}

This macro is defined as follows:
@verbatim{
(lua_isnoneornil(L,(arg)) ? (dflt) : func(L,(arg)))
}
In words, if the argument @id{arg} is nil or absent,
the macro results in the default @id{dflt}.
Otherwise, it results in the result of calling @id{func}
with the state @id{L} and the argument index @id{arg} as
arguments.
Note that it evaluates the expression @id{dflt} only if needed.

}

@APIEntry{
lua_Integer luaL_optinteger (lua_State *L,
                             int arg,
                             lua_Integer d);|
@apii{0,0,v}

If the function argument @id{arg} is an integer
(or it is convertible to an integer),
returns this integer.
If this argument is absent or is @nil,
returns @id{d}.
Otherwise, raises an error.

}

@APIEntry{
const char *luaL_optlstring (lua_State *L,
                             int arg,
                             const char *d,
                             size_t *l);|
@apii{0,0,v}

If the function argument @id{arg} is a string,
returns this string.
If this argument is absent or is @nil,
returns @id{d}.
Otherwise, raises an error.

If @id{l} is not @id{NULL},
fills its referent with the result's length.
If the result is @id{NULL}
(only possible when returning @id{d} and @T{d == NULL}),
its length is considered zero.

This function uses @Lid{lua_tolstring} to get its result,
so all conversions and caveats of that function apply here.

}

@APIEntry{lua_Number luaL_optnumber (lua_State *L, int arg, lua_Number d);|
@apii{0,0,v}

If the function argument @id{arg} is a number,
returns this number as a @id{lua_Number}.
If this argument is absent or is @nil,
returns @id{d}.
Otherwise, raises an error.

}

@APIEntry{
const char *luaL_optstring (lua_State *L,
                            int arg,
                            const char *d);|
@apii{0,0,v}

If the function argument @id{arg} is a string,
returns this string.
If this argument is absent or is @nil,
returns @id{d}.
Otherwise, raises an error.

}

@APIEntry{char *luaL_prepbuffer (luaL_Buffer *B);|
@apii{?,?,m}

Equivalent to @Lid{luaL_prepbuffsize}
with the predefined size @defid{LUAL_BUFFERSIZE}.

}

@APIEntry{char *luaL_prepbuffsize (luaL_Buffer *B, size_t sz);|
@apii{?,?,m}

Returns an address to a space of size @id{sz}
where you can copy a string to be added to buffer @id{B}
@seeC{luaL_Buffer}.
After copying the string into this space you must call
@Lid{luaL_addsize} with the size of the string to actually add
it to the buffer.

}

@APIEntry{void luaL_pushfail (lua_State *L);|
@apii{0,1,-}

Pushes the @fail value onto the stack @see{libraries}.

}

@APIEntry{void luaL_pushresult (luaL_Buffer *B);|
@apii{?,1,m}

Finishes the use of buffer @id{B} leaving the final string on
the top of the stack.

}

@APIEntry{void luaL_pushresultsize (luaL_Buffer *B, size_t sz);|
@apii{?,1,m}

Equivalent to the sequence @Lid{luaL_addsize}, @Lid{luaL_pushresult}.

}

@APIEntry{int luaL_ref (lua_State *L, int t);|
@apii{1,0,m}

Creates and returns a @def{reference},
in the table at index @id{t},
for the object on the top of the stack (and pops the object).

A reference is a unique integer key.
As long as you do not manually add integer keys into the table @id{t},
@Lid{luaL_ref} ensures the uniqueness of the key it returns.
You can retrieve an object referred by the reference @id{r}
by calling @T{lua_rawgeti(L, t, r)}.
The function @Lid{luaL_unref} frees a reference.

If the object on the top of the stack is @nil,
@Lid{luaL_ref} returns the constant @defid{LUA_REFNIL}.
The constant @defid{LUA_NOREF} is guaranteed to be different
from any reference returned by @Lid{luaL_ref}.

}

@APIEntry{
typedef struct luaL_Reg {
  const char *name;
  lua_CFunction func;
} luaL_Reg;
|

Type for arrays of functions to be registered by
@Lid{luaL_setfuncs}.
@id{name} is the function name and @id{func} is a pointer to
the function.
Any array of @Lid{luaL_Reg} must end with a sentinel entry
in which both @id{name} and @id{func} are @id{NULL}.

}

@APIEntry{
void luaL_requiref (lua_State *L, const char *modname,
                    lua_CFunction openf, int glb);|
@apii{0,1,e}

If @T{package.loaded[modname]} is not true,
calls the function @id{openf} with the string @id{modname} as an argument
and sets the call result to @T{package.loaded[modname]},
as if that function has been called through @Lid{require}.

If @id{glb} is true,
also stores the module into the global @id{modname}.

Leaves a copy of the module on the stack.

}

@APIEntry{void luaL_setfuncs (lua_State *L, const luaL_Reg *l, int nup);|
@apii{nup,0,m}

Registers all functions in the array @id{l}
@seeC{luaL_Reg} into the table on the top of the stack
(below optional upvalues, see next).

When @id{nup} is not zero,
all functions are created with @id{nup} upvalues,
initialized with copies of the @id{nup} values
previously pushed on the stack
on top of the library table.
These values are popped from the stack after the registration.

}

@APIEntry{void luaL_setmetatable (lua_State *L, const char *tname);|
@apii{0,0,-}

Sets the metatable of the object on the top of the stack
as the metatable associated with name @id{tname}
in the registry @seeC{luaL_newmetatable}.

}

@APIEntry{
typedef struct luaL_Stream {
  FILE *f;
  lua_CFunction closef;
} luaL_Stream;
|

The standard representation for @x{file handles}
used by the standard I/O library.

A file handle is implemented as a full userdata,
with a metatable called @id{LUA_FILEHANDLE}
(where @id{LUA_FILEHANDLE} is a macro with the actual metatable's name).
The metatable is created by the I/O library
@seeF{luaL_newmetatable}.

This userdata must start with the structure @id{luaL_Stream};
it can contain other data after this initial structure.
The field @id{f} points to the corresponding C stream
(or it can be @id{NULL} to indicate an incompletely created handle).
The field @id{closef} points to a Lua function
that will be called to close the stream
when the handle is closed or collected;
this function receives the file handle as its sole argument and
must return either a true value, in case of success,
or a false value plus an error message, in case of error.
Once Lua calls this field,
it changes the field value to @id{NULL}
to signal that the handle is closed.

}

@APIEntry{void *luaL_testudata (lua_State *L, int arg, const char *tname);|
@apii{0,0,m}

This function works like @Lid{luaL_checkudata},
except that, when the test fails,
it returns @id{NULL} instead of raising an error.

}

@APIEntry{const char *luaL_tolstring (lua_State *L, int idx, size_t *len);|
@apii{0,1,e}

Converts any Lua value at the given index to a @N{C string}
in a reasonable format.
The resulting string is pushed onto the stack and also
returned by the function @see{constchar}.
If @id{len} is not @id{NULL},
the function also sets @T{*len} with the string length.

If the value has a metatable with a @idx{__tostring} field,
then @id{luaL_tolstring} calls the corresponding metamethod
with the value as argument,
and uses the result of the call as its result.

}

@APIEntry{
void luaL_traceback (lua_State *L, lua_State *L1, const char *msg,
                     int level);|
@apii{0,1,m}

Creates and pushes a traceback of the stack @id{L1}.
If @id{msg} is not @id{NULL}, it is appended
at the beginning of the traceback.
The @id{level} parameter tells at which level
to start the traceback.

}

@APIEntry{const char *luaL_typeerror (lua_State *L,
                                      int arg,
                                      const char *tname);|
@apii{0,0,v}

Raises a type error for the argument @id{arg}
of the @N{C function} that called it,
using a standard message;
@id{tname} is a @Q{name} for the expected type.
This function never returns.

}

@APIEntry{const char *luaL_typename (lua_State *L, int index);|
@apii{0,0,-}

Returns the name of the type of the value at the given index.

}

@APIEntry{void luaL_unref (lua_State *L, int t, int ref);|
@apii{0,0,-}

Releases the reference @id{ref} from the table at index @id{t}
@seeC{luaL_ref}.
The entry is removed from the table,
so that the referred object can be collected.
The reference @id{ref} is also freed to be used again.

If @id{ref} is @Lid{LUA_NOREF} or @Lid{LUA_REFNIL},
@Lid{luaL_unref} does nothing.

}

@APIEntry{void luaL_where (lua_State *L, int lvl);|
@apii{0,1,m}

Pushes onto the stack a string identifying the current position
of the control at level @id{lvl} in the call stack.
Typically this string has the following format:
@verbatim{
@rep{chunkname}:@rep{currentline}:
}
@N{Level 0} is the running function,
@N{level 1} is the function that called the running function,
etc.

This function is used to build a prefix for error messages.

}

}

}


@C{-------------------------------------------------------------------------}
@sect1{libraries| @title{The Standard Libraries}

@simplesect{

The standard Lua libraries provide useful functions
that are implemented @N{in C} through the @N{C API}.
Some of these functions provide essential services to the language
(e.g., @Lid{type} and @Lid{getmetatable});
others provide access to outside services (e.g., I/O);
and others could be implemented in Lua itself,
but that for different reasons
deserve an implementation in C (e.g., @Lid{table.sort}).

All libraries are implemented through the official @N{C API}
and are provided as separate @N{C modules}.
Unless otherwise noted,
these library functions do not adjust its number of arguments
to its expected parameters.
For instance, a function documented as @T{foo(arg)}
should not be called without an argument.

The notation @fail means a false value representing
some kind of failure.
(Currently, @fail is equal to @nil,
but that may change in future versions.
The recommendation is to always test the success of these functions
with @T{(not status)}, instead of @T{(status == nil)}.)


Currently, Lua has the following standard libraries:
@itemize{

@item{@link{predefined|basic library};}

@item{@link{corolib|coroutine library};}

@item{@link{packlib|package library};}

@item{@link{strlib|string manipulation};}

@item{@link{utf8|basic UTF-8 support};}

@item{@link{tablib|table manipulation};}

@item{@link{mathlib|mathematical functions} (sin, log, etc.);}

@item{@link{iolib|input and output};}

@item{@link{oslib|operating system facilities};}

@item{@link{debuglib|debug facilities}.}

}
Except for the basic and the package libraries,
each library provides all its functions as fields of a global table
or as methods of its objects.

To have access to these libraries,
the @N{C host} program should call the @Lid{luaL_openlibs} function,
which opens all standard libraries.
Alternatively,
the host program can open them individually by using
@Lid{luaL_requiref} to call
@defid{luaopen_base} (for the basic library),
@defid{luaopen_package} (for the package library),
@defid{luaopen_coroutine} (for the coroutine library),
@defid{luaopen_string} (for the string library),
@defid{luaopen_utf8} (for the UTF-8 library),
@defid{luaopen_table} (for the table library),
@defid{luaopen_math} (for the mathematical library),
@defid{luaopen_io} (for the I/O library),
@defid{luaopen_os} (for the operating system library),
and @defid{luaopen_debug} (for the debug library).
These functions are declared in @defid{lualib.h}.

}


@sect2{predefined| @title{Basic Functions}

The basic library provides core functions to Lua.
If you do not include this library in your application,
you should check carefully whether you need to provide
implementations for some of its facilities.


@LibEntry{assert (v [, message])|

Raises an error if
the value of its argument @id{v} is false (i.e., @nil or @false);
otherwise, returns all its arguments.
In case of error,
@id{message} is the error object;
when absent, it defaults to @St{assertion failed!}

}

@LibEntry{collectgarbage ([opt [, arg]])|

This function is a generic interface to the garbage collector.
It performs different functions according to its first argument, @id{opt}:
@description{

@item{@St{collect}|
Performs a full garbage-collection cycle.
This is the default option.
}

@item{@St{stop}|
Stops automatic execution of the garbage collector.
The collector will run only when explicitly invoked,
until a call to restart it.
}

@item{@St{restart}|
Restarts automatic execution of the garbage collector.
}

@item{@St{count}|
Returns the total memory in use by Lua in Kbytes.
The value has a fractional part,
so that it multiplied by 1024
gives the exact number of bytes in use by Lua.
}

@item{@St{step}|
Performs a garbage-collection step.
The step @Q{size} is controlled by @id{arg}.
With a zero value,
the collector will perform one basic (indivisible) step.
For non-zero values,
the collector will perform as if that amount of memory
(in Kbytes) had been allocated by Lua.
Returns @true if the step finished a collection cycle.
}

@item{@St{isrunning}|
Returns a boolean that tells whether the collector is running
(i.e., not stopped).
}

@item{@St{incremental}|
Change the collector mode to incremental.
This option can be followed by three numbers:
the garbage-collector pause,
the step multiplier,
and the step size @see{incmode}.
A zero means to not change that value.
}

@item{@St{generational}|
Change the collector mode to generational.
This option can be followed by two numbers:
the garbage-collector minor multiplier
and the major multiplier @see{genmode}.
A zero means to not change that value.
}

}
See @See{GC} for more details about garbage collection
and some of these options.

}

@LibEntry{dofile ([filename])|
Opens the named file and executes its content as a Lua chunk.
When called without arguments,
@id{dofile} executes the content of the standard input (@id{stdin}).
Returns all values returned by the chunk.
In case of errors, @id{dofile} propagates the error
to its caller.
(That is, @id{dofile} does not run in protected mode.)

}

@LibEntry{error (message [, level])|
Raises an error @see{error} with @{message} as the error object.
This function never returns.

Usually, @id{error} adds some information about the error position
at the beginning of the message, if the message is a string.
The @id{level} argument specifies how to get the error position.
With @N{level 1} (the default), the error position is where the
@id{error} function was called.
@N{Level 2} points the error to where the function
that called @id{error} was called; and so on.
Passing a @N{level 0} avoids the addition of error position information
to the message.

}

@LibEntry{_G|
A global variable (not a function) that
holds the @x{global environment} @see{globalenv}.
Lua itself does not use this variable;
changing its value does not affect any environment,
nor vice versa.

}

@LibEntry{getmetatable (object)|

If @id{object} does not have a metatable, returns @nil.
Otherwise,
if the object's metatable has a @idx{__metatable} field,
returns the associated value.
Otherwise, returns the metatable of the given object.

}

@LibEntry{ipairs (t)|

Returns three values (an iterator function, the table @id{t}, and 0)
so that the construction
@verbatim{
for i,v in ipairs(t) do @rep{body} end
}
will iterate over the key@En{}value pairs
(@T{1,t[1]}), (@T{2,t[2]}), @ldots,
up to the first absent index.

}

@LibEntry{load (chunk [, chunkname [, mode [, env]]])|

Loads a chunk.

If @id{chunk} is a string, the chunk is this string.
If @id{chunk} is a function,
@id{load} calls it repeatedly to get the chunk pieces.
Each call to @id{chunk} must return a string that concatenates
with previous results.
A return of an empty string, @nil, or no value signals the end of the chunk.

If there are no syntactic errors,
@id{load} returns the compiled chunk as a function;
otherwise, it returns @fail plus the error message.

When you load a main chunk,
the resulting function will always have exactly one upvalue,
the @id{_ENV} variable @see{globalenv}.
However,
when you load a binary chunk created from a function @seeF{string.dump},
the resulting function can have an arbitrary number of upvalues,
and there is no guarantee that its first upvalue will be
the @id{_ENV} variable.
(A non-main function may not even have an @id{_ENV} upvalue.)

Regardless, if the resulting function has any upvalues,
its first upvalue is set to the value of @id{env},
if that parameter is given,
or to the value of the @x{global environment}.
Other upvalues are initialized with @nil.
All upvalues are fresh, that is,
they are not shared with any other function.

@id{chunkname} is used as the name of the chunk for error messages
and debug information @see{debugI}.
When absent,
it defaults to @id{chunk}, if @id{chunk} is a string,
or to @St{=(load)} otherwise.

The string @id{mode} controls whether the chunk can be text or binary
(that is, a precompiled chunk).
It may be the string @St{b} (only @x{binary chunk}s),
@St{t} (only text chunks),
or @St{bt} (both binary and text).
The default is @St{bt}.

It is safe to load malformed binary chunks;
@id{load} signals an appropriate error.
However,
Lua does not check the consistency of the code inside binary chunks;
running maliciously crafted bytecode can crash the interpreter.

}

@LibEntry{loadfile ([filename [, mode [, env]]])|

Similar to @Lid{load},
but gets the chunk from file @id{filename}
or from the standard input,
if no file name is given.

}

@LibEntry{next (table [, index])|

Allows a program to traverse all fields of a table.
Its first argument is a table and its second argument
is an index in this table.
A call to @id{next} returns the next index of the table
and its associated value.
When called with @nil as its second argument,
@id{next} returns an initial index
and its associated value.
When called with the last index,
or with @nil in an empty table,
@id{next} returns @nil.
If the second argument is absent, then it is interpreted as @nil.
In particular,
you can use @T{next(t)} to check whether a table is empty.

The order in which the indices are enumerated is not specified,
@emph{even for numeric indices}.
(To traverse a table in numerical order,
use a numerical @Rw{for}.)

The behavior of @id{next} is undefined if,
during the traversal,
you assign any value to a non-existent field in the table.
You may however modify existing fields.
In particular, you may set existing fields to nil.

}

@LibEntry{pairs (t)|

If @id{t} has a metamethod @idx{__pairs},
calls it with @id{t} as argument and returns the first three
results from the call.

Otherwise,
returns three values: the @Lid{next} function, the table @id{t}, and @nil,
so that the construction
@verbatim{
for k,v in pairs(t) do @rep{body} end
}
will iterate over all key@En{}value pairs of table @id{t}.

See function @Lid{next} for the caveats of modifying
the table during its traversal.

}

@LibEntry{pcall (f [, arg1, @Cdots])|

Calls the function @id{f} with
the given arguments in @emphx{protected mode}.
This means that any error @N{inside @T{f}} is not propagated;
instead, @id{pcall} catches the error
and returns a status code.
Its first result is the status code (a boolean),
which is true if the call succeeds without errors.
In such case, @id{pcall} also returns all results from the call,
after this first result.
In case of any error, @id{pcall} returns @false plus the error object.
Note that errors caught by @id{pcall} do not call a message handler.

}

@LibEntry{print (@Cdots)|
Receives any number of arguments
and prints their values to @id{stdout},
converting each argument to a string
following the same rules of @Lid{tostring}.

The function @id{print} is not intended for formatted output,
but only as a quick way to show a value,
for instance for debugging.
For complete control over the output,
use @Lid{string.format} and @Lid{io.write}.

}

@LibEntry{rawequal (v1, v2)|
Checks whether @id{v1} is equal to @id{v2},
without invoking the @idx{__eq} metamethod.
Returns a boolean.

}

@LibEntry{rawget (table, index)|
Gets the real value of @T{table[index]},
without using the @idx{__index} metavalue.
@id{table} must be a table;
@id{index} may be any value.

}

@LibEntry{rawlen (v)|
Returns the length of the object @id{v},
which must be a table or a string,
without invoking the @idx{__len} metamethod.
Returns an integer.

}

@LibEntry{rawset (table, index, value)|
Sets the real value of @T{table[index]} to @id{value},
without using the @idx{__newindex} metavalue.
@id{table} must be a table,
@id{index} any value different from @nil and @x{NaN},
and @id{value} any Lua value.

This function returns @id{table}.

}

@LibEntry{select (index, @Cdots)|

If @id{index} is a number,
returns all arguments after argument number @id{index};
a negative number indexes from the end (@num{-1} is the last argument).
Otherwise, @id{index} must be the string @T{"#"},
and @id{select} returns the total number of extra arguments it received.

}

@LibEntry{setmetatable (table, metatable)|

Sets the metatable for the given table.
If @id{metatable} is @nil,
removes the metatable of the given table.
If the original metatable has a @idx{__metatable} field,
raises an error.

This function returns @id{table}.

To change the metatable of other types from Lua code,
you must use the @link{debuglib|debug library}.

}

@LibEntry{tonumber (e [, base])|

When called with no @id{base},
@id{tonumber} tries to convert its argument to a number.
If the argument is already a number or
a string convertible to a number,
then @id{tonumber} returns this number;
otherwise, it returns @fail.

The conversion of strings can result in integers or floats,
according to the lexical conventions of Lua @see{lexical}.
The string may have leading and trailing spaces and a sign.

When called with @id{base},
then @id{e} must be a string to be interpreted as
an integer numeral in that base.
The base may be any integer between 2 and 36, inclusive.
In bases @N{above 10}, the letter @Char{A} (in either upper or lower case)
@N{represents 10}, @Char{B} @N{represents 11}, and so forth,
with @Char{Z} representing 35.
If the string @id{e} is not a valid numeral in the given base,
the function returns @fail.

}

@LibEntry{tostring (v)|

Receives a value of any type and
converts it to a string in a human-readable format.

If the metatable of @id{v} has a @idx{__tostring} field,
then @id{tostring} calls the corresponding value
with @id{v} as argument,
and uses the result of the call as its result.
Otherwise, if the metatable of @id{v} has a @idx{__name} field
with a string value,
@id{tostring} may use that string in its final result.

For complete control of how numbers are converted,
use @Lid{string.format}.

}

@LibEntry{type (v)|

Returns the type of its only argument, coded as a string.
The possible results of this function are
@St{nil} (a string, not the value @nil),
@St{number},
@St{string},
@St{boolean},
@St{table},
@St{function},
@St{thread},
and @St{userdata}.

}

@LibEntry{_VERSION|

A global variable (not a function) that
holds a string containing the running Lua version.
The current value of this variable is @St{Lua 5.4}.

}

@LibEntry{warn (msg1, @Cdots)|

Emits a warning with a message composed by the concatenation
of all its arguments (which should be strings).

By convention,
a one-piece message starting with @Char{@At}
is intended to be a @emph{control message},
which is a message to the warning system itself.
In particular, the standard warning function in Lua
recognizes the control messages @St{@At{}off},
to stop the emission of warnings,
and @St{@At{}on}, to (re)start the emission;
it ignores unknown control messages.

}

@LibEntry{xpcall (f, msgh [, arg1, @Cdots])|

This function is similar to @Lid{pcall},
except that it sets a new @x{message handler} @id{msgh}.

}

}

@sect2{corolib| @title{Coroutine Manipulation}

This library comprises the operations to manipulate coroutines,
which come inside the table @defid{coroutine}.
See @See{coroutine} for a general description of coroutines.


@LibEntry{coroutine.close (co)|

Closes coroutine @id{co},
that is,
closes all its pending to-be-closed variables
and puts the coroutine in a dead state.
The given coroutine must be dead or suspended.
In case of error closing some variable,
returns @false plus the error object;
otherwise returns @true.

}

@LibEntry{coroutine.create (f)|

Creates a new coroutine, with body @id{f}.
@id{f} must be a function.
Returns this new coroutine,
an object with type @T{"thread"}.

}

@LibEntry{coroutine.isyieldable ([co])|

Returns true when the coroutine @id{co} can yield.
The default for @id{co} is the running coroutine.

A coroutine is yieldable if it is not the main thread and
it is not inside a non-yieldable @N{C function}.

}

@LibEntry{coroutine.resume (co [, val1, @Cdots])|

Starts or continues the execution of coroutine @id{co}.
The first time you resume a coroutine,
it starts running its body.
The values @id{val1}, @ldots are passed
as the arguments to the body function.
If the coroutine has yielded,
@id{resume} restarts it;
the values @id{val1}, @ldots are passed
as the results from the yield.

If the coroutine runs without any errors,
@id{resume} returns @true plus any values passed to @id{yield}
(when the coroutine yields) or any values returned by the body function
(when the coroutine terminates).
If there is any error,
@id{resume} returns @false plus the error message.

}

@LibEntry{coroutine.running ()|

Returns the running coroutine plus a boolean,
true when the running coroutine is the main one.

}

@LibEntry{coroutine.status (co)|

Returns the status of the coroutine @id{co}, as a string:
@T{"running"},
if the coroutine is running
(that is, it is the one that called @id{status});
@T{"suspended"}, if the coroutine is suspended in a call to @id{yield},
or if it has not started running yet;
@T{"normal"} if the coroutine is active but not running
(that is, it has resumed another coroutine);
and @T{"dead"} if the coroutine has finished its body function,
or if it has stopped with an error.

}

@LibEntry{coroutine.wrap (f)|

Creates a new coroutine, with body @id{f};
@id{f} must be a function.
Returns a function that resumes the coroutine each time it is called.
Any arguments passed to this function behave as the
extra arguments to @id{resume}.
The function returns the same values returned by @id{resume},
except the first boolean.
In case of error,
the function closes the coroutine and propagates the error.

}

@LibEntry{coroutine.yield (@Cdots)|

Suspends the execution of the calling coroutine.
Any arguments to @id{yield} are passed as extra results to @id{resume}.

}

}

@sect2{packlib| @title{Modules}

The package library provides basic
facilities for loading modules in Lua.
It exports one function directly in the global environment:
@Lid{require}.
Everything else is exported in the table @defid{package}.


@LibEntry{require (modname)|

Loads the given module.
The function starts by looking into the @Lid{package.loaded} table
to determine whether @id{modname} is already loaded.
If it is, then @id{require} returns the value stored
at @T{package.loaded[modname]}.
(The absence of a second result in this case
signals that this call did not have to load the module.)
Otherwise, it tries to find a @emph{loader} for the module.

To find a loader,
@id{require} is guided by the table @Lid{package.searchers}.
Each item in this table is a search function,
that searches for the module in a particular way.
By changing this table,
we can change how @id{require} looks for a module.
The following explanation is based on the default configuration
for @Lid{package.searchers}.

First @id{require} queries @T{package.preload[modname]}.
If it has a value,
this value (which must be a function) is the loader.
Otherwise @id{require} searches for a Lua loader using the
path stored in @Lid{package.path}.
If that also fails, it searches for a @N{C loader} using the
path stored in @Lid{package.cpath}.
If that also fails,
it tries an @emph{all-in-one} loader @seeF{package.searchers}.

Once a loader is found,
@id{require} calls the loader with two arguments:
@id{modname} and an extra value,
a @emph{loader data},
also returned by the searcher.
The loader data can be any value useful to the module;
for the default searchers,
it indicates where the loader was found.
(For instance, if the loader came from a file,
this extra value is the file path.)
If the loader returns any non-nil value,
@id{require} assigns the returned value to @T{package.loaded[modname]}.
If the loader does not return a non-nil value and
has not assigned any value to @T{package.loaded[modname]},
then @id{require} assigns @Rw{true} to this entry.
In any case, @id{require} returns the
final value of @T{package.loaded[modname]}.
Besides that value, @id{require} also returns as a second result
the loader data returned by the searcher,
which indicates how @id{require} found the module.

If there is any error loading or running the module,
or if it cannot find any loader for the module,
then @id{require} raises an error.

}

@LibEntry{package.config|

A string describing some compile-time configurations for packages.
This string is a sequence of lines:
@itemize{

@item{The first line is the @x{directory separator} string.
Default is @Char{\} for @x{Windows} and @Char{/} for all other systems.}

@item{The second line is the character that separates templates in a path.
Default is @Char{;}.}

@item{The third line is the string that marks the
substitution points in a template.
Default is @Char{?}.}

@item{The fourth line is a string that, in a path in @x{Windows},
is replaced by the executable's directory.
Default is @Char{!}.}

@item{The fifth line is a mark to ignore all text after it
when building the @id{luaopen_} function name.
Default is @Char{-}.}

}

}

@LibEntry{package.cpath|

A string with the path used by @Lid{require}
to search for a @N{C loader}.

Lua initializes the @N{C path} @Lid{package.cpath} in the same way
it initializes the Lua path @Lid{package.path},
using the environment variable @defid{LUA_CPATH_5_4},
or the environment variable @defid{LUA_CPATH},
or a default path defined in @id{luaconf.h}.

}

@LibEntry{package.loaded|

A table used by @Lid{require} to control which
modules are already loaded.
When you require a module @id{modname} and
@T{package.loaded[modname]} is not false,
@Lid{require} simply returns the value stored there.

This variable is only a reference to the real table;
assignments to this variable do not change the
table used by @Lid{require}.

}

@LibEntry{package.loadlib (libname, funcname)|

Dynamically links the host program with the @N{C library} @id{libname}.

If @id{funcname} is @St{*},
then it only links with the library,
making the symbols exported by the library
available to other dynamically linked libraries.
Otherwise,
it looks for a function @id{funcname} inside the library
and returns this function as a @N{C function}.
So, @id{funcname} must follow the @Lid{lua_CFunction} prototype
@seeC{lua_CFunction}.

This is a low-level function.
It completely bypasses the package and module system.
Unlike @Lid{require},
it does not perform any path searching and
does not automatically adds extensions.
@id{libname} must be the complete file name of the @N{C library},
including if necessary a path and an extension.
@id{funcname} must be the exact name exported by the @N{C library}
(which may depend on the @N{C compiler} and linker used).

This function is not supported by @N{Standard C}.
As such, it is only available on some platforms
(Windows, Linux, Mac OS X, Solaris, BSD,
plus other Unix systems that support the @id{dlfcn} standard).

This function is inherently insecure,
as it allows Lua to call any function in any readable dynamic
library in the system.
(Lua calls any function assuming the function
has a proper prototype and respects a proper protocol
@see{lua_CFunction}.
Therefore,
calling an arbitrary function in an arbitrary dynamic library
more often than not results in an access violation.)

}

@LibEntry{package.path|

A string with the path used by @Lid{require}
to search for a Lua loader.

At start-up, Lua initializes this variable with
the value of the environment variable @defid{LUA_PATH_5_4} or
the environment variable @defid{LUA_PATH} or
with a default path defined in @id{luaconf.h},
if those environment variables are not defined.
A @St{;;} in the value of the environment variable
is replaced by the default path.

}

@LibEntry{package.preload|

A table to store loaders for specific modules
@seeF{require}.

This variable is only a reference to the real table;
assignments to this variable do not change the
table used by @Lid{require}.

}

@LibEntry{package.searchers|

A table used by @Lid{require} to control how to find modules.

Each entry in this table is a @def{searcher function}.
When looking for a module,
@Lid{require} calls each of these searchers in ascending order,
with the module name (the argument given to @Lid{require}) as its
sole argument.
If the searcher finds the module,
it returns another function, the module @def{loader},
plus an extra value, a @emph{loader data},
that will be passed to that loader and
returned as a second result by @Lid{require}.
If it cannot find the module,
it returns a string explaining why
(or @nil if it has nothing to say).

Lua initializes this table with four searcher functions.

The first searcher simply looks for a loader in the
@Lid{package.preload} table.

The second searcher looks for a loader as a Lua library,
using the path stored at @Lid{package.path}.
The search is done as described in function @Lid{package.searchpath}.

The third searcher looks for a loader as a @N{C library},
using the path given by the variable @Lid{package.cpath}.
Again,
the search is done as described in function @Lid{package.searchpath}.
For instance,
if the @N{C path} is the string
@verbatim{
"./?.so;./?.dll;/usr/local/?/init.so"
}
the searcher for module @id{foo}
will try to open the files @T{./foo.so}, @T{./foo.dll},
and @T{/usr/local/foo/init.so}, in that order.
Once it finds a @N{C library},
this searcher first uses a dynamic link facility to link the
application with the library.
Then it tries to find a @N{C function} inside the library to
be used as the loader.
The name of this @N{C function} is the string @St{luaopen_}
concatenated with a copy of the module name where each dot
is replaced by an underscore.
Moreover, if the module name has a hyphen,
its suffix after (and including) the first hyphen is removed.
For instance, if the module name is @id{a.b.c-v2.1},
the function name will be @id{luaopen_a_b_c}.

The fourth searcher tries an @def{all-in-one loader}.
It searches the @N{C path} for a library for
the root name of the given module.
For instance, when requiring @id{a.b.c},
it will search for a @N{C library} for @id{a}.
If found, it looks into it for an open function for
the submodule;
in our example, that would be @id{luaopen_a_b_c}.
With this facility, a package can pack several @N{C submodules}
into one single library,
with each submodule keeping its original open function.

All searchers except the first one (preload) return as the extra value
the file path where the module was found,
as returned by @Lid{package.searchpath}.
The first searcher always returns the string @St{:preload:}.

Searchers should raise no errors and have no side effects in Lua.
(They may have side effects in C,
for instance by linking the application with a library.)

}

@LibEntry{package.searchpath (name, path [, sep [, rep]])|

Searches for the given @id{name} in the given @id{path}.

A path is a string containing a sequence of
@emph{templates} separated by semicolons.
For each template,
the function replaces each interrogation mark (if any)
in the template with a copy of @id{name}
wherein all occurrences of @id{sep}
(a dot, by default)
were replaced by @id{rep}
(the system's directory separator, by default),
and then tries to open the resulting file name.

For instance, if the path is the string
@verbatim{
"./?.lua;./?.lc;/usr/local/?/init.lua"
}
the search for the name @id{foo.a}
will try to open the files
@T{./foo/a.lua}, @T{./foo/a.lc}, and
@T{/usr/local/foo/a/init.lua}, in that order.

Returns the resulting name of the first file that it can
open in read mode (after closing the file),
or @fail plus an error message if none succeeds.
(This error message lists all file names it tried to open.)

}

}

@sect2{strlib| @title{String Manipulation}

@simplesect{

This library provides generic functions for string manipulation,
such as finding and extracting substrings, and pattern matching.
When indexing a string in Lua, the first character is at @N{position 1}
(not @N{at 0}, as in C).
Indices are allowed to be negative and are interpreted as indexing backwards,
from the end of the string.
Thus, the last character is at position @num{-1}, and so on.

The string library provides all its functions inside the table
@defid{string}.
It also sets a @x{metatable for strings}
where the @idx{__index} field points to the @id{string} table.
Therefore, you can use the string functions in object-oriented style.
For instance, @T{string.byte(s,i)}
can be written as @T{s:byte(i)}.

The string library assumes one-byte character encodings.


@LibEntry{string.byte (s [, i [, j]])|
Returns the internal numeric codes of the characters @T{s[i]},
@T{s[i+1]}, @ldots, @T{s[j]}.
The default value for @id{i} @N{is 1};
the default value for @id{j} @N{is @id{i}}.
These indices are corrected
following the same rules of function @Lid{string.sub}.

Numeric codes are not necessarily portable across platforms.

}

@LibEntry{string.char (@Cdots)|
Receives zero or more integers.
Returns a string with length equal to the number of arguments,
in which each character has the internal numeric code equal
to its corresponding argument.

Numeric codes are not necessarily portable across platforms.

}

@LibEntry{string.dump (function [, strip])|

Returns a string containing a binary representation
(a @emph{binary chunk})
of the given function,
so that a later @Lid{load} on this string returns
a copy of the function (but with new upvalues).
If @id{strip} is a true value,
the binary representation may not include all debug information
about the function,
to save space.

Functions with upvalues have only their number of upvalues saved.
When (re)loaded,
those upvalues receive fresh instances.
(See the @Lid{load} function for details about
how these upvalues are initialized.
You can use the debug library to serialize
and reload the upvalues of a function
in a way adequate to your needs.)

}

@LibEntry{string.find (s, pattern [, init [, plain]])|

Looks for the first match of
@id{pattern} @see{pm} in the string @id{s}.
If it finds a match, then @id{find} returns the indices @N{of @T{s}}
where this occurrence starts and ends;
otherwise, it returns @fail.
A third, optional numeric argument @id{init} specifies
where to start the search;
its default value @N{is 1} and can be negative.
A value of @true as a fourth, optional argument @id{plain}
turns off the pattern matching facilities,
so the function does a plain @Q{find substring} operation,
with no characters in @id{pattern} being considered magic.

If the pattern has captures,
then in a successful match
the captured values are also returned,
after the two indices.

}

@LibEntry{string.format (formatstring, @Cdots)|

Returns a formatted version of its variable number of arguments
following the description given in its first argument,
which must be a string.
The format string follows the same rules as the @ANSI{sprintf}.
The only differences are that the conversion specifiers and modifiers
@T{*}, @id{h}, @id{L}, @id{l}, and @id{n} are not supported
and that there is an extra specifier, @id{q}.

The specifier @id{q} formats booleans, nil, numbers, and strings
in a way that the result is a valid constant in Lua source code.
Booleans and nil are written in the obvious way
(@id{true}, @id{false}, @id{nil}).
Floats are written in hexadecimal,
to preserve full precision.
A string is written between double quotes,
using escape sequences when necessary to ensure that
it can safely be read back by the Lua interpreter.
For instance, the call
@verbatim{
string.format('%q', 'a string with "quotes" and \n new line')
}
may produce the string:
@verbatim{
"a string with \"quotes\" and \
 new line"
}
This specifier does not support modifiers (flags, width, length).

The conversion specifiers
@id{A}, @id{a}, @id{E}, @id{e}, @id{f},
@id{G}, and @id{g} all expect a number as argument.
The specifiers @id{c}, @id{d},
@id{i}, @id{o}, @id{u}, @id{X}, and @id{x}
expect an integer.
When Lua is compiled with a C89 compiler,
the specifiers @id{A} and @id{a} (hexadecimal floats)
do not support modifiers.

The specifier @id{s} expects a string;
if its argument is not a string,
it is converted to one following the same rules of @Lid{tostring}.
If the specifier has any modifier,
the corresponding string argument should not contain @x{embedded zeros}.

The specifier @id{p} formats the pointer
returned by @Lid{lua_topointer}.
That gives a unique string identifier for tables, userdata,
threads, strings, and functions.
For other values (numbers, nil, booleans),
this specifier results in a string representing
the pointer @id{NULL}.

}

@LibEntry{string.gmatch (s, pattern [, init])|
Returns an iterator function that,
each time it is called,
returns the next captures from @id{pattern} @see{pm}
over the string @id{s}.
If @id{pattern} specifies no captures,
then the whole match is produced in each call.
A third, optional numeric argument @id{init} specifies
where to start the search;
its default value @N{is 1} and can be negative.

As an example, the following loop
will iterate over all the words from string @id{s},
printing one per line:
@verbatim{
s = "hello world from Lua"
for w in string.gmatch(s, "%a+") do
  print(w)
end
}
The next example collects all pairs @T{key=value} from the
given string into a table:
@verbatim{
t = {}
s = "from=world, to=Lua"
for k, v in string.gmatch(s, "(%w+)=(%w+)") do
  t[k] = v
end
}

For this function, a caret @Char{^} at the start of a pattern does not
work as an anchor, as this would prevent the iteration.

}

@LibEntry{string.gsub (s, pattern, repl [, n])|
Returns a copy of @id{s}
in which all (or the first @id{n}, if given)
occurrences of the @id{pattern} @see{pm} have been
replaced by a replacement string specified by @id{repl},
which can be a string, a table, or a function.
@id{gsub} also returns, as its second value,
the total number of matches that occurred.
The name @id{gsub} comes from @emph{Global SUBstitution}.

If @id{repl} is a string, then its value is used for replacement.
The @N{character @T{%}} works as an escape character:
any sequence in @id{repl} of the form @T{%@rep{d}},
with @rep{d} between 1 and 9,
stands for the value of the @rep{d}-th captured substring;
the sequence @T{%0} stands for the whole match;
the sequence @T{%%} stands for a @N{single @T{%}}.

If @id{repl} is a table, then the table is queried for every match,
using the first capture as the key.

If @id{repl} is a function, then this function is called every time a
match occurs, with all captured substrings passed as arguments,
in order.

In any case,
if the pattern specifies no captures,
then it behaves as if the whole pattern was inside a capture.

If the value returned by the table query or by the function call
is a string or a number,
then it is used as the replacement string;
otherwise, if it is @Rw{false} or @nil,
then there is no replacement
(that is, the original match is kept in the string).

Here are some examples:
@verbatim{
x = string.gsub("hello world", "(%w+)", "%1 %1")
--> x="hello hello world world"

x = string.gsub("hello world", "%w+", "%0 %0", 1)
--> x="hello hello world"

x = string.gsub("hello world from Lua", "(%w+)%s*(%w+)", "%2 %1")
--> x="world hello Lua from"

x = string.gsub("home = $HOME, user = $USER", "%$(%w+)", os.getenv)
--> x="home = /home/<USER>"

x = string.gsub("4+5 = $return 4+5$", "%$(.-)%$", function (s)
      return load(s)()
    end)
--> x="4+5 = 9"

local t = {name="lua", version="5.4"}
x = string.gsub("$name-$version.tar.gz", "%$(%w+)", t)
--> x="lua-5.4.tar.gz"
}

}

@LibEntry{string.len (s)|

Receives a string and returns its length.
The empty string @T{""} has length 0.
Embedded zeros are counted,
so @T{"a\000bc\000"} has length 5.

}

@LibEntry{string.lower (s)|

Receives a string and returns a copy of this string with all
uppercase letters changed to lowercase.
All other characters are left unchanged.
The definition of what an uppercase letter is depends on the current locale.

}

@LibEntry{string.match (s, pattern [, init])|

Looks for the first @emph{match} of
the @id{pattern} @see{pm} in the string @id{s}.
If it finds one, then @id{match} returns
the captures from the pattern;
otherwise it returns @fail.
If @id{pattern} specifies no captures,
then the whole match is returned.
A third, optional numeric argument @id{init} specifies
where to start the search;
its default value @N{is 1} and can be negative.

}

@LibEntry{string.pack (fmt, v1, v2, @Cdots)|

Returns a binary string containing the values @id{v1}, @id{v2}, etc.
serialized in binary form (packed)
according to the format string @id{fmt} @see{pack}.

}

@LibEntry{string.packsize (fmt)|

Returns the size of a string resulting from @Lid{string.pack}
with the given format.
The format string cannot have the variable-length options
@Char{s} or @Char{z} @see{pack}.

}

@LibEntry{string.rep (s, n [, sep])|

Returns a string that is the concatenation of @id{n} copies of
the string @id{s} separated by the string @id{sep}.
The default value for @id{sep} is the empty string
(that is, no separator).
Returns the empty string if @id{n} is not positive.

(Note that it is very easy to exhaust the memory of your machine
with a single call to this function.)

}

@LibEntry{string.reverse (s)|

Returns a string that is the string @id{s} reversed.

}

@LibEntry{string.sub (s, i [, j])|

Returns the substring of @id{s} that
starts at @id{i}  and continues until @id{j};
@id{i} and @id{j} can be negative.
If @id{j} is absent, then it is assumed to be equal to @num{-1}
(which is the same as the string length).
In particular,
the call @T{string.sub(s,1,j)} returns a prefix of @id{s}
with length @id{j},
and @T{string.sub(s, -i)} (for a positive @id{i})
returns a suffix of @id{s}
with length @id{i}.

If, after the translation of negative indices,
@id{i} is less than 1,
it is corrected to 1.
If @id{j} is greater than the string length,
it is corrected to that length.
If, after these corrections,
@id{i} is greater than @id{j},
the function returns the empty string.

}

@LibEntry{string.unpack (fmt, s [, pos])|

Returns the values packed in string @id{s} @seeF{string.pack}
according to the format string @id{fmt} @see{pack}.
An optional @id{pos} marks where
to start reading in @id{s} (default is 1).
After the read values,
this function also returns the index of the first unread byte in @id{s}.

}

@LibEntry{string.upper (s)|

Receives a string and returns a copy of this string with all
lowercase letters changed to uppercase.
All other characters are left unchanged.
The definition of what a lowercase letter is depends on the current locale.

}

}


@sect3{pm| @title{Patterns}

@simplesect{

Patterns in Lua are described by regular strings,
which are interpreted as patterns by the pattern-matching functions
@Lid{string.find},
@Lid{string.gmatch},
@Lid{string.gsub},
and @Lid{string.match}.
This section describes the syntax and the meaning
(that is, what they match) of these strings.

}

@sect4{@title{Character Class:}
A @def{character class} is used to represent a set of characters.
The following combinations are allowed in describing a character class:
@description{

@item{@rep{x}|
(where @rep{x} is not one of the @emphx{magic characters}
@T{^$()%.[]*+-?})
represents the character @emph{x} itself.
}

@item{@T{.}| (a dot) represents all characters.}

@item{@T{%a}| represents all letters.}

@item{@T{%c}| represents all control characters.}

@item{@T{%d}| represents all digits.}

@item{@T{%g}| represents all printable characters except space.}

@item{@T{%l}| represents all lowercase letters.}

@item{@T{%p}| represents all punctuation characters.}

@item{@T{%s}| represents all space characters.}

@item{@T{%u}| represents all uppercase letters.}

@item{@T{%w}| represents all alphanumeric characters.}

@item{@T{%x}| represents all hexadecimal digits.}

@item{@T{%@rep{x}}| (where @rep{x} is any non-alphanumeric character)
represents the character @rep{x}.
This is the standard way to escape the magic characters.
Any non-alphanumeric character
(including all punctuation characters, even the non-magical)
can be preceded by a @Char{%} to represent itself in a pattern.
}

@item{@T{[@rep{set}]}|
represents the class which is the union of all
characters in @rep{set}.
A range of characters can be specified by
separating the end characters of the range,
in ascending order, with a @Char{-}.
All classes @T{%}@emph{x} described above can also be used as
components in @rep{set}.
All other characters in @rep{set} represent themselves.
For example, @T{[%w_]} (or @T{[_%w]})
represents all alphanumeric characters plus the underscore,
@T{[0-7]} represents the octal digits,
and @T{[0-7%l%-]} represents the octal digits plus
the lowercase letters plus the @Char{-} character.

You can put a closing square bracket in a set
by positioning it as the first character in the set.
You can put a hyphen in a set
by positioning it as the first or the last character in the set.
(You can also use an escape for both cases.)

The interaction between ranges and classes is not defined.
Therefore, patterns like @T{[%a-z]} or @T{[a-%%]}
have no meaning.
}

@item{@T{[^@rep{set}]}|
represents the complement of @rep{set},
where @rep{set} is interpreted as above.
}

}
For all classes represented by single letters (@T{%a}, @T{%c}, etc.),
the corresponding uppercase letter represents the complement of the class.
For instance, @T{%S} represents all non-space characters.

The definitions of letter, space, and other character groups
depend on the current locale.
In particular, the class @T{[a-z]} may not be equivalent to @T{%l}.

}

@sect4{@title{Pattern Item:}
A @def{pattern item} can be
@itemize{

@item{
a single character class,
which matches any single character in the class;
}

@item{
a single character class followed by @Char{*},
which matches sequences of zero or more characters in the class.
These repetition items will always match the longest possible sequence;
}

@item{
a single character class followed by @Char{+},
which matches sequences of one or more characters in the class.
These repetition items will always match the longest possible sequence;
}

@item{
a single character class followed by @Char{-},
which also matches sequences of zero or more characters in the class.
Unlike @Char{*},
these repetition items will always match the shortest possible sequence;
}

@item{
a single character class followed by @Char{?},
which matches zero or one occurrence of a character in the class.
It always matches one occurrence if possible;
}

@item{
@T{%@rep{n}}, for @rep{n} between 1 and 9;
such item matches a substring equal to the @rep{n}-th captured string
(see below);
}

@item{
@T{%b@rep{xy}}, where @rep{x} and @rep{y} are two distinct characters;
such item matches strings that start @N{with @rep{x}}, end @N{with @rep{y}},
and where the @rep{x} and @rep{y} are @emph{balanced}.
This means that, if one reads the string from left to right,
counting @M{+1} for an @rep{x} and @M{-1} for a @rep{y},
the ending @rep{y} is the first @rep{y} where the count reaches 0.
For instance, the item @T{%b()} matches expressions with
balanced parentheses.
}

@item{
@T{%f[@rep{set}]}, a @def{frontier pattern};
such item matches an empty string at any position such that
the next character belongs to @rep{set}
and the previous character does not belong to @rep{set}.
The set @rep{set} is interpreted as previously described.
The beginning and the end of the subject are handled as if
they were the character @Char{\0}.
}

}

}

@sect4{@title{Pattern:}
A @def{pattern} is a sequence of pattern items.
A caret @Char{^} at the beginning of a pattern anchors the match at the
beginning of the subject string.
A @Char{$} at the end of a pattern anchors the match at the
end of the subject string.
At other positions,
@Char{^} and @Char{$} have no special meaning and represent themselves.

}

@sect4{@title{Captures:}
A pattern can contain sub-patterns enclosed in parentheses;
they describe @def{captures}.
When a match succeeds, the substrings of the subject string
that match captures are stored (@emph{captured}) for future use.
Captures are numbered according to their left parentheses.
For instance, in the pattern @T{"(a*(.)%w(%s*))"},
the part of the string matching @T{"a*(.)%w(%s*)"} is
stored as the first capture, and therefore has @N{number 1};
the character matching @St{.} is captured with @N{number 2},
and the part matching @St{%s*} has @N{number 3}.

As a special case, the capture @T{()} captures
the current string position (a number).
For instance, if we apply the pattern @T{"()aa()"} on the
string @T{"flaaap"}, there will be two captures: @N{3 and 5}.

}

@sect4{@title{Multiple matches:}
The function @Lid{string.gsub} and the iterator @Lid{string.gmatch}
match multiple occurrences of the given pattern in the subject.
For these functions,
a new match is considered valid only
if it ends at least one byte after the end of the previous match.
In other words, the pattern machine never accepts the
empty string as a match immediately after another match.
As an example,
consider the results of the following code:
@verbatim{
> string.gsub("abc", "()a*()", print);
--> 1   2
--> 3   3
--> 4   4
}
The second and third results come from Lua matching an empty
string after @Char{b} and another one after @Char{c}.
Lua does not match an empty string after @Char{a},
because it would end at the same position of the previous match.

}

}

@sect3{pack| @title{Format Strings for Pack and Unpack}

The first argument to @Lid{string.pack},
@Lid{string.packsize}, and @Lid{string.unpack}
is a format string,
which describes the layout of the structure being created or read.

A format string is a sequence of conversion options.
The conversion options are as follows:
@description{
@item{@T{<}|sets little endian}
@item{@T{>}|sets big endian}
@item{@T{=}|sets native endian}
@item{@T{![@rep{n}]}|sets maximum alignment to @id{n}
(default is native alignment)}
@item{@T{b}|a signed byte (@id{char})}
@item{@T{B}|an unsigned byte (@id{char})}
@item{@T{h}|a signed @id{short} (native size)}
@item{@T{H}|an unsigned @id{short} (native size)}
@item{@T{l}|a signed @id{long} (native size)}
@item{@T{L}|an unsigned @id{long} (native size)}
@item{@T{j}|a @id{lua_Integer}}
@item{@T{J}|a @id{lua_Unsigned}}
@item{@T{T}|a @id{size_t} (native size)}
@item{@T{i[@rep{n}]}|a signed @id{int} with @id{n} bytes
(default is native size)}
@item{@T{I[@rep{n}]}|an unsigned @id{int} with @id{n} bytes
(default is native size)}
@item{@T{f}|a @id{float} (native size)}
@item{@T{d}|a @id{double} (native size)}
@item{@T{n}|a @id{lua_Number}}
@item{@T{c@rep{n}}|a fixed-sized string with @id{n} bytes}
@item{@T{z}|a zero-terminated string}
@item{@T{s[@emph{n}]}|a string preceded by its length
coded as an unsigned integer with @id{n} bytes
(default is a @id{size_t})}
@item{@T{x}|one byte of padding}
@item{@T{X@rep{op}}|an empty item that aligns
according to option @id{op}
(which is otherwise ignored)}
@item{@Char{ }|(space) ignored}
}
(A @St{[@rep{n}]} means an optional integral numeral.)
Except for padding, spaces, and configurations
(options @St{xX <=>!}),
each option corresponds to an argument in @Lid{string.pack}
or a result in @Lid{string.unpack}.

For options @St{!@rep{n}}, @St{s@rep{n}}, @St{i@rep{n}}, and @St{I@rep{n}},
@id{n} can be any integer between 1 and 16.
All integral options check overflows;
@Lid{string.pack} checks whether the given value fits in the given size;
@Lid{string.unpack} checks whether the read value fits in a Lua integer.
For the unsigned options,
Lua integers are treated as unsigned values too.

Any format string starts as if prefixed by @St{!1=},
that is,
with maximum alignment of 1 (no alignment)
and native endianness.

Native endianness assumes that the whole system is
either big or little endian.
The packing functions will not emulate correctly the behavior
of mixed-endian formats.

Alignment works as follows:
For each option,
the format gets extra padding until the data starts
at an offset that is a multiple of the minimum between the
option size and the maximum alignment;
this minimum must be a power of 2.
Options @St{c} and @St{z} are not aligned;
option @St{s} follows the alignment of its starting integer.


All padding is filled with zeros by @Lid{string.pack}
and ignored by @Lid{string.unpack}.

}

}

@sect2{utf8| @title{UTF-8 Support}

This library provides basic support for @x{UTF-8} encoding.
It provides all its functions inside the table @defid{utf8}.
This library does not provide any support for @x{Unicode} other
than the handling of the encoding.
Any operation that needs the meaning of a character,
such as character classification, is outside its scope.

Unless stated otherwise,
all functions that expect a byte position as a parameter
assume that the given position is either the start of a byte sequence
or one plus the length of the subject string.
As in the string library,
negative indices count from the end of the string.

Functions that create byte sequences
accept all values up to @T{0x7FFFFFFF},
as defined in the original UTF-8 specification;
that implies byte sequences of up to six bytes.

Functions that interpret byte sequences only accept
valid sequences (well formed and not overlong).
By default, they only accept byte sequences
that result in valid Unicode code points,
rejecting values greater than @T{10FFFF} and surrogates.
A boolean argument @id{lax}, when available,
lifts these checks,
so that all values up to @T{0x7FFFFFFF} are accepted.
(Not well formed and overlong sequences are still rejected.)


@LibEntry{utf8.char (@Cdots)|

Receives zero or more integers,
converts each one to its corresponding UTF-8 byte sequence
and returns a string with the concatenation of all these sequences.

}

@LibEntry{utf8.charpattern|

The pattern (a string, not a function) @St{[\0-\x7F\xC2-\xFD][\x80-\xBF]*}
@see{pm},
which matches exactly one UTF-8 byte sequence,
assuming that the subject is a valid UTF-8 string.

}

@LibEntry{utf8.codes (s [, lax])|

Returns values so that the construction
@verbatim{
for p, c in utf8.codes(s) do @rep{body} end
}
will iterate over all UTF-8 characters in string @id{s},
with @id{p} being the position (in bytes) and @id{c} the code point
of each character.
It raises an error if it meets any invalid byte sequence.

}

@LibEntry{utf8.codepoint (s [, i [, j [, lax]]])|

Returns the code points (as integers) from all characters in @id{s}
that start between byte position @id{i} and @id{j} (both included).
The default for @id{i} is 1 and for @id{j} is @id{i}.
It raises an error if it meets any invalid byte sequence.

}

@LibEntry{utf8.len (s [, i [, j [, lax]]])|

Returns the number of UTF-8 characters in string @id{s}
that start between positions @id{i} and @id{j} (both inclusive).
The default for @id{i} is @num{1} and for @id{j} is @num{-1}.
If it finds any invalid byte sequence,
returns @fail plus the position of the first invalid byte.

}

@LibEntry{utf8.offset (s, n [, i])|

Returns the position (in bytes) where the encoding of the
@id{n}-th character of @id{s}
(counting from position @id{i}) starts.
A negative @id{n} gets characters before position @id{i}.
The default for @id{i} is 1 when @id{n} is non-negative
and @T{#s + 1} otherwise,
so that @T{utf8.offset(s, -n)} gets the offset of the
@id{n}-th character from the end of the string.
If the specified character is neither in the subject
nor right after its end,
the function returns @fail.

As a special case,
when @id{n} is 0 the function returns the start of the encoding
of the character that contains the @id{i}-th byte of @id{s}.

This function assumes that @id{s} is a valid UTF-8 string.

}

}

@sect2{tablib| @title{Table Manipulation}

This library provides generic functions for table manipulation.
It provides all its functions inside the table @defid{table}.

Remember that, whenever an operation needs the length of a table,
all caveats about the length operator apply @see{len-op}.
All functions ignore non-numeric keys
in the tables given as arguments.


@LibEntry{table.concat (list [, sep [, i [, j]]])|

Given a list where all elements are strings or numbers,
returns the string @T{list[i]..sep..list[i+1] @Cdots sep..list[j]}.
The default value for @id{sep} is the empty string,
the default for @id{i} is 1,
and the default for @id{j} is @T{#list}.
If @id{i} is greater than @id{j}, returns the empty string.

}

@LibEntry{table.insert (list, [pos,] value)|

Inserts element @id{value} at position @id{pos} in @id{list},
shifting up the elements
@T{list[pos], list[pos+1], @Cdots, list[#list]}.
The default value for @id{pos} is @T{#list+1},
so that a call @T{table.insert(t,x)} inserts @id{x} at the end
of the list @id{t}.

}

@LibEntry{table.move (a1, f, e, t [,a2])|

Moves elements from the table @id{a1} to the table @id{a2},
performing the equivalent to the following
multiple assignment:
@T{a2[t],@Cdots = a1[f],@Cdots,a1[e]}.
The default for @id{a2} is @id{a1}.
The destination range can overlap with the source range.
The number of elements to be moved must fit in a Lua integer.

Returns the destination table @id{a2}.

}

@LibEntry{table.pack (@Cdots)|

Returns a new table with all arguments stored into keys 1, 2, etc.
and with a field @St{n} with the total number of arguments.
Note that the resulting table may not be a sequence,
if some arguments are @nil.

}

@LibEntry{table.remove (list [, pos])|

Removes from @id{list} the element at position @id{pos},
returning the value of the removed element.
When @id{pos} is an integer between 1 and @T{#list},
it shifts down the elements
@T{list[pos+1], list[pos+2], @Cdots, list[#list]}
and erases element @T{list[#list]};
The index @id{pos} can also be 0 when @T{#list} is 0,
or @T{#list + 1}.

The default value for @id{pos} is @T{#list},
so that a call @T{table.remove(l)} removes the last element
of the list @id{l}.

}

@LibEntry{table.sort (list [, comp])|

Sorts the list elements in a given order, @emph{in-place},
from @T{list[1]} to @T{list[#list]}.
If @id{comp} is given,
then it must be a function that receives two list elements
and returns true when the first element must come
before the second in the final order
(so that, after the sort,
@T{i < j} implies @T{not comp(list[j],list[i])}).
If @id{comp} is not given,
then the standard Lua operator @T{<} is used instead.

Note that the @id{comp} function must define
a strict partial order over the elements in the list;
that is, it must be asymmetric and transitive.
Otherwise, no valid sort may be possible.

The sort algorithm is not stable:
elements considered equal by the given order
may have their relative positions changed by the sort.

}

@LibEntry{table.unpack (list [, i [, j]])|

Returns the elements from the given list.
This function is equivalent to
@verbatim{
return list[i], list[i+1], @Cdots, list[j]
}
By default, @id{i} @N{is 1} and @id{j} is @T{#list}.

}

}

@sect2{mathlib| @title{Mathematical Functions}

This library provides basic mathematical functions.
It provides all its functions and constants inside the table @defid{math}.
Functions with the annotation @St{integer/float} give
integer results for integer arguments
and float results for non-integer arguments.
The rounding functions
@Lid{math.ceil}, @Lid{math.floor}, and @Lid{math.modf}
return an integer when the result fits in the range of an integer,
or a float otherwise.

@LibEntry{math.abs (x)|

Returns the maximum value between @id{x} and @id{-x}. (integer/float)

}

@LibEntry{math.acos (x)|

Returns the arc cosine of @id{x} (in radians).

}

@LibEntry{math.asin (x)|

Returns the arc sine of @id{x} (in radians).

}

@LibEntry{math.atan (y [, x])|

@index{atan2}
Returns the arc tangent of @T{y/x} (in radians),
but uses the signs of both arguments to find the
quadrant of the result.
It also handles correctly the case of @id{x} being zero.

The default value for @id{x} is 1,
so that the call @T{math.atan(y)}
returns the arc tangent of @id{y}.

}

@LibEntry{math.ceil (x)|

Returns the smallest integral value greater than or equal to @id{x}.

}

@LibEntry{math.cos (x)|

Returns the cosine of @id{x} (assumed to be in radians).

}

@LibEntry{math.deg (x)|

Converts the angle @id{x} from radians to degrees.

}

@LibEntry{math.exp (x)|

Returns the value @M{e@sp{x}}
(where @id{e} is the base of natural logarithms).

}

@LibEntry{math.floor (x)|

Returns the largest integral value less than or equal to @id{x}.

}

@LibEntry{math.fmod (x, y)|

Returns the remainder of the division of @id{x} by @id{y}
that rounds the quotient towards zero. (integer/float)

}

@LibEntry{math.huge|

The float value @idx{HUGE_VAL},
a value greater than any other numeric value.

}

@LibEntry{math.log (x [, base])|

Returns the logarithm of @id{x} in the given base.
The default for @id{base} is @M{e}
(so that the function returns the natural logarithm of @id{x}).

}

@LibEntry{math.max (x, @Cdots)|

Returns the argument with the maximum value,
according to the Lua operator @T{<}.

}

@LibEntry{math.maxinteger|
An integer with the maximum value for an integer.

}

@LibEntry{math.min (x, @Cdots)|

Returns the argument with the minimum value,
according to the Lua operator @T{<}.

}

@LibEntry{math.mininteger|
An integer with the minimum value for an integer.

}

@LibEntry{math.modf (x)|

Returns the integral part of @id{x} and the fractional part of @id{x}.
Its second result is always a float.

}

@LibEntry{math.pi|

The value of @M{@pi}.

}

@LibEntry{math.rad (x)|

Converts the angle @id{x} from degrees to radians.

}

@LibEntry{math.random ([m [, n]])|

When called without arguments,
returns a pseudo-random float with uniform distribution
in the range @C{(} @M{[0,1)}.  @C{]}
When called with two integers @id{m} and @id{n},
@id{math.random} returns a pseudo-random integer
with uniform distribution in the range @M{[m, n]}.
The call @T{math.random(n)}, for a positive @id{n},
is equivalent to @T{math.random(1,n)}.
The call @T{math.random(0)} produces an integer with
all bits (pseudo)random.

This function uses the @idx{xoshiro256**} algorithm to produce
pseudo-random 64-bit integers,
which are the results of calls with @N{argument 0}.
Other results (ranges and floats)
are unbiased extracted from these integers.

Lua initializes its pseudo-random generator with the equivalent of
a call to @Lid{math.randomseed} with no arguments,
so that @id{math.random} should generate
different sequences of results each time the program runs.

}

@LibEntry{math.randomseed ([x [, y]])|

When called with at least one argument,
the integer parameters @id{x} and @id{y} are
joined into a 128-bit @emphx{seed} that
is used to reinitialize the pseudo-random generator;
equal seeds produce equal sequences of numbers.
The default for @id{y} is zero.

When called with no arguments,
Lua generates a seed with
a weak attempt for randomness.

This function returns the two seed components
that were effectively used,
so that setting them again repeats the sequence.

To ensure a required level of randomness to the initial state
(or contrarily, to have a deterministic sequence,
for instance when debugging a program),
you should call @Lid{math.randomseed} with explicit arguments.

}

@LibEntry{math.sin (x)|

Returns the sine of @id{x} (assumed to be in radians).

}

@LibEntry{math.sqrt (x)|

Returns the square root of @id{x}.
(You can also use the expression @T{x^0.5} to compute this value.)

}

@LibEntry{math.tan (x)|

Returns the tangent of @id{x} (assumed to be in radians).

}

@LibEntry{math.tointeger (x)|

If the value @id{x} is convertible to an integer,
returns that integer.
Otherwise, returns @fail.

}

@LibEntry{math.type (x)|

Returns @St{integer} if @id{x} is an integer,
@St{float} if it is a float,
or @fail if @id{x} is not a number.

}

@LibEntry{math.ult (m, n)|

Returns a boolean,
true if and only if integer @id{m} is below integer @id{n} when
they are compared as @x{unsigned integers}.

}

}

@sect2{iolib| @title{Input and Output Facilities}

The I/O library provides two different styles for file manipulation.
The first one uses implicit file handles;
that is, there are operations to set a default input file and a
default output file,
and all input/output operations are done over these default files.
The second style uses explicit file handles.

When using implicit file handles,
all operations are supplied by table @defid{io}.
When using explicit file handles,
the operation @Lid{io.open} returns a file handle
and then all operations are supplied as methods of the file handle.

The metatable for file handles provides metamethods
for @idx{__gc} and @idx{__close} that try
to close the file when called.

The table @id{io} also provides
three predefined file handles with their usual meanings from C:
@defid{io.stdin}, @defid{io.stdout}, and @defid{io.stderr}.
The I/O library never closes these files.

Unless otherwise stated,
all I/O functions return @fail on failure,
plus an error message as a second result and
a system-dependent error code as a third result,
and some non-false value on success.
On non-POSIX systems,
the computation of the error message and error code
in case of errors
may be not @x{thread safe},
because they rely on the global C variable @id{errno}.

@LibEntry{io.close ([file])|

Equivalent to @T{file:close()}.
Without a @id{file}, closes the default output file.

}

@LibEntry{io.flush ()|

Equivalent to @T{io.output():flush()}.

}

@LibEntry{io.input ([file])|

When called with a file name, it opens the named file (in text mode),
and sets its handle as the default input file.
When called with a file handle,
it simply sets this file handle as the default input file.
When called without arguments,
it returns the current default input file.

In case of errors this function raises the error,
instead of returning an error code.

}

@LibEntry{io.lines ([filename, @Cdots])|

Opens the given file name in read mode
and returns an iterator function that
works like @T{file:lines(@Cdots)} over the opened file.
When the iterator function fails to read any value,
it automatically closes the file.
Besides the iterator function,
@id{io.lines} returns three other values:
two @nil values as placeholders,
plus the created file handle.
Therefore, when used in a generic @Rw{for} loop,
the file is closed also if the loop is interrupted by an
error or a @Rw{break}.

The call @T{io.lines()} (with no file name) is equivalent
to @T{io.input():lines("l")};
that is, it iterates over the lines of the default input file.
In this case, the iterator does not close the file when the loop ends.

In case of errors opening the file,
this function raises the error,
instead of returning an error code.

}

@LibEntry{io.open (filename [, mode])|

This function opens a file,
in the mode specified in the string @id{mode}.
In case of success,
it returns a new file handle.

The @id{mode} string can be any of the following:
@description{
@item{@St{r}| read mode (the default);}
@item{@St{w}| write mode;}
@item{@St{a}| append mode;}
@item{@St{r+}| update mode, all previous data is preserved;}
@item{@St{w+}| update mode, all previous data is erased;}
@item{@St{a+}| append update mode, previous data is preserved,
  writing is only allowed at the end of file.}
}
The @id{mode} string can also have a @Char{b} at the end,
which is needed in some systems to open the file in binary mode.

}

@LibEntry{io.output ([file])|

Similar to @Lid{io.input}, but operates over the default output file.

}

@LibEntry{io.popen (prog [, mode])|

This function is system dependent and is not available
on all platforms.

Starts the program @id{prog} in a separated process and returns
a file handle that you can use to read data from this program
(if @id{mode} is @T{"r"}, the default)
or to write data to this program
(if @id{mode} is @T{"w"}).

}

@LibEntry{io.read (@Cdots)|

Equivalent to @T{io.input():read(@Cdots)}.

}

@LibEntry{io.tmpfile ()|

In case of success,
returns a handle for a temporary file.
This file is opened in update mode
and it is automatically removed when the program ends.

}

@LibEntry{io.type (obj)|

Checks whether @id{obj} is a valid file handle.
Returns the string @T{"file"} if @id{obj} is an open file handle,
@T{"closed file"} if @id{obj} is a closed file handle,
or @fail if @id{obj} is not a file handle.

}

@LibEntry{io.write (@Cdots)|

Equivalent to @T{io.output():write(@Cdots)}.


}

@LibEntry{file:close ()|

Closes @id{file}.
Note that files are automatically closed when
their handles are garbage collected,
but that takes an unpredictable amount of time to happen.

When closing a file handle created with @Lid{io.popen},
@Lid{file:close} returns the same values
returned by @Lid{os.execute}.

}

@LibEntry{file:flush ()|

Saves any written data to @id{file}.

}

@LibEntry{file:lines (@Cdots)|

Returns an iterator function that,
each time it is called,
reads the file according to the given formats.
When no format is given,
uses @St{l} as a default.
As an example, the construction
@verbatim{
for c in file:lines(1) do @rep{body} end
}
will iterate over all characters of the file,
starting at the current position.
Unlike @Lid{io.lines}, this function does not close the file
when the loop ends.

}

@LibEntry{file:read (@Cdots)|

Reads the file @id{file},
according to the given formats, which specify what to read.
For each format,
the function returns a string or a number with the characters read,
or @fail if it cannot read data with the specified format.
(In this latter case,
the function does not read subsequent formats.)
When called without arguments,
it uses a default format that reads the next line
(see below).

The available formats are
@description{

@item{@St{n}|
reads a numeral and returns it as a float or an integer,
following the lexical conventions of Lua.
(The numeral may have leading whitespaces and a sign.)
This format always reads the longest input sequence that
is a valid prefix for a numeral;
if that prefix does not form a valid numeral
(e.g., an empty string, @St{0x}, or @St{3.4e-})
or it is too long (more than 200 characters),
it is discarded and the format returns @fail.
}

@item{@St{a}|
reads the whole file, starting at the current position.
On end of file, it returns the empty string;
this format never fails.
}

@item{@St{l}|
reads the next line skipping the end of line,
returning @fail on end of file.
This is the default format.
}

@item{@St{L}|
reads the next line keeping the end-of-line character (if present),
returning @fail on end of file.
}

@item{@emph{number}|
reads a string with up to this number of bytes,
returning @fail on end of file.
If @id{number} is zero,
it reads nothing and returns an empty string,
or @fail on end of file.
}

}
The formats @St{l} and @St{L} should be used only for text files.

}

@LibEntry{file:seek ([whence [, offset]])|

Sets and gets the file position,
measured from the beginning of the file,
to the position given by @id{offset} plus a base
specified by the string @id{whence}, as follows:
@description{
@item{@St{set}| base is position 0 (beginning of the file);}
@item{@St{cur}| base is current position;}
@item{@St{end}| base is end of file;}
}
In case of success, @id{seek} returns the final file position,
measured in bytes from the beginning of the file.
If @id{seek} fails, it returns @fail,
plus a string describing the error.

The default value for @id{whence} is @T{"cur"},
and for @id{offset} is 0.
Therefore, the call @T{file:seek()} returns the current
file position, without changing it;
the call @T{file:seek("set")} sets the position to the
beginning of the file (and returns 0);
and the call @T{file:seek("end")} sets the position to the
end of the file, and returns its size.

}

@LibEntry{file:setvbuf (mode [, size])|

Sets the buffering mode for a file.
There are three available modes:
@description{
@item{@St{no}| no buffering.}
@item{@St{full}| full buffering.}
@item{@St{line}| line buffering.}
}

For the last two cases,
@id{size} is a hint for the size of the buffer, in bytes.
The default is an appropriate size.

The specific behavior of each mode is non portable;
check the underlying @ANSI{setvbuf} in your platform for
more details.

}

@LibEntry{file:write (@Cdots)|

Writes the value of each of its arguments to @id{file}.
The arguments must be strings or numbers.

In case of success, this function returns @id{file}.

}

}

@sect2{oslib| @title{Operating System Facilities}

This library is implemented through table @defid{os}.


@LibEntry{os.clock ()|

Returns an approximation of the amount in seconds of CPU time
used by the program,
as returned by the underlying @ANSI{clock}.

}

@LibEntry{os.date ([format [, time]])|

Returns a string or a table containing date and time,
formatted according to the given string @id{format}.

If the @id{time} argument is present,
this is the time to be formatted
(see the @Lid{os.time} function for a description of this value).
Otherwise, @id{date} formats the current time.

If @id{format} starts with @Char{!},
then the date is formatted in Coordinated Universal Time.
After this optional character,
if @id{format} is the string @St{*t},
then @id{date} returns a table with the following fields:
@id{year}, @id{month} (1@En{}12), @id{day} (1@En{}31),
@id{hour} (0@En{}23), @id{min} (0@En{}59),
@id{sec} (0@En{}61, due to leap seconds),
@id{wday} (weekday, 1@En{}7, Sunday @N{is 1}),
@id{yday} (day of the year, 1@En{}366),
and @id{isdst} (daylight saving flag, a boolean).
This last field may be absent
if the information is not available.

If @id{format} is not @St{*t},
then @id{date} returns the date as a string,
formatted according to the same rules as the @ANSI{strftime}.

If @id{format} is absent, it defaults to @St{%c},
which gives a human-readable date and time representation
using the current locale.

On non-POSIX systems,
this function may be not @x{thread safe}
because of its reliance on @CId{gmtime} and @CId{localtime}.

}

@LibEntry{os.difftime (t2, t1)|

Returns the difference, in seconds,
from time @id{t1} to time @id{t2}
(where the times are values returned by @Lid{os.time}).
In @x{POSIX}, @x{Windows}, and some other systems,
this value is exactly @id{t2}@M{-}@id{t1}.

}

@LibEntry{os.execute ([command])|

This function is equivalent to the @ANSI{system}.
It passes @id{command} to be executed by an operating system shell.
Its first result is @true
if the command terminated successfully,
or @fail otherwise.
After this first result
the function returns a string plus a number,
as follows:
@description{

@item{@St{exit}|
the command terminated normally;
the following number is the exit status of the command.
}

@item{@St{signal}|
the command was terminated by a signal;
the following number is the signal that terminated the command.
}

}

When called without a @id{command},
@id{os.execute} returns a boolean that is true if a shell is available.

}

@LibEntry{os.exit ([code [, close]])|

Calls the @ANSI{exit} to terminate the host program.
If @id{code} is @Rw{true},
the returned status is @idx{EXIT_SUCCESS};
if @id{code} is @Rw{false},
the returned status is @idx{EXIT_FAILURE};
if @id{code} is a number,
the returned status is this number.
The default value for @id{code} is @Rw{true}.

If the optional second argument @id{close} is true,
closes the Lua state before exiting.

}

@LibEntry{os.getenv (varname)|

Returns the value of the process environment variable @id{varname}
or @fail if the variable is not defined.

}

@LibEntry{os.remove (filename)|

Deletes the file (or empty directory, on @x{POSIX} systems)
with the given name.
If this function fails, it returns @fail
plus a string describing the error and the error code.
Otherwise, it returns true.

}

@LibEntry{os.rename (oldname, newname)|

Renames the file or directory named @id{oldname} to @id{newname}.
If this function fails, it returns @fail,
plus a string describing the error and the error code.
Otherwise, it returns true.

}

@LibEntry{os.setlocale (locale [, category])|

Sets the current locale of the program.
@id{locale} is a system-dependent string specifying a locale;
@id{category} is an optional string describing which category to change:
@T{"all"}, @T{"collate"}, @T{"ctype"},
@T{"monetary"}, @T{"numeric"}, or @T{"time"};
the default category is @T{"all"}.
The function returns the name of the new locale,
or @fail if the request cannot be honored.

If @id{locale} is the empty string,
the current locale is set to an implementation-defined native locale.
If @id{locale} is the string @St{C},
the current locale is set to the standard C locale.

When called with @nil as the first argument,
this function only returns the name of the current locale
for the given category.

This function may be not @x{thread safe}
because of its reliance on @CId{setlocale}.

}

@LibEntry{os.time ([table])|

Returns the current time when called without arguments,
or a time representing the local date and time specified by the given table.
This table must have fields @id{year}, @id{month}, and @id{day},
and may have fields
@id{hour} (default is 12),
@id{min} (default is 0),
@id{sec} (default is 0),
and @id{isdst} (default is @nil).
Other fields are ignored.
For a description of these fields, see the @Lid{os.date} function.

When the function is called,
the values in these fields do not need to be inside their valid ranges.
For instance, if @id{sec} is -10,
it means 10 seconds before the time specified by the other fields;
if @id{hour} is 1000,
it means 1000 hours after the time specified by the other fields.

The returned value is a number, whose meaning depends on your system.
In @x{POSIX}, @x{Windows}, and some other systems,
this number counts the number
of seconds since some given start time (the @Q{epoch}).
In other systems, the meaning is not specified,
and the number returned by @id{time} can be used only as an argument to
@Lid{os.date} and @Lid{os.difftime}.

When called with a table,
@id{os.time} also normalizes all the fields
documented in the @Lid{os.date} function,
so that they represent the same time as before the call
but with values inside their valid ranges.

}

@LibEntry{os.tmpname ()|

Returns a string with a file name that can
be used for a temporary file.
The file must be explicitly opened before its use
and explicitly removed when no longer needed.

In @x{POSIX} systems,
this function also creates a file with that name,
to avoid security risks.
(Someone else might create the file with wrong permissions
in the time between getting the name and creating the file.)
You still have to open the file to use it
and to remove it (even if you do not use it).

When possible,
you may prefer to use @Lid{io.tmpfile},
which automatically removes the file when the program ends.

}

}

@sect2{debuglib| @title{The Debug Library}

This library provides
the functionality of the @link{debugI|debug interface} to Lua programs.
You should exert care when using this library.
Several of its functions
violate basic assumptions about Lua code
(e.g., that variables local to a function
cannot be accessed from outside;
that userdata metatables cannot be changed by Lua code;
that Lua programs do not crash)
and therefore can compromise otherwise secure code.
Moreover, some functions in this library may be slow.

All functions in this library are provided
inside the @defid{debug} table.
All functions that operate over a thread
have an optional first argument which is the
thread to operate over.
The default is always the current thread.


@LibEntry{debug.debug ()|

Enters an interactive mode with the user,
running each string that the user enters.
Using simple commands and other debug facilities,
the user can inspect global and local variables,
change their values, evaluate expressions, and so on.
A line containing only the word @id{cont} finishes this function,
so that the caller continues its execution.

Note that commands for @id{debug.debug} are not lexically nested
within any function and so have no direct access to local variables.

}

@LibEntry{debug.gethook ([thread])|

Returns the current hook settings of the thread, as three values:
the current hook function, the current hook mask,
and the current hook count,
as set by the @Lid{debug.sethook} function.

Returns @fail if there is no active hook.

}

@LibEntry{debug.getinfo ([thread,] f [, what])|

Returns a table with information about a function.
You can give the function directly
or you can give a number as the value of @id{f},
which means the function running at level @id{f} of the call stack
of the given thread:
@N{level 0} is the current function (@id{getinfo} itself);
@N{level 1} is the function that called @id{getinfo}
(except for tail calls, which do not count in the stack);
and so on.
If @id{f} is a number greater than the number of active functions,
then @id{getinfo} returns @fail.

The returned table can contain all the fields returned by @Lid{lua_getinfo},
with the string @id{what} describing which fields to fill in.
The default for @id{what} is to get all information available,
except the table of valid lines.
If present,
the option @Char{f}
adds a field named @id{func} with the function itself.
If present,
the option @Char{L}
adds a field named @id{activelines} with the table of
valid lines.

For instance, the expression @T{debug.getinfo(1,"n").name} returns
a name for the current function,
if a reasonable name can be found,
and the expression @T{debug.getinfo(print)}
returns a table with all available information
about the @Lid{print} function.

}

@LibEntry{debug.getlocal ([thread,] f, local)|

This function returns the name and the value of the local variable
with index @id{local} of the function at level @id{f} of the stack.
This function accesses not only explicit local variables,
but also parameters and temporary values.

The first parameter or local variable has @N{index 1}, and so on,
following the order that they are declared in the code,
counting only the variables that are active
in the current scope of the function.
Compile-time constants may not appear in this listing,
if they were optimized away by the compiler.
Negative indices refer to vararg arguments;
@num{-1} is the first vararg argument.
The function returns @fail
if there is no variable with the given index,
and raises an error when called with a level out of range.
(You can call @Lid{debug.getinfo} to check whether the level is valid.)

Variable names starting with @Char{(} (open parenthesis) @C{)}
represent variables with no known names
(internal variables such as loop control variables,
and variables from chunks saved without debug information).

The parameter @id{f} may also be a function.
In that case, @id{getlocal} returns only the name of function parameters.

}

@LibEntry{debug.getmetatable (value)|

Returns the metatable of the given @id{value}
or @nil if it does not have a metatable.

}

@LibEntry{debug.getregistry ()|

Returns the registry table @see{registry}.

}

@LibEntry{debug.getupvalue (f, up)|

This function returns the name and the value of the upvalue
with index @id{up} of the function @id{f}.
The function returns @fail
if there is no upvalue with the given index.

(For Lua functions,
upvalues are the external local variables that the function uses,
and that are consequently included in its closure.)

For @N{C functions}, this function uses the empty string @T{""}
as a name for all upvalues.

Variable name @Char{?} (interrogation mark)
represents variables with no known names
(variables from chunks saved without debug information).

}

@LibEntry{debug.getuservalue (u, n)|

Returns the @id{n}-th user value associated
to the userdata @id{u} plus a boolean,
@false if the userdata does not have that value.

}

@LibEntry{debug.setcstacklimit (limit)|

Sets a new limit for the C stack.
This limit controls how deeply nested calls can go in Lua,
with the intent of avoiding a stack overflow.
A limit too small restricts recursive calls pointlessly;
a limit too large exposes the interpreter to stack-overflow crashes.
Unfortunately, there is no way to know a priori
the maximum safe limit for a platform.

Each call made from Lua code counts one unit.
Other operations (e.g., calls made from C to Lua or resuming a coroutine)
may have a higher cost.

This function has the following restrictions:
@description{
@item{It can only be called from the main coroutine (thread);}
@item{It cannot be called while handling a stack-overflow error;}
@item{@id{limit} must be less than 40000;}
@item{@id{limit} cannot be less than the amount of C stack in use.}
}
If a call does not respect some restriction,
it returns a false value.
Otherwise,
the call returns the old limit.

}

@LibEntry{debug.sethook ([thread,] hook, mask [, count])|

Sets the given function as the debug hook.
The string @id{mask} and the number @id{count} describe
when the hook will be called.
The string mask may have any combination of the following characters,
with the given meaning:
@description{
@item{@Char{c}| the hook is called every time Lua calls a function;}
@item{@Char{r}| the hook is called every time Lua returns from a function;}
@item{@Char{l}| the hook is called every time Lua enters a new line of code.}
}
Moreover,
with a @id{count} different from zero,
the hook is called also after every @id{count} instructions.

When called without arguments,
@Lid{debug.sethook} turns off the hook.

When the hook is called, its first parameter is a string
describing the event that has triggered its call:
@T{"call"}, @T{"tail call"}, @T{"return"},
@T{"line"}, and @T{"count"}.
For line events,
the hook also gets the new line number as its second parameter.
Inside a hook,
you can call @id{getinfo} with @N{level 2} to get more information about
the running function.
(@N{Level 0} is the @id{getinfo} function,
and @N{level 1} is the hook function.)

}

@LibEntry{debug.setlocal ([thread,] level, local, value)|

This function assigns the value @id{value} to the local variable
with index @id{local} of the function at level @id{level} of the stack.
The function returns @fail if there is no local
variable with the given index,
and raises an error when called with a @id{level} out of range.
(You can call @id{getinfo} to check whether the level is valid.)
Otherwise, it returns the name of the local variable.

See @Lid{debug.getlocal} for more information about
variable indices and names.

}

@LibEntry{debug.setmetatable (value, table)|

Sets the metatable for the given @id{value} to the given @id{table}
(which can be @nil).
Returns @id{value}.

}

@LibEntry{debug.setupvalue (f, up, value)|

This function assigns the value @id{value} to the upvalue
with index @id{up} of the function @id{f}.
The function returns @fail if there is no upvalue
with the given index.
Otherwise, it returns the name of the upvalue.

See @Lid{debug.getupvalue} for more information about upvalues.

}

@LibEntry{debug.setuservalue (udata, value, n)|

Sets the given @id{value} as
the @id{n}-th user value associated to the given @id{udata}.
@id{udata} must be a full userdata.

Returns @id{udata},
or @fail if the userdata does not have that value.

}

@LibEntry{debug.traceback ([thread,] [message [, level]])|

If @id{message} is present but is neither a string nor @nil,
this function returns @id{message} without further processing.
Otherwise,
it returns a string with a traceback of the call stack.
The optional @id{message} string is appended
at the beginning of the traceback.
An optional @id{level} number tells at which level
to start the traceback
(default is 1, the function calling @id{traceback}).

}

@LibEntry{debug.upvalueid (f, n)|

Returns a unique identifier (as a light userdata)
for the upvalue numbered @id{n}
from the given function.

These unique identifiers allow a program to check whether different
closures share upvalues.
Lua closures that share an upvalue
(that is, that access a same external local variable)
will return identical ids for those upvalue indices.

}

@LibEntry{debug.upvaluejoin (f1, n1, f2, n2)|

Make the @id{n1}-th upvalue of the Lua closure @id{f1}
refer to the @id{n2}-th upvalue of the Lua closure @id{f2}.

}

}

}


@C{-------------------------------------------------------------------------}
@sect1{lua-sa| @title{Lua Standalone}

Although Lua has been designed as an extension language,
to be embedded in a host @N{C program},
it is also frequently used as a standalone language.
An interpreter for Lua as a standalone language,
called simply @id{lua},
is provided with the standard distribution.
The @x{standalone interpreter} includes
all standard libraries.
Its usage is:
@verbatim{
lua [options] [script [args]]
}
The options are:
@description{
@item{@T{-e @rep{stat}}| execute string @rep{stat};}
@item{@T{-i}| enter interactive mode after running @rep{script};}
@item{@T{-l @rep{mod}}| @Q{require} @rep{mod} and assign the
  result to global @rep{mod};}
@item{@T{-v}| print version information;}
@item{@T{-E}| ignore environment variables;}
@item{@T{-W}| turn warnings on;}
@item{@T{--}| stop handling options;}
@item{@T{-}| execute @id{stdin} as a file and stop handling options.}
}
After handling its options, @id{lua} runs the given @emph{script}.
When called without arguments,
@id{lua} behaves as @T{lua -v -i}
when the standard input (@id{stdin}) is a terminal,
and as @T{lua -} otherwise.

When called without the option @T{-E},
the interpreter checks for an environment variable @defid{LUA_INIT_5_4}
(or @defid{LUA_INIT} if the versioned name is not defined)
before running any argument.
If the variable content has the format @T{@At@rep{filename}},
then @id{lua} executes the file.
Otherwise, @id{lua} executes the string itself.

When called with the option @T{-E},
Lua does not consult any environment variables.
In particular,
the values of @Lid{package.path} and @Lid{package.cpath}
are set with the default paths defined in @id{luaconf.h}.

The options @T{-e}, @T{-l}, and @T{-W} are handled in
the order they appear.
For instance, an invocation like
@verbatim{
$ lua -e 'a=1' -llib1 script.lua
}
will first set @id{a} to 1, then require the library @id{lib1},
and finally run the file @id{script.lua} with no arguments.
(Here @T{$} is the shell prompt. Your prompt may be different.)

Before running any code,
@id{lua} collects all command-line arguments
in a global table called @id{arg}.
The script name goes to index 0,
the first argument after the script name goes to index 1,
and so on.
Any arguments before the script name
(that is, the interpreter name plus its options)
go to negative indices.
For instance, in the call
@verbatim{
$ lua -la b.lua t1 t2
}
the table is like this:
@verbatim{
arg = { [-2] = "lua", [-1] = "-la",
        [0] = "b.lua",
        [1] = "t1", [2] = "t2" }
}
If there is no script in the call,
the interpreter name goes to index 0,
followed by the other arguments.
For instance, the call
@verbatim{
$ lua -e "print(arg[1])"
}
will print @St{-e}.
If there is a script,
the script is called with arguments
@T{arg[1]}, @Cdots, @T{arg[#arg]}.
Like all chunks in Lua,
the script is compiled as a vararg function.

In interactive mode,
Lua repeatedly prompts and waits for a line.
After reading a line,
Lua first try to interpret the line as an expression.
If it succeeds, it prints its value.
Otherwise, it interprets the line as a statement.
If you write an incomplete statement,
the interpreter waits for its completion
by issuing a different prompt.

If the global variable @defid{_PROMPT} contains a string,
then its value is used as the prompt.
Similarly, if the global variable @defid{_PROMPT2} contains a string,
its value is used as the secondary prompt
(issued during incomplete statements).

In case of unprotected errors in the script,
the interpreter reports the error to the standard error stream.
If the error object is not a string but
has a metamethod @idx{__tostring},
the interpreter calls this metamethod to produce the final message.
Otherwise, the interpreter converts the error object to a string
and adds a stack traceback to it.
When warnings are on,
they are simply printed in the standard error output.

When finishing normally,
the interpreter closes its main Lua state
@seeF{lua_close}.
The script can avoid this step by
calling @Lid{os.exit} to terminate.

To allow the use of Lua as a
script interpreter in Unix systems,
Lua skips the first line of a file chunk if it starts with @T{#}.
Therefore, Lua scripts can be made into executable programs
by using @T{chmod +x} and @N{the @T{#!}} form,
as in
@verbatim{
#!/usr/local/bin/lua
}
Of course,
the location of the Lua interpreter may be different in your machine.
If @id{lua} is in your @id{PATH},
then
@verbatim{
#!/usr/bin/env lua
}
is a more portable solution.

}


@sect1{incompat| @title{Incompatibilities with the Previous Version}

@simplesect{

Here we list the incompatibilities that you may find when moving a program
from @N{Lua 5.3} to @N{Lua 5.4}.

You can avoid some incompatibilities by compiling Lua with
appropriate options (see file @id{luaconf.h}).
However,
all these compatibility options will be removed in the future.
More often than not,
compatibility issues arise when these compatibility options
are removed.
So, whenever you have the chance,
you should try to test your code with a version of Lua compiled
with all compatibility options turned off.
That will ease transitions to newer versions of Lua.

Lua versions can always change the C API in ways that
do not imply source-code changes in a program,
such as the numeric values for constants
or the implementation of functions as macros.
Therefore,
you should never assume that binaries are compatible between
different Lua versions.
Always recompile clients of the Lua API when
using a new version.

Similarly, Lua versions can always change the internal representation
of precompiled chunks;
precompiled chunks are not compatible between different Lua versions.

The standard paths in the official distribution may
change between versions.

}

@sect2{@title{Incompatibilities in the Language}
@itemize{

@item{
The coercion of strings to numbers in
arithmetic and bitwise operations
has been removed from the core language.
The string library does a similar job
for arithmetic (but not for bitwise) operations
using the string metamethods.
However, unlike in previous versions,
the new implementation preserves the implicit type of the numeral
in the string.
For instance, the result of @T{"1" + "2"} now is an integer,
not a float.
}

@item{
Literal decimal integer constants that overflow are read as floats,
instead of wrapping around.
You can use hexadecimal notation for such constants if you
want the old behavior
(reading them as integers with wrap around).
}

@item{
The use of the @idx{__lt} metamethod to emulate @id{__le}
has been removed.
When needed, this metamethod must be explicitly defined.
}

@item{
The semantics of the numerical @Rw{for} loop
over integers changed in some details.
In particular, the control variable never wraps around.
}

@item{
A label for a @Rw{goto} cannot be declared where a label with the same
name is visible, even if this other label is declared in an enclosing
block.
}

@item{
When finalizing an object,
Lua does not ignore @idx{__gc} metamethods that are not functions.
Any value will be called, if present.
(Non-callable values will generate a warning,
like any other error when calling a finalizer.)
}

}

}

@sect2{@title{Incompatibilities in the Libraries}
@itemize{

@item{
The function @Lid{print} does not call @Lid{tostring}
to format its arguments;
instead, it has this functionality hardwired.
You should use @id{__tostring} to modify how values are printed.
}

@item{
The pseudo-random number generator used by the function @Lid{math.random}
now starts with a somewhat random seed.
Moreover, it uses a different algorithm.
}

@item{
By default, the decoding functions in the @Lid{utf8} library
do not accept surrogates as valid code points.
An extra parameter in these functions makes them more permissive.
}

@item{
The options @St{setpause} and @St{setstepmul}
of the function @Lid{collectgarbage} are deprecated.
You should use the new option @St{incremental} to set them.
}

@item{
The function @Lid{io.lines} now returns four values,
instead of just one.
That can be a problem when it is used as the sole
argument to another function that has optional parameters,
such as in @T{load(io.lines(filename, "L"))}.
To fix that issue,
you can wrap the call into parentheses,
to adjust its number of results to one.
}

}

}

@sect2{@title{Incompatibilities in the API}

@itemize{

@item{
Full userdata now has an arbitrary number of associated user values.
Therefore, the functions @id{lua_newuserdata},
@id{lua_setuservalue}, and @id{lua_getuservalue} were
replaced by @Lid{lua_newuserdatauv},
@Lid{lua_setiuservalue}, and @Lid{lua_getiuservalue},
which have an extra argument.

For compatibility, the old names still work as macros assuming
one single user value.
Note, however, that userdata with zero user values
are more efficient memory-wise.
}

@item{
The function @Lid{lua_resume} has an extra parameter.
This out parameter returns the number of values on
the top of the stack that were yielded or returned by the coroutine.
(In previous versions,
those values were the entire stack.)
}

@item{
The function @Lid{lua_version} returns the version number,
instead of an address of the version number.
The Lua core should work correctly with libraries using their
own static copies of the same core,
so there is no need to check whether they are using the same
address space.
}

@item{
The constant @id{LUA_ERRGCMM} was removed.
Errors in finalizers are never propagated;
instead, they generate a warning.
}

@item{
The options @idx{LUA_GCSETPAUSE} and @idx{LUA_GCSETSTEPMUL}
of the function @Lid{lua_gc} are deprecated.
You should use the new option @id{LUA_GCINC} to set them.
}

}

}

}


@C{[===============================================================}

@sect1{BNF| @title{The Complete Syntax of Lua}

Here is the complete syntax of Lua in extended BNF.
As usual in extended BNF,
@bnfNter{{A}} means 0 or more @bnfNter{A}s,
and @bnfNter{[A]} means an optional @bnfNter{A}.
(For operator precedences, see @See{prec};
for a description of the terminals
@bnfNter{Name}, @bnfNter{Numeral},
and @bnfNter{LiteralString}, see @See{lexical}.)
@index{grammar}

@Produc{

@producname{chunk}@producbody{block}

@producname{block}@producbody{@bnfrep{stat} @bnfopt{retstat}}

@producname{stat}@producbody{
	@bnfter{;}
@OrNL	varlist @bnfter{=} explist
@OrNL	functioncall
@OrNL	label
@OrNL   @Rw{break}
@OrNL   @Rw{goto} Name
@OrNL	@Rw{do} block @Rw{end}
@OrNL	@Rw{while} exp @Rw{do} block @Rw{end}
@OrNL	@Rw{repeat} block @Rw{until} exp
@OrNL	@Rw{if} exp @Rw{then} block
	@bnfrep{@Rw{elseif} exp @Rw{then} block}
	@bnfopt{@Rw{else} block} @Rw{end}
@OrNL	@Rw{for} @bnfNter{Name} @bnfter{=} exp @bnfter{,} exp @bnfopt{@bnfter{,} exp}
	@Rw{do} block @Rw{end}
@OrNL   @Rw{for} namelist @Rw{in} explist @Rw{do} block @Rw{end}
@OrNL	@Rw{function} funcname funcbody
@OrNL	@Rw{local} @Rw{function} @bnfNter{Name} funcbody
@OrNL	@Rw{local} attnamelist @bnfopt{@bnfter{=} explist}
}

@producname{attnamelist}@producbody{
  @bnfNter{Name} attrib @bnfrep{@bnfter{,} @bnfNter{Name} attrib}}

@producname{attrib}@producbody{@bnfopt{@bnfter{<} @bnfNter{Name} @bnfter{>}}}

@producname{retstat}@producbody{@Rw{return}
                                   @bnfopt{explist} @bnfopt{@bnfter{;}}}

@producname{label}@producbody{@bnfter{::} Name @bnfter{::}}

@producname{funcname}@producbody{@bnfNter{Name} @bnfrep{@bnfter{.} @bnfNter{Name}}
                              @bnfopt{@bnfter{:} @bnfNter{Name}}}

@producname{varlist}@producbody{var @bnfrep{@bnfter{,} var}}

@producname{var}@producbody{
	@bnfNter{Name}
@Or	prefixexp @bnfter{[} exp @bnfter{]}
@Or	prefixexp @bnfter{.} @bnfNter{Name}
}

@producname{namelist}@producbody{@bnfNter{Name} @bnfrep{@bnfter{,} @bnfNter{Name}}}


@producname{explist}@producbody{exp @bnfrep{@bnfter{,} exp}}

@producname{exp}@producbody{
	@Rw{nil}
@Or	@Rw{false}
@Or	@Rw{true}
@Or	@bnfNter{Numeral}
@Or	@bnfNter{LiteralString}
@Or	@bnfter{...}
@Or	functiondef
@OrNL	prefixexp
@Or	tableconstructor
@Or	exp binop exp
@Or	unop exp
}

@producname{prefixexp}@producbody{var @Or functioncall @Or @bnfter{(} exp @bnfter{)}}

@producname{functioncall}@producbody{
	prefixexp args
@Or	prefixexp @bnfter{:} @bnfNter{Name} args
}

@producname{args}@producbody{
	@bnfter{(} @bnfopt{explist} @bnfter{)}
@Or	tableconstructor
@Or	@bnfNter{LiteralString}
}

@producname{functiondef}@producbody{@Rw{function} funcbody}

@producname{funcbody}@producbody{@bnfter{(} @bnfopt{parlist} @bnfter{)} block @Rw{end}}

@producname{parlist}@producbody{namelist @bnfopt{@bnfter{,} @bnfter{...}}
  @Or @bnfter{...}}

@producname{tableconstructor}@producbody{@bnfter{@Open} @bnfopt{fieldlist} @bnfter{@Close}}

@producname{fieldlist}@producbody{field @bnfrep{fieldsep field} @bnfopt{fieldsep}}

@producname{field}@producbody{@bnfter{[} exp @bnfter{]} @bnfter{=} exp @Or @bnfNter{Name} @bnfter{=} exp @Or exp}

@producname{fieldsep}@producbody{@bnfter{,} @Or @bnfter{;}}

@producname{binop}@producbody{
  @bnfter{+} @Or @bnfter{-} @Or @bnfter{*} @Or @bnfter{/} @Or @bnfter{//}
    @Or @bnfter{^} @Or @bnfter{%}
        @OrNL
  @bnfter{&} @Or @bnfter{~} @Or @bnfter{|} @Or @bnfter{>>} @Or @bnfter{<<}
    @Or @bnfter{..}
        @OrNL
  @bnfter{<} @Or @bnfter{<=} @Or @bnfter{>} @Or @bnfter{>=}
    @Or @bnfter{==} @Or @bnfter{~=}
        @OrNL
  @Rw{and} @Or @Rw{or}}

@producname{unop}@producbody{@bnfter{-} @Or @Rw{not} @Or @bnfter{#} @Or
  @bnfter{~}}

}

}

@C{]===============================================================}

}
@C{)]-------------------------------------------------------------------------}
