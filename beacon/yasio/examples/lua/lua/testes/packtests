NAME=$1"-tests"

ln -s . $NAME
ln -s .. ltests

tar --create --gzip --no-recursion --file=$NAME.tar.gz  \
$NAME/all.lua  \
$NAME/api.lua  \
$NAME/attrib.lua  \
$NAME/big.lua  \
$NAME/bitwise.lua  \
$NAME/bwcoercion.lua  \
$NAME/calls.lua  \
$NAME/closure.lua  \
$NAME/code.lua  \
$NAME/constructs.lua  \
$NAME/coroutine.lua  \
$NAME/cstack.lua  \
$NAME/db.lua  \
$NAME/errors.lua  \
$NAME/events.lua  \
$NAME/files.lua  \
$NAME/gc.lua  \
$NAME/gengc.lua  \
$NAME/goto.lua  \
$NAME/heavy.lua  \
$NAME/literals.lua  \
$NAME/locals.lua  \
$NAME/main.lua  \
$NAME/math.lua  \
$NAME/nextvar.lua  \
$NAME/pm.lua  \
$NAME/sort.lua  \
$NAME/strings.lua  \
$NAME/tpack.lua  \
$NAME/utf8.lua  \
$NAME/vararg.lua  \
$NAME/verybig.lua  \
$NAME/libs/makefile  \
$NAME/libs/P1  \
$NAME/libs/lib1.c  \
$NAME/libs/lib11.c  \
$NAME/libs/lib2.c \
$NAME/libs/lib21.c  \
$NAME/libs/lib22.c  \
$NAME/ltests/ltests.h \
$NAME/ltests/ltests.c

\rm -I $NAME
\rm -I ltests

echo ${NAME}.tar.gz" created"


