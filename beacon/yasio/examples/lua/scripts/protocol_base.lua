-- This file is generated by x-studio 10.0.9000.1 © 2020, All rights reserved.
-- Module: protocol_base, <please write module description>
-- Author: halx99
-- Create Date: [2020-01-02 22:50:31]
local proto = require 'protocol_enums'
local yasio = require 'yasio'
local obstream = yasio.obstream

proto.begin_encode = function (id)
    local obs = obstream.new()
    obs:push32() -- alloc length field
    obs:write_u16(id) -- command_id
    obs:write_u16(1) -- version
    obs:write_i32(0) -- reserved
    obs:write_i32(0) -- reserved2 low
    obs:write_i32(0) -- reserved2 high
    return obs
end

proto.begin_decode = function(ibs)
    ibs:seek(4, yasio.SEEK_CUR) -- skip length
    local msg = {
        header = {
            command_id = ibs:read_u16(),
            version = ibs:read_u16(),
            reserved = ibs:read_i32(),
            reserved2_low = ibs:read_i32(),
            reserved2_high = ibs:read_i32()
        }
    }
    return msg
end

return proto
