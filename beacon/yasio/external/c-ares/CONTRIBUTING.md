Contributing to c-ares
======================

To contribute patches to c-ares, please generate a GitHub pull request
and follow these guidelines:

 - Check that the Travis builds are green for your pull request.
 - Please update the test suite to add a test case for any new functionality.
 - Build the library with `./configure --enable-debug --enable-maintainer-mode` and
   ensure there are no new warnings.

To improve the chances of the c-ares maintainers responding to your request:

 - Also send an email to the mailing list at `<EMAIL>` describing your change.
 - To follow any associated discussion, please subscribe to the [mailing list](http://cool.haxx.se/mailman/listinfo/c-ares).
