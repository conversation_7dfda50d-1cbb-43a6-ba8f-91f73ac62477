
# Copyright (C) 2009-2013 by <PERSON>
#
# Permission to use, copy, modify, and distribute this
# software and its documentation for any purpose and without
# fee is hereby granted, provided that the above copyright
# notice appear in all copies and that both that copyright
# notice and this permission notice appear in supporting
# documentation, and that the name of M.I.T. not be used in
# advertising or publicity pertaining to distribution of the
# software without specific, written prior permission.
# M.I.T. makes no representations about the suitability of
# this software for any purpose.  It is provided "as is"
# without express or implied warranty.

# ------------------------------------------------------------------------------
#
# Makefile for building c-ares libraries and sample programs with MSVC.
#
# Usage: nmake /f makefile.msvc CFG=<config> <target>
#
# <config> must be one of: [ lib-release | lib-debug | dll-release | dll-debug }
# <target> must be one of: [ ALL | c-ares | acountry | adig | ahost | clean }
#
# If a <target> other than ALL or clean is given, <config> becomes mandatory.
#
# If neither <config> nor <target> are specified this results in
# all targets being built for all <config> c-ares library types.
#
# This makefile must be processed from the subdir where it is located.
#
# All results are generated below a subdirectory named msvcXXX.
#
# ------------------------------------------------------------------------------

NAME = cares

# ------------------------------------------------
# c-ares static and dynamic libraries common base
# file names for release and debug configurations
# ------------------------------------------------

STA_LIB_REL = lib$(NAME)
DYN_LIB_REL = $(NAME)
STA_LIB_DBG = $(STA_LIB_REL)d
DYN_LIB_DBG = $(DYN_LIB_REL)d

# -------------------------------------------
# Base names for c-ares DLL import libraries
# -------------------------------------------

IMP_LIB_REL = $(DYN_LIB_REL)
IMP_LIB_DBG = $(DYN_LIB_DBG)

# --------------------------
# Runtime library selection
# --------------------------

RTLIB  = /MD
RTLIBD = /MDd

!IF "$(RTLIBCFG)" == "static"
RTLIB  = /MT
RTLIBD = /MTd
!ENDIF

# --------------------------------------------------------
# Define USE_WATT32 to 1 to use the Watt-32 tcp/ip stack,
# otherwise Winsock tcp/ip stack will be used as default.
# --------------------------------------------------------

USE_WATT32 = 0

# --------------------------------------------------------
# Detect compiler version.
# --------------------------------------------------------
!INCLUDE .\msvc_ver.inc

# ----------------------------------------------------
# Verify that current subdir is the c-ares source one
# ----------------------------------------------------

!IF ! EXIST(.\src\lib\ares_init.c)
!  MESSAGE Can not process Makefile.msvc from outside of c-ares source subdirectory.
!  MESSAGE Change to the subdirectory where Makefile.msvc is found, and try again.
!  ERROR   See previous message.
!ENDIF

# ------------------------------------------------------------------
# Base subdir is the common root from which other subdirs will hang.
# ------------------------------------------------------------------

BASE_DIR = .\msvc

# ----------------------------------------
# Subdir holding sources for all projects
# ----------------------------------------

SRCDIR = .

# -----------------------------
# Default installation subdirs
# -----------------------------

!IFNDEF INSTALL_DIR
INSTALL_DIR = .
!ENDIF

!IFNDEF INSTALL_DIR_LIB
INSTALL_DIR_LIB = $(INSTALL_DIR)\lib
!ENDIF

!IFNDEF INSTALL_DIR_INC
INSTALL_DIR_INC = $(INSTALL_DIR)\include
!ENDIF

# -------------------------
# Configuration validation
# -------------------------

VALID_CFGSET = FALSE
!IF "$(CFG)" == "lib-release" || "$(CFG)" == "lib-debug" || \
    "$(CFG)" == "dll-release" || "$(CFG)" == "dll-debug"
VALID_CFGSET = TRUE
!ENDIF

!IF "$(VALID_CFGSET)" == "FALSE" && "$(CFG)" != ""
!  MESSAGE MSVC c-ares makefile
!  MESSAGE
!  MESSAGE Usage: nmake /f makefile.msvc CFG=<config> <target>
!  MESSAGE
!  MESSAGE <config> must be one of: [ lib-release | lib-debug | dll-release | dll-debug }
!  MESSAGE <target> must be one of: [ ALL | c-ares | acountry | adig | ahost | clean }
!  MESSAGE
!  MESSAGE If a <target> other than ALL or clean is given, <config> becomes mandatory.
!  MESSAGE
!  MESSAGE If neither <config> nor <target> are specified this results in
!  MESSAGE all targets being built for all <config> c-ares library types.
!  MESSAGE
!  ERROR Choose a valid configuration.
!ENDIF

# --------------------------------------------------------
# Project subdirs independent of configuration being used
# --------------------------------------------------------

CARES_DIR = $(BASE_DIR)\cares
PROG1_DIR = $(BASE_DIR)\acountry
PROG2_DIR = $(BASE_DIR)\adig
PROG3_DIR = $(BASE_DIR)\ahost

# ---------------------------------------------------
# Subdirs which are configuration dependent are only
# defined when a valid configuration has been given.
# ---------------------------------------------------

!IF "$(VALID_CFGSET)" == "TRUE"
CARES_OUTDIR = $(CARES_DIR)\$(CFG)
PROG1_OUTDIR = $(PROG1_DIR)\$(CFG)
PROG2_OUTDIR = $(PROG2_DIR)\$(CFG)
PROG3_OUTDIR = $(PROG3_DIR)\$(CFG)
CARES_OBJDIR = $(CARES_OUTDIR)\obj
PROG1_OBJDIR = $(PROG1_OUTDIR)\obj
PROG2_OBJDIR = $(PROG2_OUTDIR)\obj
PROG3_OBJDIR = $(PROG3_OUTDIR)\obj
!ELSE
!UNDEF CARES_OUTDIR
!UNDEF PROG1_OUTDIR
!UNDEF PROG2_OUTDIR
!UNDEF PROG3_OUTDIR
!UNDEF CARES_OBJDIR
!UNDEF PROG1_OBJDIR
!UNDEF PROG2_OBJDIR
!UNDEF PROG3_OBJDIR
!ENDIF

# -------------------------------------
# Settings that depend on tcp/ip stack
# -------------------------------------

!IF "$(USE_WATT32)" == "1"
CFLAGS  = /UWIN32 /DWATT32 /I$(WATT_ROOT)\inc
EX_LIBS_REL = $(WATT_ROOT)\lib\wattcpvc_imp.lib
EX_LIBS_DBG = $(WATT_ROOT)\lib\wattcpvc_imp_d.lib
!ELSE
CFLAGS  = /DWIN32 /D_WIN32_WINNT=0x0600
EX_LIBS_REL = ws2_32.lib advapi32.lib kernel32.lib
EX_LIBS_DBG = ws2_32.lib advapi32.lib kernel32.lib
!ENDIF

# -------------------------------------------------
# Switches that depend on ancient compiler versions
# -------------------------------------------------

!IF $(CC_VERS_NUM) == 60
PDB_NONE            = /pdb:none
PDBTYPE_CONSOLIDATE = /pdbtype:consolidate
!ELSE
!UNDEF PDB_NONE
!UNDEF PDBTYPE_CONSOLIDATE
!ENDIF

!IF $(CC_VERS_NUM) <= 70
RT_ERROR_CHECKING = /GZ
!ELSE
RT_ERROR_CHECKING = /RTCsu
!ENDIF

# ----------------------------
# Assorted commands and flags
# ----------------------------

CC_CMD_REL = cl.exe /nologo $(RTLIB) /DNDEBUG /O2
CC_CMD_DBG = cl.exe /nologo $(RTLIBD) /D_DEBUG /Od /Zi $(RT_ERROR_CHECKING)
CC_CFLAGS  = $(CFLAGS) /I.\src\lib /I.\include /W3 /EHsc /FD

RC_CMD_REL = rc.exe /l 0x409 /d "NDEBUG"
RC_CMD_DBG = rc.exe /l 0x409 /d "_DEBUG"

LINK_CMD_LIB = link.exe /lib /nologo
LINK_CMD_DLL = link.exe /dll /nologo /incremental:no /fixed:no
LINK_CMD_EXE = link.exe /nologo /incremental:no /fixed:no /subsystem:console

LINK_CMD_EXE_REL = $(LINK_CMD_EXE) /release $(PDB_NONE)
LINK_CMD_EXE_DBG = $(LINK_CMD_EXE) /debug $(PDBTYPE_CONSOLIDATE)

# ---------------------------------
# Configuration dependent settings
# ---------------------------------

!IF "$(CFG)" == "lib-release"
CARES_TARGET = $(STA_LIB_REL).lib
CARES_CFLAGS = /DCARES_BUILDING_LIBRARY /DCARES_STATICLIB
CARES_LFLAGS =
SPROG_CFLAGS = /DCARES_STATICLIB
SPROG_LFLAGS = /libpath:$(CARES_OUTDIR) $(EX_LIBS_REL) $(STA_LIB_REL).lib
CARES_LINK   = $(LINK_CMD_LIB)
SPROG_LINK   = $(LINK_CMD_EXE_REL)
CC_CMD       = $(CC_CMD_REL)
!ENDIF

!IF "$(CFG)" == "lib-debug"
CARES_TARGET = $(STA_LIB_DBG).lib
CARES_CFLAGS = /DCARES_BUILDING_LIBRARY /DCARES_STATICLIB /DDEBUGBUILD
CARES_LFLAGS =
SPROG_CFLAGS = /DCARES_STATICLIB
SPROG_LFLAGS = /libpath:$(CARES_OUTDIR) $(EX_LIBS_DBG) $(STA_LIB_DBG).lib
CARES_LINK   = $(LINK_CMD_LIB)
SPROG_LINK   = $(LINK_CMD_EXE_DBG)
CC_CMD       = $(CC_CMD_DBG)
!ENDIF

!IF "$(CFG)" == "dll-release"
CARES_TARGET = $(DYN_LIB_REL).dll
CARES_CFLAGS = /DCARES_BUILDING_LIBRARY
CARES_LFLAGS = /release $(EX_LIBS_REL) /implib:$(CARES_OUTDIR)\$(IMP_LIB_REL).lib $(PDB_NONE)
SPROG_CFLAGS =
SPROG_LFLAGS = /libpath:$(CARES_OUTDIR) $(EX_LIBS_REL) $(IMP_LIB_REL).lib
CARES_LINK   = $(LINK_CMD_DLL)
SPROG_LINK   = $(LINK_CMD_EXE_REL)
CC_CMD       = $(CC_CMD_REL)
USE_RES_FILE = TRUE
RC_CMD       = $(RC_CMD_REL)
!ENDIF

!IF "$(CFG)" == "dll-debug"
CARES_TARGET = $(DYN_LIB_DBG).dll
CARES_CFLAGS = /DCARES_BUILDING_LIBRARY /DDEBUGBUILD
CARES_LFLAGS = /debug $(EX_LIBS_DBG) /implib:$(CARES_OUTDIR)\$(IMP_LIB_DBG).lib /pdb:$(CARES_OUTDIR)\$(DYN_LIB_DBG).pdb $(PDBTYPE_CONSOLIDATE)
SPROG_CFLAGS =
SPROG_LFLAGS = /libpath:$(CARES_OUTDIR) $(EX_LIBS_DBG) $(IMP_LIB_DBG).lib
CARES_LINK   = $(LINK_CMD_DLL)
SPROG_LINK   = $(LINK_CMD_EXE_DBG)
CC_CMD       = $(CC_CMD_DBG)
USE_RES_FILE = TRUE
RC_CMD       = $(RC_CMD_DBG)
!ENDIF

# --------------------------------------------
# Makefile.inc provides lists of source files
# --------------------------------------------

!INCLUDE .\src\lib\Makefile.inc
!INCLUDE .\src\tools\Makefile.inc

# ----------------------------
# Build lists of object files
# ----------------------------

!IF "$(VALID_CFGSET)" == "TRUE"

!IF [ECHO CARES_OBJS=^$(CARES_OBJDIR)\$(CSOURCES: = $(CARES_OBJDIR^)\) > .\cares_objs.inc] == 0
!INCLUDE .\cares_objs.inc
!IF [DEL .\cares_objs.inc]
!ENDIF
!ELSE
!ERROR Problem generating CARES_OBJS list.
!ENDIF
CARES_OBJS = $(CARES_OBJS:.c=.obj)
!IF "$(USE_RES_FILE)" == "TRUE"
CARES_OBJS = $(CARES_OBJS) $(CARES_OBJDIR)\cares.res
!ENDIF

!IF [ECHO PROG1_OBJS=^$(PROG1_OBJDIR)\$(SAMPLESOURCES: = $(PROG1_OBJDIR^)\) > .\prog1_objs.inc] == 0
!INCLUDE .\prog1_objs.inc
!IF [DEL .\prog1_objs.inc]
!ENDIF
!ELSE
!ERROR Problem generating PROG1_OBJS list.
!ENDIF
PROG1_OBJS = $(PROG1_OBJS:.c=.obj)
PROG1_OBJS = $(PROG1_OBJS:/=\)
PROG1_OBJS = $(PROG1_OBJS) $(PROG1_OBJDIR)\acountry.obj

!IF [ECHO PROG2_OBJS=^$(PROG2_OBJDIR)\$(SAMPLESOURCES: = $(PROG2_OBJDIR^)\) > .\prog2_objs.inc] == 0
!INCLUDE .\prog2_objs.inc
!IF [DEL .\prog2_objs.inc]
!ENDIF
!ELSE
!ERROR Problem generating PROG2_OBJS list.
!ENDIF
PROG2_OBJS = $(PROG2_OBJS:.c=.obj)
PROG2_OBJS = $(PROG2_OBJS:/=\)
PROG2_OBJS = $(PROG2_OBJS) $(PROG2_OBJDIR)\adig.obj

!IF [ECHO PROG3_OBJS=^$(PROG3_OBJDIR)\$(SAMPLESOURCES: = $(PROG3_OBJDIR^)\) > .\prog3_objs.inc] == 0
!INCLUDE .\prog3_objs.inc
!IF [DEL .\prog3_objs.inc]
!ENDIF
!ELSE
!ERROR Problem generating PROG3_OBJS list.
!ENDIF
PROG3_OBJS = $(PROG3_OBJS:.c=.obj)
PROG3_OBJS = $(PROG3_OBJS:/=\)
PROG3_OBJS = $(PROG3_OBJS) $(PROG3_OBJDIR)\ahost.obj

!ENDIF


# --------------------------------
# Only our custom inference rules
# --------------------------------

.SUFFIXES:
.SUFFIXES: .c .rc

{$(SRCDIR)\src\lib}.rc{$(CARES_OBJDIR)}.res:
    $(RC_CMD) /Fo $@ $<

{$(SRCDIR)\src\lib}.c{$(CARES_OBJDIR)}.obj:
    $(CC_CMD) $(CC_CFLAGS) $(CARES_CFLAGS) /Fo$@ /Fd$(@D)\ /c $<

{$(SRCDIR)\src\tools}.c{$(PROG1_OBJDIR)}.obj:
    $(CC_CMD) $(CC_CFLAGS) $(SPROG_CFLAGS) /Fo$@ /Fd$(@D)\ /c $<

{$(SRCDIR)\src\tools}.c{$(PROG2_OBJDIR)}.obj:
    $(CC_CMD) $(CC_CFLAGS) $(SPROG_CFLAGS) /Fo$@ /Fd$(@D)\ /c $<

{$(SRCDIR)\src\tools}.c{$(PROG3_OBJDIR)}.obj:
    $(CC_CMD) $(CC_CFLAGS) $(SPROG_CFLAGS) /Fo$@ /Fd$(@D)\ /c $<

# Hack Alert! we reference ../lib/ files in the Makefile.inc for tools as they
# share some files with the library itself.  We need to hack around that here.

{$(SRCDIR)\src\lib}.c{$(PROG1_OBJDIR)\..\lib}.obj:
    $(CC_CMD) $(CC_CFLAGS) $(SPROG_CFLAGS) /Fo$(PROG1_OBJDIR)\$(@F) /Fd$(PROG1_OBJDIR)\ /c $<

{$(SRCDIR)\src\lib}.c{$(PROG2_OBJDIR)\..\lib}.obj:
    $(CC_CMD) $(CC_CFLAGS) $(SPROG_CFLAGS) /Fo$(PROG2_OBJDIR)\$(@F) /Fd$(PROG2_OBJDIR)\ /c $<

{$(SRCDIR)\src\lib}.c{$(PROG3_OBJDIR)\..\lib}.obj:
    $(CC_CMD) $(CC_CFLAGS) $(SPROG_CFLAGS) /Fo$(PROG3_OBJDIR)\$(@F) /Fd$(PROG3_OBJDIR)\ /c $<

# ------------------------------------------------------------- #
# ------------------------------------------------------------- #
# Default target when no CFG library type has been specified,   #
# results in building target ALL for all c-ares library types.  #
# ------------------------------------------------------------- #
# ------------------------------------------------------------- #

!IF "$(VALID_CFGSET)" == "FALSE"

ALL:
    $(MAKE) /NOLOGO /f .\Makefile.msvc  CFG=lib-release  ALL
    $(MAKE) /NOLOGO /f .\Makefile.msvc  CFG=lib-debug    ALL
    $(MAKE) /NOLOGO /f .\Makefile.msvc  CFG=dll-release  ALL
    $(MAKE) /NOLOGO /f .\Makefile.msvc  CFG=dll-debug    ALL

clean:
    @-RMDIR /S /Q $(BASE_DIR) >NUL 2>&1

install:
    @$(MAKE) /nologo /f .\Makefile.msvc  CFG=lib-release  install
    @$(MAKE) /nologo /f .\Makefile.msvc  CFG=lib-debug    install
    @$(MAKE) /nologo /f .\Makefile.msvc  CFG=dll-release  install
    @$(MAKE) /nologo /f .\Makefile.msvc  CFG=dll-debug    install

!ENDIF

# ---------------------------------------------------------------------
# Targets only available when a proper CFG library type has been given
# ---------------------------------------------------------------------

!IF "$(VALID_CFGSET)" == "TRUE"

ALL: c-ares acountry adig ahost
    @

# $(HHEADERS) $(CSOURCES)
c-ares: $(CARES_OBJDIR) $(CARES_OBJS) $(CARES_OUTDIR)
    $(CARES_LINK) $(CARES_LFLAGS) /out:$(CARES_OUTDIR)\$(CARES_TARGET) $(CARES_OBJS)
!   IF "$(USE_RES_FILE)" == "TRUE"
    @if exist $(CARES_OUTDIR)\$(CARES_TARGET).manifest mt -nologo -manifest $(CARES_OUTDIR)\$(CARES_TARGET).manifest -outputresource:$(CARES_OUTDIR)\$(CARES_TARGET);2
!   ENDIF

# acountry.c $(SAMPLESOURCES) $(SAMPLEHEADERS)
acountry: c-ares $(PROG1_OBJDIR) $(PROG1_OBJS) $(PROG1_OUTDIR)
    $(SPROG_LINK) $(SPROG_LFLAGS) /out:$(PROG1_OUTDIR)\acountry.exe $(PROG1_OBJS:..\lib=)
    @if exist $(PROG1_OUTDIR)\acountry.exe.manifest mt -nologo -manifest $(PROG1_OUTDIR)\acountry.exe.manifest -outputresource:$(PROG1_OUTDIR)\acountry.exe;1

# adig.c $(SAMPLESOURCES) $(SAMPLEHEADERS)
adig: c-ares  $(PROG2_OBJDIR) $(PROG2_OBJS) $(PROG2_OUTDIR)
    $(SPROG_LINK) $(SPROG_LFLAGS) /out:$(PROG2_OUTDIR)\adig.exe $(PROG2_OBJS:..\lib=)
    @if exist $(PROG2_OUTDIR)\adig.exe.manifest mt -nologo -manifest $(PROG2_OUTDIR)\adig.exe.manifest -outputresource:$(PROG2_OUTDIR)\adig.exe;1

# ahost.c $(SAMPLESOURCES) $(SAMPLEHEADERS)
ahost: c-ares $(PROG3_OBJDIR) $(PROG3_OBJS) $(PROG3_OUTDIR)
    $(SPROG_LINK) $(SPROG_LFLAGS) /out:$(PROG3_OUTDIR)\ahost.exe $(PROG3_OBJS:..\lib=)
    @if exist $(PROG3_OUTDIR)\ahost.exe.manifest mt -nologo -manifest $(PROG3_OUTDIR)\ahost.exe.manifest -outputresource:$(PROG3_OUTDIR)\ahost.exe;1

$(CARES_OUTDIR): $(CARES_DIR)
    @if not exist $(CARES_OUTDIR) mkdir $(CARES_OUTDIR)

$(PROG1_OUTDIR): $(PROG1_DIR)
    @if not exist $(PROG1_OUTDIR) mkdir $(PROG1_OUTDIR)

$(PROG2_OUTDIR): $(PROG2_DIR)
    @if not exist $(PROG2_OUTDIR) mkdir $(PROG2_OUTDIR)

$(PROG3_OUTDIR): $(PROG3_DIR)
    @if not exist $(PROG3_OUTDIR) mkdir $(PROG3_OUTDIR)

$(CARES_OBJDIR): $(CARES_OUTDIR)
    @if not exist $(CARES_OBJDIR) mkdir $(CARES_OBJDIR)

$(PROG1_OBJDIR): $(PROG1_OUTDIR)
    @if not exist $(PROG1_OBJDIR) mkdir $(PROG1_OBJDIR)

$(PROG2_OBJDIR): $(PROG2_OUTDIR)
    @if not exist $(PROG2_OBJDIR) mkdir $(PROG2_OBJDIR)

$(PROG3_OBJDIR): $(PROG3_OUTDIR)
    @if not exist $(PROG3_OBJDIR) mkdir $(PROG3_OBJDIR)

clean:
    @-RMDIR /S /Q $(CARES_OUTDIR) >NUL 2>&1
    @-RMDIR /S /Q $(PROG1_OUTDIR) >NUL 2>&1
    @-RMDIR /S /Q $(PROG2_OUTDIR) >NUL 2>&1
    @-RMDIR /S /Q $(PROG3_OUTDIR) >NUL 2>&1

install:
    @if not exist $(CARES_OUTDIR)\$(CARES_TARGET) \
        $(MAKE) /f .\Makefile.msvc CFG=$(CFG) c-ares
    @if not exist "$(INSTALL_DIR)"     mkdir "$(INSTALL_DIR)"
    @if not exist "$(INSTALL_DIR_LIB)" mkdir "$(INSTALL_DIR_LIB)"
    @if not exist "$(INSTALL_DIR_INC)" mkdir "$(INSTALL_DIR_INC)"
    @copy /y $(CARES_OUTDIR)\*.*              "$(INSTALL_DIR_LIB)" >NUL
    @copy /y $(SRCDIR)\include\ares.h         "$(INSTALL_DIR_INC)" >NUL
    @copy /y $(SRCDIR)\include\ares_build.h   "$(INSTALL_DIR_INC)" >NUL
    @copy /y $(SRCDIR)\include\ares_rules.h   "$(INSTALL_DIR_INC)" >NUL
    @copy /y $(SRCDIR)\include\ares_version.h "$(INSTALL_DIR_INC)" >NUL
    @echo Installed c-ares $(CFG)

!ENDIF

$(BASE_DIR):
    @if not exist $(BASE_DIR) mkdir $(BASE_DIR)

$(CARES_DIR): $(BASE_DIR)
    @if not exist $(CARES_DIR) mkdir $(CARES_DIR)

$(PROG1_DIR): $(BASE_DIR)
    @if not exist $(PROG1_DIR) mkdir $(PROG1_DIR)

$(PROG2_DIR): $(BASE_DIR)
    @if not exist $(PROG2_DIR) mkdir $(PROG2_DIR)

$(PROG3_DIR): $(BASE_DIR)
    @if not exist $(PROG3_DIR) mkdir $(PROG3_DIR)

# End of Makefile.msvc
