@PACKAGE_INIT@

set_and_check(c-ares_INCLUDE_DIR "@PACKAGE_CMAKE_INSTALL_INCLUDEDIR@")

include("${CMAKE_CURRENT_LIST_DIR}/c-ares-config-version.cmake")
include("${CMAKE_CURRENT_LIST_DIR}/c-ares-targets.cmake")

set(c-ares_LIBRARY c-ares::cares)

if(@CARES_SHARED@)
	add_library(c-ares::cares_shared INTERFACE IMPORTED)
	set_target_properties(c-ares::cares_shared PROPERTIES INTERFACE_LINK_LIBRARIES "c-ares::cares")
	set(c-ares_SHARED_LIBRARY c-ares::cares_shared)
elseif(@CARES_STATIC@)
	add_library(c-ares::cares_static INTERFACE IMPORTED)
	set_target_properties(c-ares::cares_static PROPERTIES INTERFACE_LINK_LIBRARIES "c-ares::cares")
endif()

if(@CARES_STATIC@)
	set(c-ares_STATIC_LIBRARY c-ares::cares_static)
endif()
