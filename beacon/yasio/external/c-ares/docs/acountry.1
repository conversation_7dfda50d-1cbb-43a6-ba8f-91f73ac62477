.TH ACOUNTRY "1" "April 2011" "c-ares utilities"
.SH NAME
acountry \- print the country where an IPv4 address or host is located
.SH SYNOPSIS
.B acountry
[\fIOPTION\fR]... \fIHOST\fR...
.SH DESCRIPTION
.PP
.\" Add any additional description here
.PP
Print the country where <PERSON><PERSON><PERSON> (an IPv4 address or hostname) is located,
using the countries.nerd.dk DNS domain to identify the country.
.PP
This utility comes with the \fBc\-ares\fR asynchronous resolver library.
.SH OPTIONS
.TP
\fB\-d\fR
Print some extra debugging output.
.TP
\fB\-h\fR, \fB\-\-help\fR
Display this help and exit.
.TP
\fB\-v\fR
Be more verbose. Print extra information.
.SH "REPORTING BUGS"
Report bugs to the c-ares mailing list:
.br
\fBhttps://cool.haxx.se/mailman/listinfo/c-ares\fR
.SH "SEE ALSO"
.PP
adig(1), ahost(1).
.PP
The DNSBL countries.nerd.dk
.br
\fBhttp://countries.nerd.dk/\fR
.SH COPYRIGHT
This utility is based on code/ideas contained in sofware written by <PERSON> (ares)
carrying the following notice:
.br
Copyright 1998 by the Massachusetts Institute of Technology.
.br
Permission to use, copy, modify, and distribute this software and its
documentation for any purpose and without fee is hereby granted,
provided that the above copyright notice appear in all copies and that
both that copyright notice and this permission notice appear in
supporting documentation, and that the name of M.I.T. not be used in
advertising or publicity pertaining to distribution of the software
without specific, written prior permission. M.I.T. makes no
representations about the suitability of this software for any
purpose. It is provided "as is" without express or implied warranty.
.br
No further copyright claims are being made by the author(s) of this utility.
.SH AUTHOR
Gisle Vanem
