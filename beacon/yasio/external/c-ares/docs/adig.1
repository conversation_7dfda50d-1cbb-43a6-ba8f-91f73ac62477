.TH ADIG "1" "April 2011" "c-ares utilities"
.SH NAME
adig \- print information collected from Domain Name System (DNS) servers
.SH SYNOPSIS
.B adig
[\fIOPTION\fR]... \fINAME\fR...
.SH DESCRIPTION
.PP
.\" Add any additional description here
.PP
Send queries to DNS servers about \fINAME\fR and print received
information, where \fINAME\fR is a valid DNS name (e.g. www.example.com,
********.in-addr.arpa).
.PP
This utility comes with the \fBc\-ares\fR asynchronous resolver library.
.SH OPTIONS
.TP
\fB\-c\fR class
Set the query class.
Possible values for class are
NY, CHAOS, HS, IN (default).
.TP
\fB\-d\fR
Print some extra debugging output.
.TP
\fB\-f\fR flag
Add a flag.
Possible values for flag are
igntc, noaliases, norecurse, primary, stayopen, usevc.
.TP
\fB\-h\fR, \fB\-\-help\fR
Display this help and exit.
.TP
\fB\-T\fR port
Use specified TCP port to connect to DNS server.
.TP
\fB\-s\fR server
Connect to specified DNS server, instead of the system's default one(s).
.TP
\fB\-t\fR type
Query records of specified type.
Possible values for type are
A (default), AAAA, AFSDB, ANY, AXFR, CNAME, GPOS, HINFO, ISDN, KEY, LOC, MAILA,
MAILB, MB, MD, MF, MG, MINFO, MR, MX, NAPTR, NS, NSAP, NSAP_PTR, NULL,
PTR, PX, RP, RT, SIG, SOA, SRV, TXT, WKS, X25,
.TP
\fB\-U\fR port
Use specified UDP port to connect to DNS server.
.TP
\fB\-x\fR
For an IPv4 \fB-t PTR a.b.c.d\fR lookup, query for
.br
\fBd.c.b.a.in-addr.arpa.\fR
This more often gives correct names in the \fBANSWER\fR.
.br
For an IPv6 \fB-t PTR addr\fR lookup, query for \fBa.b.c....z.IP6.ARPA.\fR
.TP
\fB\-xx\fR
As for \fB-x\fR and an IPv6 address, compact \fBa.b.c....z.IP6.ARPA.\fR into a RFC-2673 bit-string.
This compacted \fBbit-string\fR form is not supported by many DNS-servers.

.SH "REPORTING BUGS"
Report bugs to the c-ares mailing list:
.br
\fBhttps://cool.haxx.se/mailman/listinfo/c-ares\fR
.SH "SEE ALSO"
.PP
acountry(1), ahost(1).
.SH COPYRIGHT
This utility is based on code/ideas contained in sofware written by Greg Hudson (ares)
carrying the following notice:
.br
Copyright 1998 by the Massachusetts Institute of Technology.
.br
Permission to use, copy, modify, and distribute this software and its
documentation for any purpose and without fee is hereby granted,
provided that the above copyright notice appear in all copies and that
both that copyright notice and this permission notice appear in
supporting documentation, and that the name of M.I.T. not be used in
advertising or publicity pertaining to distribution of the software
without specific, written prior permission. M.I.T. makes no
representations about the suitability of this software for any
purpose. It is provided "as is" without express or implied warranty.
.br
No further copyright claims are being made by the author(s) of this utility.
