.TH AHOST "1" "April 2011" "c-ares utilities"
.SH NAME
ahost \- print the A or AAAA record associated with a hostname or IP address
.SH SYNOPSIS
.B ahost
[\fIOPTION\fR]... \fIHOST\fR...
.SH DESCRIPTION
.PP
.\" Add any additional description here
.PP
Look up the DNS A or AAAA record associated with HOST (a hostname or an
IP address).
.PP
This utility comes with the \fBc\-ares\fR asynchronous resolver library.
.SH OPTIONS
.TP
\fB\-d\fR
Print some extra debugging output.
.TP
\fB\-h\fR, \fB\-\-help\fR
Display this help and exit.
.TP
\fB\-t\fR type
If type is "a", print the A record (default).
If type is "aaaa", print the AAAA record.
If type is "u", look for either AAAA or A record (in that order).
.TP
\fB\-s\fR \fIdomain\fP
Specify the \fIdomain\fP to search instead of using the default values from
.br
/etc/resolv.conf. This option only has an effect on platforms that use
.br
/etc/resolv.conf
for DNS configuration; it has no effect on other platforms (such as Win32
or Android).
.SH "REPORTING BUGS"
Report bugs to the c-ares mailing list:
.br
\fBhttps://cool.haxx.se/mailman/listinfo/c-ares\fR
.SH "SEE ALSO"
.PP
acountry(1), adig(1).
.SH COPYRIGHT
This utility is based on code/ideas contained in sofware written by Greg Hudson (ares)
carrying the following notice:
.br
Copyright 1998 by the Massachusetts Institute of Technology.
.br
Permission to use, copy, modify, and distribute this software and its
documentation for any purpose and without fee is hereby granted,
provided that the above copyright notice appear in all copies and that
both that copyright notice and this permission notice appear in
supporting documentation, and that the name of M.I.T. not be used in
advertising or publicity pertaining to distribution of the software
without specific, written prior permission. M.I.T. makes no
representations about the suitability of this software for any
purpose. It is provided "as is" without express or implied warranty.
.br
No further copyright claims are being made by the author(s) of this utility.
