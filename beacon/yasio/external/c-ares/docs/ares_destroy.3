.\"
.\" Copyright 1998 by the Massachusetts Institute of Technology.
.\"
.\" Permission to use, copy, modify, and distribute this
.\" software and its documentation for any purpose and without
.\" fee is hereby granted, provided that the above copyright
.\" notice appear in all copies and that both that copyright
.\" notice and this permission notice appear in supporting
.\" documentation, and that the name of M.I.T. not be used in
.\" advertising or publicity pertaining to distribution of the
.\" software without specific, written prior permission.
.\" M.I.T. makes no representations about the suitability of
.\" this software for any purpose.  It is provided "as is"
.\" without express or implied warranty.
.\"
.TH ARES_DESTROY 3 "7 December 2004"
.SH NAME
ares_destroy \- Destroy a resolver channel
.SH SYNOPSIS
.nf
#include <ares.h>

void ares_destroy(ares_channel \fIchannel\fP)
.fi
.SH DESCRIPTION
The \fBares_destroy(3)\fP function destroys the name service channel
identified by \fIchannel\fP, freeing all memory and closing all sockets used
by the channel.

\fBares_destroy(3)\fP invokes the callbacks for each pending query on the
channel, passing a status of \fIARES_EDESTRUCTION\fP. These calls give the
callbacks a chance to clean up any state which might have been stored in their
arguments. A callback must not add new requests to a channel being destroyed.
.SH SEE ALSO
.BR ares_init (3),
.BR ares_cancel (3)
.SH AUTHOR
Greg Hudson, MIT Information Systems
.br
Copyright 1998 by the Massachusetts Institute of Technology.
