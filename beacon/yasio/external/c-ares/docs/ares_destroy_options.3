.\"
.\" Copyright 1998 by the Massachusetts Institute of Technology.
.\"
.\" Permission to use, copy, modify, and distribute this
.\" software and its documentation for any purpose and without
.\" fee is hereby granted, provided that the above copyright
.\" notice appear in all copies and that both that copyright
.\" notice and this permission notice appear in supporting
.\" documentation, and that the name of M.I.T. not be used in
.\" advertising or publicity pertaining to distribution of the
.\" software without specific, written prior permission.
.\" M.I.T. makes no representations about the suitability of
.\" this software for any purpose.  It is provided "as is"
.\" without express or implied warranty.
.\"
.TH ARES_DESTROY_OPTIONS 3 "1 June 2007"
.SH NAME
ares_destroy_options \- Destroy options initialized with ares_save_options
.SH SYNOPSIS
.nf
.B #include <ares.h>
.PP
.B void ares_destroy_options(struct ares_options *\fIoptions\fP)
.fi
.SH DESCRIPTION
The \fBares_destroy_options(3)\fP function destroys the options struct
identified by \Ioptions\fP, freeing all memory allocated by
\fBares_save_options(3)\fP.
.SH SEE ALSO
.BR ares_save_options (3),
.BR ares_init_options (3)
.SH AUTHOR
Brad House
.br
Copyright 1998 by the Massachusetts Institute of Technology.
