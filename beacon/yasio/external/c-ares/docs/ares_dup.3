.\"
.\" Copyright (C) 2004-2009 by <PERSON>
.\"
.\" Permission to use, copy, modify, and distribute this
.\" software and its documentation for any purpose and without
.\" fee is hereby granted, provided that the above copyright
.\" notice appear in all copies and that both that copyright
.\" notice and this permission notice appear in supporting
.\" documentation, and that the name of M.I.T. not be used in
.\" advertising or publicity pertaining to distribution of the
.\" software without specific, written prior permission.
.\" M.I.T. makes no representations about the suitability of
.\" this software for any purpose.  It is provided "as is"
.\" without express or implied warranty.
.\"
.TH ARES_DUP 3 "26 May 2009"
.SH NAME
ares_dup \- Duplicate a resolver channel
.SH SYNOPSIS
.nf
.B #include <ares.h>
.PP
.B int ares_dup(ares_channel *\fIdest\fP, ares_channel \fIsource\fP)
.fi
.SH DESCRIPTION
The \fBares_dup(3)\fP function duplicates an existing communications channel
for name service lookups.  If it returns successfully, \fBares_dup(3)\fP will
set the variable pointed to by \fIdest\fP to a handle used to identify the
name service channel.  The caller should invoke \fIares_destroy(3)\fP on the
handle when the channel is no longer needed.
.SH SEE ALSO
.BR ares_destroy(3),
.BR ares_init(3),
.BR ares_library_init(3)
.SH AVAILABILITY
\fIares_dup(3)\fP was added in c-ares 1.6.0
.SH AUTHOR
Daniel Stenberg

