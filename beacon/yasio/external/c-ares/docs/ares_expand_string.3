.\"
.\" Copyright 1998 by the Massachusetts Institute of Technology.
.\"
.\" Permission to use, copy, modify, and distribute this
.\" software and its documentation for any purpose and without
.\" fee is hereby granted, provided that the above copyright
.\" notice appear in all copies and that both that copyright
.\" notice and this permission notice appear in supporting
.\" documentation, and that the name of M.I.T. not be used in
.\" advertising or publicity pertaining to distribution of the
.\" software without specific, written prior permission.
.\" M.I.T. makes no representations about the suitability of
.\" this software for any purpose.  It is provided "as is"
.\" without express or implied warranty.
.\"
.TH ARES_EXPAND_NAME 3 "20 Nov 2009"
.SH NAME
ares_expand_string \- Expand a length encoded string
.SH SYNOPSIS
.nf
.B #include <ares.h>
.PP
.B int ares_expand_string(const unsigned char *\fIencoded\fP,
.B      const unsigned char *\fIabuf\fP, int \fIalen\fP, unsigned char **\fIs\fP,
.B 	long *\fIenclen\fP)
.fi
.SH DESCRIPTION
The
.B ares_expand_string
function converts a length encoded string to a NUL-terminated C
string.  The argument
.I encoded
gives the beginning of the encoded string, and the arguments
.I abuf
and
.I alen
give the containing message buffer (necessary for the processing of
indirection pointers within the encoded domain name).  The result is
placed in a NUL-terminated allocated buffer, a pointer to which is
stored in the variable pointed to by
.IR s .
The length of the encoded string is stored in the variable pointed to by
.I enclen
so that the caller can advance past the encoded string to read
further data in the message.
.SH RETURN VALUES
.B ares_expand_string
can return any of the following values:
.TP 15
.B ARES_SUCCESS
Expansion of the encoded string succeeded.
.TP 15
.B ARES_EBADSTR
The encoded string was malformed and could not be expanded.
.TP 15
.B ARES_ENOMEM
Memory was exhausted.
.SH SEE ALSO
.BR ares_free_string (3)
.SH AUTHOR
Dominick Meglio
