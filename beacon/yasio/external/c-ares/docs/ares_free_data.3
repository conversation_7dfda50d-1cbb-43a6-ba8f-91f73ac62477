.\"
.\" Copyright 1998 by the Massachusetts Institute of Technology.
.\" Copyright (C) 2004-2010 by <PERSON>
.\"
.\" Permission to use, copy, modify, and distribute this
.\" software and its documentation for any purpose and without
.\" fee is hereby granted, provided that the above copyright
.\" notice appear in all copies and that both that copyright
.\" notice and this permission notice appear in supporting
.\" documentation, and that the name of M.I.T. not be used in
.\" advertising or publicity pertaining to distribution of the
.\" software without specific, written prior permission.
.\" M.I.T. makes no representations about the suitability of
.\" this software for any purpose.  It is provided "as is"
.\" without express or implied warranty.
.\"
.TH ARES_FREE_DATA 3 "5 March 2010"
.SH NAME
ares_free_data \- Free data allocated by several c-ares functions
.SH SYNOPSIS
.nf
.B #include <ares.h>
.PP
.B void ares_free_data(void *\fIdataptr\fP)
.PP
.B cc file.c -lcares
.fi
.SH DESCRIPTION
.PP
The
.B ares_free_data(3)
function frees one or more data structures allocated and returned
by several c-ares functions. Specifically the data returned by the
following list of functions must be deallocated using this function.
.TP 5
.B ares_get_servers(3)
When used to free the data returned by ares_get_servers(3) this
will free the whole linked list of ares_addr_node structures returned
by ares_get_servers(3).
.TP
.B ares_parse_srv_reply(3)
When used to free the data returned by ares_parse_srv_reply(3) this
will free the whole linked list of ares_srv_reply structures returned
by ares_parse_srv_reply(3), along with any additional storage
associated with those structures.
.TP
.B ares_parse_mx_reply(3)
When used to free the data returned by ares_parse_mx_reply(3) this
will free the whole linked list of ares_mx_reply structures returned
by ares_parse_mx_reply(3), along with any additional storage
associated with those structures.
.TP
.B ares_parse_txt_reply(3)
When used to free the data returned by ares_parse_txt_reply(3) this
will free the whole linked list of ares_txt_reply structures returned
by ares_parse_txt_reply(3), along with any additional storage
associated with those structures.
.TP
.B ares_parse_soa_reply(3)
When used to free the data returned by ares_parse_soa_reply(3) this
will free the ares_soa_reply structure, along with any additional storage
associated with those structure.
.SH RETURN VALUE
The ares_free_data() function does not return a value.
.SH AVAILABILITY
This function was first introduced in c-ares version 1.7.0.
.SH SEE ALSO
.BR ares_get_servers(3),
.BR ares_parse_srv_reply(3),
.BR ares_parse_mx_reply(3),
.BR ares_parse_txt_reply(3),
.BR ares_parse_soa_reply(3)
.SH AUTHOR
Yang Tse
.PP
Copyright 1998 by the Massachusetts Institute of Technology.
.br
Copyright (C) 2004-2010 by Daniel Stenberg.
