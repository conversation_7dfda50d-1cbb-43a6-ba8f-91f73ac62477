.\"
.\" Copyright 1998 by the Massachusetts Institute of Technology.
.\"
.\" Permission to use, copy, modify, and distribute this
.\" software and its documentation for any purpose and without
.\" fee is hereby granted, provided that the above copyright
.\" notice appear in all copies and that both that copyright
.\" notice and this permission notice appear in supporting
.\" documentation, and that the name of M.I.T. not be used in
.\" advertising or publicity pertaining to distribution of the
.\" software without specific, written prior permission.
.\" M.I.T. makes no representations about the suitability of
.\" this software for any purpose.  It is provided "as is"
.\" without express or implied warranty.
.\"
.TH ARES_FREE_HOSTENT 3 "23 July 1998"
.SH NAME
ares_free_hostent \- Free host structure allocated by ares functions
.SH SYNOPSIS
.nf
.B #include <ares.h>
.PP
.B void ares_free_hostent(struct hostent *\fIhost\fP)
.fi
.SH DESCRIPTION
The
.I ares_free_hostent
function frees a
.B struct hostent
allocated by one of the functions \fIares_parse_a_reply(3)\fP,
\fIares_parse_aaaa_reply(3)\fP, or \fIares_parse_ptr_reply(3)\fP.
.SH NOTES
It is not necessary (and is not correct) to free the host structure passed to
the callback functions for \fIares_gethostbyname(3)\fP or
\fIares_gethostbyaddr(3)\fP. c-ares will automatically free such host
structures when the callback returns.
.SH SEE ALSO
.BR ares_parse_a_reply (3),
.BR ares_parse_aaaa_reply (3),
.BR ares_parse_ptr_reply (3),
.BR ares_parse_ns_reply (3)
.SH AUTHOR
Greg Hudson, MIT Information Systems
.br
Copyright 1998 by the Massachusetts Institute of Technology.
