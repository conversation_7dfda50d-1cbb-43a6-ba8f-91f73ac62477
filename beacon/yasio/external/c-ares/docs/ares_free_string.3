.\"
.\" Copyright 2000 by the Massachusetts Institute of Technology.
.\"
.\" Permission to use, copy, modify, and distribute this
.\" software and its documentation for any purpose and without
.\" fee is hereby granted, provided that the above copyright
.\" notice appear in all copies and that both that copyright
.\" notice and this permission notice appear in supporting
.\" documentation, and that the name of M.I.T. not be used in
.\" advertising or publicity pertaining to distribution of the
.\" software without specific, written prior permission.
.\" M.I.T. makes no representations about the suitability of
.\" this software for any purpose.  It is provided "as is"
.\" without express or implied warranty.
.\"
.TH ARES_FREE_STRING 3 "4 February 2004"
.SH NAME
ares_free_string \- Free strings allocated by ares functions
.SH SYNOPSIS
.nf
.B #include <ares.h>
.PP
.B void ares_free_string(void *\fIstr\fP)
.fi
.SH DESCRIPTION
The \fIares_free_string(3)\fP function frees a string allocated by an ares
function.
.SH SEE ALSO
.BR ares_mkquery (3)
.BR ares_expand_string (3)
.SH AUTHOR
<PERSON>, MIT Information Systems
.br
Copyright 2000 by the Massachusetts Institute of Technology.
