.\"
.\" Copyright 1998 by the Massachusetts Institute of Technology.
.\"
.\" Permission to use, copy, modify, and distribute this
.\" software and its documentation for any purpose and without
.\" fee is hereby granted, provided that the above copyright
.\" notice appear in all copies and that both that copyright
.\" notice and this permission notice appear in supporting
.\" documentation, and that the name of M.I.T. not be used in
.\" advertising or publicity pertaining to distribution of the
.\" software without specific, written prior permission.
.\" M.I.T. makes no representations about the suitability of
.\" this software for any purpose.  It is provided "as is"
.\" without express or implied warranty.
.\"
.TH ARES_GETHOSTBYNAME 3 "25 July 1998"
.SH NAME
ares_gethostbyname \- Initiate a host query by name
.SH SYNOPSIS
.nf
.B #include <ares.h>
.PP
.B typedef void (*ares_host_callback)(void *\fIarg\fP, int \fIstatus\fP,
.B	int \fItimeouts\fP, struct hostent *\fIhostent\fP)
.PP
.B void ares_gethostbyname(ares_channel \fIchannel\fP, const char *\fIname\fP,
.B 	int \fIfamily\fP, ares_host_callback \fIcallback\fP, void *\fIarg\fP)
.fi
.SH DESCRIPTION
The
.B ares_gethostbyname
function initiates a host query by name on the name service channel
identified by
.IR channel .
The parameter
.I name
gives the hostname as a NUL-terminated C string, and
.I family
gives the desired type of address for the resulting host entry.  When the
query is complete or has failed, the ares library will invoke \fIcallback\fP.
Completion or failure of the query may happen immediately, or may happen
during a later call to \fIares_process(3)\fP, \fIares_destroy(3)\fP or
\fIares_cancel(3)\fP.
.PP
The callback argument
.I arg
is copied from the
.B ares_gethostbyname
argument
.IR arg .
The callback argument
.I status
indicates whether the query succeeded and, if not, how it failed.  It
may have any of the following values:
.TP 19
.B ARES_SUCCESS
The host lookup completed successfully.
.TP 19
.B ARES_ENOTIMP
The ares library does not know how to find addresses of type
.IR family .
.TP 19
.B ARES_EBADNAME
The hostname
.B name
is composed entirely of numbers and periods, but is not a valid
representation of an Internet address.
.TP 19
.B ARES_ENODATA
There was no data returned to extract a result from.
.TP 19
.B ARES_ENOTFOUND
The name
.I name
was not found.
.TP 19
.B ARES_ENOMEM
Memory was exhausted.
.TP 19
.B ARES_ECANCELLED
The query was cancelled.
.TP 19
.B ARES_EDESTRUCTION
The name service channel
.I channel
is being destroyed; the query will not be completed.
.PP
The callback argument
.I timeouts
reports how many times a query timed out during the execution of the
given request.
.PP
On successful completion of the query, the callback argument
.I hostent
points to a
.B struct hostent
containing the name of the host returned by the query.  The callback
need not and should not attempt to free the memory pointed to by
.IR hostent ;
the ares library will free it when the callback returns.  If the query
did not complete successfully,
.I hostent
will be
.BR NULL .
.SH SEE ALSO
.BR ares_process (3),
.BR ares_gethostbyaddr (3)
.SH AUTHOR
Greg Hudson, MIT Information Systems
.br
Copyright 1998 by the Massachusetts Institute of Technology.
