.\"
.\" Copyright 1998 by <PERSON>
.\"
.\" Permission to use, copy, modify, and distribute this
.\" software and its documentation for any purpose and without
.\" fee is hereby granted, provided that the above copyright
.\" notice appear in all copies and that both that copyright
.\" notice and this permission notice appear in supporting
.\" documentation, and that the name of M.I.T. not be used in
.\" advertising or publicity pertaining to distribution of the
.\" software without specific, written prior permission.
.\" M.I.T. makes no representations about the suitability of
.\" this software for any purpose.  It is provided "as is"
.\" without express or implied warranty.
.\"
.TH ARES_GETSOCK 3 "11 March 2010"
.SH NAME
ares_getsock \- get socket descriptors to wait on
.SH SYNOPSIS
.nf
.B #include <ares.h>
.PP
.B int ares_getsock(ares_channel \fIchannel\fP, ares_socket_t *\fIsocks\fP,
.B int \fInumsocks\fP);
.fi
.SH DESCRIPTION
The
.B ares_getsock
function retrieves the set of socket descriptors which the calling
application should wait on for reading and/or writing for the
processing of name service queries pending on the name service channel
identified by
.IR channel .
Socket descriptors will be set in the socket descriptor array pointed to by
\fIsocks\fP.
\fInumsocks\fP is the size of the given array in number of ints.

This function can only return information about up to 16 sockets. If more are
in use (however unlikely that is), they are simply not reported back.
.SH RETURN VALUES
\fBares_getsock\fP returns a bitmask for what actions to wait for on the
different sockets. The ares.h header file provides these convenience macros to
extract the information appropriately:

.nf
#define ARES_GETSOCK_MAXNUM 16 /* ares_getsock() can return info about
                                  this many sockets */
#define ARES_GETSOCK_READABLE(bits,num) (bits & (1<< (num)))
#define ARES_GETSOCK_WRITABLE(bits,num) (bits & (1 << ((num) + \
                                         ARES_GETSOCK_MAXNUM)))
.fi
.SH NOTES
This function was added in c-ares 1.3.1
.SH SEE ALSO
.BR ares_timeout (3),
.BR ares_fds (3),
.BR ares_process (3)
