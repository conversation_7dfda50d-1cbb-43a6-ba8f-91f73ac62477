.\"
.\" Copyright (C) 2013 by <PERSON>
.\"
.\" Permission to use, copy, modify, and distribute this
.\" software and its documentation for any purpose and without
.\" fee is hereby granted, provided that the above copyright
.\" notice appear in all copies and that both that copyright
.\" notice and this permission notice appear in supporting
.\" documentation, and that the name of M.I.T. not be used in
.\" advertising or publicity pertaining to distribution of the
.\" software without specific, written prior permission.
.\" M.I.T. makes no representations about the suitability of
.\" this software for any purpose.  It is provided "as is"
.\" without express or implied warranty.
.\"
.TH ARES_INET_PTON 3 "17 Feb 2013"
.SH NAME
ares_inet_pton \- convert an IPv4 or IPv6 address from text to binary form
.SH SYNOPSIS
.nf
.B #include <ares.h>
.PP
.B const char *ares_inet_pton(int af, const char *src, void *dst);
.fi
.SH DESCRIPTION
This is a portable version with the identical functionality of the commonly
available \fIinet_pton\fP.

The ares_inet_pton() function converts the address in its standard text
presentation form into its numeric binary form. The \fBaf\fP argument shall
specify the family of the address. The AF_INET and AF_INET6 address families
shall be supported. The \fBsrc\fP argument points to the string being passed
in. The \fBdst\fP argument points to a buffer into which the function stores
the numeric address; this shall be large enough to hold the numeric address
(32 bits for AF_INET, 128 bits for AF_INET6).
.SH SEE ALSO
.BR ares_init(3),
.BR ares_inet_ntop(3)
.SH AVAILABILITY
made properly publicly available in c-ares for real in version 1.10.0
.SH AUTHOR
Daniel Stenberg

