.\"
.\" Copyright 1998 by the Massachusetts Institute of Technology.
.\" Copyright (C) 2004-2010 by <PERSON>
.\"
.\" Permission to use, copy, modify, and distribute this
.\" software and its documentation for any purpose and without
.\" fee is hereby granted, provided that the above copyright
.\" notice appear in all copies and that both that copyright
.\" notice and this permission notice appear in supporting
.\" documentation, and that the name of M.I.T. not be used in
.\" advertising or publicity pertaining to distribution of the
.\" software without specific, written prior permission.
.\" M.I.T. makes no representations about the suitability of
.\" this software for any purpose.  It is provided "as is"
.\" without express or implied warranty.
.\"
.TH ARES_INIT 3 "5 March 2010"
.SH NAME
ares_init \- Initialize a resolver channel
.SH SYNOPSIS
.nf
#include <ares.h>

int ares_init(ares_channel *\fIchannelptr\fP)
.fi
.SH DESCRIPTION
The \fBares_init(3)\fP function initializes a communications channel for name
service lookups.  If it returns successfully, \fBares_init(3)\fP will set the
variable pointed to by \fIchannelptr\fP to a handle used to identify the name
service channel.  The caller should invoke \fIares_destroy(3)\fP on the handle
when the channel is no longer needed.

The \fIares_init_options(3)\fP function is provide to offer more init
alternatives.
.SH RETURN VALUES
\fIares_init(3)\fP can return any of the following values:
.TP 14
.B ARES_SUCCESS
Initialization succeeded.
.TP 14
.B ARES_EFILE
A configuration file could not be read.
.TP 14
.B ARES_ENOMEM
The process's available memory was exhausted.
.TP 14
.B ARES_ENOTINITIALIZED
c-ares library initialization not yet performed.
.SH NOTES
When initializing from
.B /etc/resolv.conf,
.BR ares_init (3)
reads the
.I domain
and
.I search
directives to allow lookups of short names relative to the domains
specified. The
.I domain
and
.I search
directives override one another. If more that one instance of either
.I domain
or
.I search
directives is specified, the last occurrence wins. For more information,
please see the
.BR resolv.conf (5)
manual page.
.SH SEE ALSO
.BR ares_init_options(3),
.BR ares_destroy(3),
.BR ares_dup(3),
.BR ares_library_init(3),
.BR ares_set_servers(3)
.SH AUTHOR
Greg Hudson, MIT Information Systems
.br
Copyright 1998 by the Massachusetts Institute of Technology.
.br
Copyright (C) 2004-2010 by Daniel Stenberg.
