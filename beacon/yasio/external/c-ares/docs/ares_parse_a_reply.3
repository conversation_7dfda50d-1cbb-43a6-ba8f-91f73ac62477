.\"
.\" Copyright 1998 by the Massachusetts Institute of Technology.
.\"
.\" Permission to use, copy, modify, and distribute this
.\" software and its documentation for any purpose and without
.\" fee is hereby granted, provided that the above copyright
.\" notice appear in all copies and that both that copyright
.\" notice and this permission notice appear in supporting
.\" documentation, and that the name of M.I.T. not be used in
.\" advertising or publicity pertaining to distribution of the
.\" software without specific, written prior permission.
.\" M.I.T. makes no representations about the suitability of
.\" this software for any purpose.  It is provided "as is"
.\" without express or implied warranty.
.\"
.TH ARES_PARSE_A_REPLY 3 "25 July 1998"
.SH NAME
ares_parse_a_reply \- Parse a reply to a DNS query of type A
.SH SYNOPSIS
.nf
.B #include <ares.h>
.PP
.B int ares_parse_a_reply(const unsigned char *\fIabuf\fP, int \fIalen\fP,
.B 	struct hostent **\fIhost\fP,
.B      struct ares_addrttl *\fIaddrttls\fP, int *\fInaddrttls\fP);
.fi
.SH DESCRIPTION
The
.B ares_parse_a_reply
function parses the response to a query of type A into a
.BR "struct hostent"
and/or an array of
.BR "struct ares_addrttls" . 
The parameters
.I abuf
and
.I alen
give the contents of the response.  The result is stored in allocated
memory and a pointer to it stored into the variable pointed to by
.IR host ,
if host is nonnull.
It is the caller's responsibility to free the resulting host structure
using
.BR ares_free_hostent (3)
when it is no longer needed.
.PP
If
.IR addrttls
and
.IR naddrttls
are both nonnull,
then up to *naddrttls
.BR "struct ares_addrttl"
records are stored in the array pointed to by addrttls,
and then *naddrttls is set to the number of records so stored.
Note that the memory for these records is supplied by the caller.
.SH RETURN VALUES
.B ares_parse_a_reply
can return any of the following values:
.TP 15
.B ARES_SUCCESS
The response was successfully parsed.
.TP 15
.B ARES_EBADRESP
The response was malformatted.
.TP 15
.B ARES_ENODATA
The response did not contain an answer to the query.
.TP 15
.B ARES_ENOMEM
Memory was exhausted.
.SH SEE ALSO
.BR ares_gethostbyname (3),
.BR ares_free_hostent (3)
.SH AUTHOR
Greg Hudson, MIT Information Systems
.br
Andrew Selivanov <<EMAIL>>
.br
Copyright 1998 by the Massachusetts Institute of Technology.
