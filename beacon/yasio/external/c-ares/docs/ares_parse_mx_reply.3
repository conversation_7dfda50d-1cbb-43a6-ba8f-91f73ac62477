.\"
.\" Copyright 1998 by the Massachusetts Institute of Technology.
.\"
.\" Permission to use, copy, modify, and distribute this
.\" software and its documentation for any purpose and without
.\" fee is hereby granted, provided that the above copyright
.\" notice appear in all copies and that both that copyright
.\" notice and this permission notice appear in supporting
.\" documentation, and that the name of M.I.T. not be used in
.\" advertising or publicity pertaining to distribution of the
.\" software without specific, written prior permission.
.\" M.I.T. makes no representations about the suitability of
.\" this software for any purpose.  It is provided "as is"
.\" without express or implied warranty.
.\"
.TH ARES_PARSE_MX_REPLY 3 "4 August 2009"
.SH NAME
ares_parse_mx_reply \- Parse a reply to a DNS query of type MX
.SH SYNOPSIS
.nf
.B #include <ares.h>
.PP
.B int ares_parse_mx_reply(const unsigned char* \fIabuf\fP, int \fIalen\fP,
.B                          struct ares_mx_reply** \fImx_out\fP);
.fi
.SH DESCRIPTION
The
.B ares_parse_mx_reply
function parses the response to a query of type MX into a
linked list of
.I struct ares_mx_reply 
The parameters
.I abuf
and
.I alen
give the contents of the response.  The result is stored in allocated
memory and a pointer to it stored into the variable pointed to by
.IR mx_out .
It is the caller's responsibility to free the resulting
.IR mx_out
structure when it is no longer needed using the function
.B ares_free_data
.PP
The structure 
.I ares_mx_reply
contains the following fields:
.sp
.in +4n
.nf
struct ares_mx_reply {
    struct ares_mx_reply *next;
    char *host;
    unsigned short priority;
};
.fi
.in
.PP
.SH RETURN VALUES
.B ares_parse_mx_reply
can return any of the following values:
.TP 15
.B ARES_SUCCESS
The response was successfully parsed.
.TP 15
.B ARES_EBADRESP
The response was malformatted.
.TP 15
.B ARES_ENODATA
The response did not contain an answer to the query.
.TP 15
.B ARES_ENOMEM
Memory was exhausted.
.SH AVAILABILITY
This function was first introduced in c-ares version 1.7.2.
.SH SEE ALSO
.BR ares_query (3)
.BR ares_free_data (3)
.SH AUTHOR
Written by Jeremy Lal <<EMAIL>>
