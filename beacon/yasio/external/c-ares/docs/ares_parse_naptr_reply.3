.\"
.\" Copyright 1998 by the Massachusetts Institute of Technology.
.\"
.\" Permission to use, copy, modify, and distribute this
.\" software and its documentation for any purpose and without
.\" fee is hereby granted, provided that the above copyright
.\" notice appear in all copies and that both that copyright
.\" notice and this permission notice appear in supporting
.\" documentation, and that the name of M.I.T. not be used in
.\" advertising or publicity pertaining to distribution of the
.\" software without specific, written prior permission.
.\" M.I.T. makes no representations about the suitability of
.\" this software for any purpose.  It is provided "as is"
.\" without express or implied warranty.
.\"
.TH ARES_PARSE_NAPTR_REPLY 3 "23 February 2012"
.SH NAME
ares_parse_naptr_reply \- Parse a reply to a DNS query of type NAPTR
.SH SYNOPSIS
.nf
.B #include <ares.h>
.PP
.B int ares_parse_naptr_reply(const unsigned char* \fIabuf\fP, int \fIalen\fP,
.B                          struct ares_naptr_reply** \fInaptr_out\fP);
.fi
.SH DESCRIPTION
The
.B ares_parse_naptr_reply
function parses the response to a query of type NAPTR into a
linked list of
.I struct ares_naptr_reply 
The parameters
.I abuf
and
.I alen
give the contents of the response.  The result is stored in allocated
memory and a pointer to it stored into the variable pointed to by
.IR naptr_out .
It is the caller's responsibility to free the resulting
.IR naptr_out
structure when it is no longer needed using the function
.B ares_free_data
.PP
The structure 
.I ares_naptr_reply
contains the following fields:
.sp
.in +4n
.nf
struct ares_naptr_reply {
    struct ares_naptr_reply *next;
    unsigned char *flags;
    unsigned char *service;
    unsigned char *regexp;
    char *replacement;
    unsigned short order;
    unsigned short preference;
};
.fi
.in
.PP
.SH RETURN VALUES
.B ares_parse_naptr_reply
can return any of the following values:
.TP 15
.B ARES_SUCCESS
The response was successfully parsed.
.TP 15
.B ARES_EBADRESP
The response was malformatted.
.TP 15
.B ARES_ENODATA
The response did not contain an answer to the query.
.TP 15
.B ARES_ENOMEM
Memory was exhausted.
.SH AVAILABILITY
This function was first introduced in c-ares version 1.7.6.
.SH SEE ALSO
.BR ares_query (3)
.BR ares_free_data (3)
.SH AUTHOR
Written by Jakub Hrozek <<EMAIL>>, on behalf of Red Hat, Inc http://www.redhat.com
