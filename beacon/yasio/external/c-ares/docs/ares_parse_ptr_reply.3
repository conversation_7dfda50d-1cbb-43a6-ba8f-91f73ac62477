.\"
.\" Copyright 1998 by the Massachusetts Institute of Technology.
.\"
.\" Permission to use, copy, modify, and distribute this
.\" software and its documentation for any purpose and without
.\" fee is hereby granted, provided that the above copyright
.\" notice appear in all copies and that both that copyright
.\" notice and this permission notice appear in supporting
.\" documentation, and that the name of M.I.T. not be used in
.\" advertising or publicity pertaining to distribution of the
.\" software without specific, written prior permission.
.\" M.I.T. makes no representations about the suitability of
.\" this software for any purpose.  It is provided "as is"
.\" without express or implied warranty.
.\"
.TH ARES_PARSE_PTR_REPLY 3 "25 July 1998"
.SH NAME
ares_parse_ptr_reply \- Parse a reply to a DNS query of type PTR into a hostent
.SH SYNOPSIS
.nf
.B #include <ares.h>
.PP
.B int ares_parse_ptr_reply(const unsigned char *\fIabuf\fP, int \fIalen\fP,
.B 	const void *\fIaddr\fP, int \fIaddrlen\fP, int \fIfamily\fP,
.B	struct hostent **\fIhost\fP);
.fi
.SH DESCRIPTION
The
.B ares_parse_ptr_reply
function parses the response to a query of type PTR into a
.BR "struct hostent" .
The parameters
.I abuf
and
.I alen
give the contents of the response.  The parameters
.IR addr ,
.IR addrlen ,
and
.I family
specify which address was queried for; they are not used to verify the
response, merely used to fill in the address of the
.BR "struct hostent" .
The resulting
.B struct hostent
is stored in allocated memory and a pointer to it stored into the
variable pointed to by
.IR host .
It is the caller's responsibility to free the resulting host structure
using
.BR ares_free_hostent (3)
when it is no longer needed.
.SH RETURN VALUES
.B ares_parse_ptr_reply
can return any of the following values:
.TP 15
.B ARES_SUCCESS
The response was successfully parsed.
.TP 15
.B ARES_EBADRESP
The response was malformatted.
.TP 15
.B ARES_ENODATA
The response did not contain an answer to the query.
.TP 15
.B ARES_ENOMEM
Memory was exhausted.
.SH SEE ALSO
.BR ares_gethostbyaddr (3),
.BR ares_free_hostent (3)
.SH AUTHOR
Greg Hudson, MIT Information Systems
.br
Copyright 1998 by the Massachusetts Institute of Technology.
