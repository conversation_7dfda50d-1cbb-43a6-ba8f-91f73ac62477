.\"
.\" Copyright 1998 by the Massachusetts Institute of Technology.
.\"
.\" Permission to use, copy, modify, and distribute this
.\" software and its documentation for any purpose and without
.\" fee is hereby granted, provided that the above copyright
.\" notice appear in all copies and that both that copyright
.\" notice and this permission notice appear in supporting
.\" documentation, and that the name of M.I.T. not be used in
.\" advertising or publicity pertaining to distribution of the
.\" software without specific, written prior permission.
.\" M.I.T. makes no representations about the suitability of
.\" this software for any purpose.  It is provided "as is"
.\" without express or implied warranty.
.\"
.TH ARES_PROCESS 3 "25 July 1998"
.SH NAME
ares_process \- Process events for name resolution
.SH SYNOPSIS
.nf
#include <ares.h>

void ares_process(ares_channel \fIchannel\fP,
                  fd_set *\fIread_fds\fP,
                  fd_set *\fIwrite_fds\fP)

void ares_process_fd(ares_channel \fIchannel\fP,
                     ares_socket_t \fIread_fd\fP,
                     ares_socket_t \fIwrite_fd\fP)
.fi
.SH DESCRIPTION
The \fBares_process(3)\fP function handles input/output events and timeouts
associated with queries pending on the name service channel identified by
.IR channel .
The file descriptor sets pointed to by \fIread_fds\fP and \fIwrite_fds\fP
should have file descriptors set in them according to whether the file
descriptors specified by \fIares_fds(3)\fP are ready for reading and writing.
(The easiest way to determine this information is to invoke \fBselect(3)\fP
with a timeout no greater than the timeout given by \fIares_timeout(3)\fP).

The \fBares_process(3)\fP function will invoke callbacks for pending queries
if they complete successfully or fail.

\fBares_process_fd(3)\fP works the same way but acts and operates only on the
specific file descriptors (sockets) you pass in to the function. Use
ARES_SOCKET_BAD for "no action". This function is provided to allow users of
c-ares to void \fIselect(3)\fP in their applications and within c-ares.

To only process possible timeout conditions without a socket event occurring,
one may pass NULL as the values for both \fIread_fds\fP and \fIwrite_fds\fP for
\fBares_process(3)\fP, or ARES_SOCKET_BAD for both \fIread_fd\fP and
\fIwrite_fd\fP for \fBares_process_fd(3)\fP.
.SH EXAMPLE
The following code fragment waits for all pending queries on a channel
to complete:

.nf
int nfds, count;
fd_set readers, writers;
struct timeval tv, *tvp;

while (1) {
  FD_ZERO(&readers);
  FD_ZERO(&writers);
  nfds = ares_fds(channel, &readers, &writers);
  if (nfds == 0)
    break;
  tvp = ares_timeout(channel, NULL, &tv);
  count = select(nfds, &readers, &writers, NULL, tvp);
  ares_process(channel, &readers, &writers);
}
.fi
.SH SEE ALSO
.BR ares_fds (3),
.BR ares_timeout (3)
.SH AUTHOR
Greg Hudson, MIT Information Systems
.br
Copyright 1998 by the Massachusetts Institute of Technology.
