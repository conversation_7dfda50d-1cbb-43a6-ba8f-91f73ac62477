.\"
.\" Copyright 1998 by the Massachusetts Institute of Technology.
.\"
.\" Permission to use, copy, modify, and distribute this
.\" software and its documentation for any purpose and without
.\" fee is hereby granted, provided that the above copyright
.\" notice appear in all copies and that both that copyright
.\" notice and this permission notice appear in supporting
.\" documentation, and that the name of M.I.T. not be used in
.\" advertising or publicity pertaining to distribution of the
.\" software without specific, written prior permission.
.\" M.I.T. makes no representations about the suitability of
.\" this software for any purpose.  It is provided "as is"
.\" without express or implied warranty.
.\"
.TH ARES_SEND 3 "25 July 1998"
.SH NAME
ares_send \- Initiate a DNS query
.SH SYNOPSIS
.nf
.B #include <ares.h>
.PP
.B typedef void (*ares_callback)(void *\fIarg\fP, int \fIstatus\fP,
.B	int \fItimeouts\fP, unsigned char *\fIabuf\fP, int \fIalen\fP)
.PP
.B void ares_send(ares_channel \fIchannel\fP, const unsigned char *\fIqbuf\fP,
.B 	int \fIqlen\fP, ares_callback \fIcallback\fP, void *\fIarg\fP)
.fi
.SH DESCRIPTION
The
.B ares_send
function initiates a DNS query on the name service channel identified
by
.IR channel .
The parameters
.I qbuf
and
.I qlen
give the DNS query, which should already have been formatted according
to the DNS protocol.  When the query is complete or has failed, the
ares library will invoke
.IR callback .
Completion or failure of the query may happen immediately, or may
happen during a later call to
.BR ares_process (3)
or
.BR ares_destroy (3).
.PP
The callback argument
.I arg
is copied from the
.B ares_send
argument
.IR arg .
The callback argument
.I status
indicates whether the query succeeded and, if not, how it failed.  It
may have any of the following values:
.TP 19
.B ARES_SUCCESS
The query completed.
.TP 19
.B ARES_EBADQUERY
The query buffer was poorly formed (was not long enough for a DNS
header or was too long for TCP transmission).
.TP 19
.B ARES_ETIMEOUT
No name servers responded within the timeout period.
.TP 19
.B ARES_ECONNREFUSED
No name servers could be contacted.
.TP 19
.B ARES_ENOMEM
Memory was exhausted.
.TP 19
.B ARES_ECANCELLED
The query was cancelled.
.TP 19
.B ARES_EDESTRUCTION
The name service channel
.I channel
is being destroyed; the query will not be completed.
.PP
The callback argument
.I timeouts
reports how many times a query timed out during the execution of the
given request.
.PP
If the query completed, the callback argument
.I abuf
points to a result buffer of length
.IR alen .
If the query did not complete,
.I abuf
will be NULL and
.I alen
will be 0.
.PP
Unless the flag
.B ARES_FLAG_NOCHECKRESP
was set at channel initialization time,
.B ares_send
will normally ignore responses whose questions do not match the
questions in
.IR qbuf ,
as well as responses with reply codes of
.BR SERVFAIL ,
.BR NOTIMP ,
and
.BR REFUSED .
Unlike other query functions in the ares library, however,
.B ares_send
does not inspect the header of the reply packet to determine the error
status, so a callback status of
.B ARES_SUCCESS
does not reflect as much about the response as for other query
functions.
.SH SEE ALSO
.BR ares_process (3)
.SH AUTHOR
Greg Hudson, MIT Information Systems
.br
Copyright 1998 by the Massachusetts Institute of Technology.
