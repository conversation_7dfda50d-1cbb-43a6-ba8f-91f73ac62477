.\"
.\" Copyright 2010 by <PERSON> <<EMAIL>>
.\"
.\" Permission to use, copy, modify, and distribute this
.\" software and its documentation for any purpose and without
.\" fee is hereby granted, provided that the above copyright
.\" notice appear in all copies and that both that copyright
.\" notice and this permission notice appear in supporting
.\" documentation, and that the name of M.I.T. not be used in
.\" advertising or publicity pertaining to distribution of the
.\" software without specific, written prior permission.
.\" M.I.T. makes no representations about the suitability of
.\" this software for any purpose.  It is provided "as is"
.\" without express or implied warranty.
.\"
.TH ARES_SET_LOCAL_IP6 3 "30 June 2010"
.SH NAME
ares_set_local_ip6 \- Set local IPv6 address outgoing requests.
.SH SYNOPSIS
.nf
.B #include <ares.h>
.PP
.B void ares_set_local_ip6(ares_channel \fIchannel\fP, const unsigned char* \fIlocal_ip6\fP)
.fi
.SH DESCRIPTION
The \fBares_set_local_ip6\fP function sets the IPv6 address for outbound
IPv6 requests.  The parameter \fIlocal_ip6\fP is specified in network byte
order.  This allows users to specify outbound interfaces when used on
multi-homed systems.  The local_ip6 argument must be 16 bytes in length.
.SH SEE ALSO
.BR ares_set_local_ip4 (3)
.SH NOTES
This function was added in c-ares 1.7.4
.SH AUTHOR
Ben Greear
