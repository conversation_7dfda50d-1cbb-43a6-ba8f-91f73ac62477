.\"
.\" Copyright 2010 by <PERSON> <<EMAIL>>
.\"
.\" Permission to use, copy, modify, and distribute this
.\" software and its documentation for any purpose and without
.\" fee is hereby granted, provided that the above copyright
.\" notice appear in all copies and that both that copyright
.\" notice and this permission notice appear in supporting
.\" documentation, and that the name of M.I.T. not be used in
.\" advertising or publicity pertaining to distribution of the
.\" software without specific, written prior permission.
.\" M.I.T. makes no representations about the suitability of
.\" this software for any purpose.  It is provided "as is"
.\" without express or implied warranty.
.\"
.TH ARES_SET_SERVERS 3 "5 March 2010"
.SH NAME
ares_set_servers, ares_set_servers_ports \- Initialize an ares_channel name servers configuration
.SH SYNOPSIS
.nf
.B #include <ares.h>
.PP
.B int ares_set_servers(ares_channel \fIchannel\fP, struct ares_addr_node *\fIservers\fP)
.B int ares_set_servers_ports(ares_channel \fIchannel\fP, struct ares_addr_port_node *\fIservers\fP)
.fi
.SH DESCRIPTION
The \fBares_set_servers(3)\fP function initializes name servers configuration
for the channel data identified by
.IR channel ,
from a
.IR servers
pointer to a linked list of ares_addr_node structs holding name servers
address data.
.PP
The name server linked list pointer argument may be the result of a previous
call to \fBares_get_servers(3)\fP or a linked list of \fBares_addr_node\fP structs
set up by other means.
.PP
The \fBares_set_servers(3)\fP function also allows the specification of UDP and
TCP ports to be used for communication on a per-server basis.  The provided
linked list argument may be the result of a previous call to
\fBares_get_servers_ports(3)\fP or a linked list of \fBares_addr_port_node\fP structs
set up by other means.
.PP
This function replaces any potentially previously configured name servers
with the ones given in the linked list. So, in order to configure a channel
with more than one name server all the desired ones must be specified in a
single list.
.PP
The function does not take ownership of the linked list argument.
The caller is responsible for freeing the linked list when no longer needed.
.PP
This function is capable of handling IPv4 and IPv6 name server
addresses simultaneously, rendering \fBares_init_options(3)\fP with
optmask \fBARES_OPT_SERVERS\fP functionally obsolete except for
IPv4-only name server usage.

.SH RETURN VALUES
.B ares_set_servers(3)
may return any of the following values:
.TP 15
.B ARES_SUCCESS
The name servers configuration was successfully initialized.
.TP 15
.B ARES_ENOMEM
The process's available memory was exhausted.
.TP 15
.B ARES_ENODATA
The channel data identified by 
.IR channel
was invalid.
.TP 15
.B ARES_ENOTINITIALIZED
c-ares library initialization not yet performed.
.TP 15
.B ARES_ENOTIMP
Changing name servers configuration while queries are outstanding is not implemented.
.SH SEE ALSO
.BR ares_set_servers_csv (3),
.BR ares_get_servers (3),
.BR ares_init_options (3),
.BR ares_dup(3)
.SH AVAILABILITY
\fBares_set_servers(3)\fP was added in c-ares 1.7.1;
\fBares_set_servers_ports(3)\fP was added in c-ares 1.11.0.
.SH AUTHOR
Implementation of this function and associated library internals are based
on code, comments and feedback provided in November and December of 2008 by
Daniel Stenberg, Gregor Jasny, Phil Blundell and Yang Tse, December 2009
by Cedric Bail, February 2010 by Jakub Hrozek. On March 2010 Yang Tse
shuffled all the bits and this function popped out.
.br
Copyright 1998 by the Massachusetts Institute of Technology.
.br
Copyright (C) 2008-2010 by Daniel Stenberg
