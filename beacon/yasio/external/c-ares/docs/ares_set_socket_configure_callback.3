.\"
.TH ARES_SET_SOCKET_CONFIGURE_CALLBACK 3 "6 Feb 2016"
.SH NAME
ares_set_socket_configure_callback \- Set a socket configuration callback
.SH SYNOPSIS
.nf
.B #include <ares.h>
.PP
.B typedef int (*ares_sock_config_callback)(ares_socket_t \fIsocket_fd\fP,
                                         int \fItype\fP,
                                         void *\fIuserdata\fP)
.PP
.B void ares_set_socket_configure_callback(ares_channel \fIchannel\fP,
                             ares_sock_config_callback \fIcallback\fP,
                             void *\fIuserdata\fP)
.PP
.B cc file.c -lcares
.fi
.SH DESCRIPTION
.PP
This function sets a \fIcallback\fP in the given ares channel handle. This
callback function will be invoked after the socket has been created, but
before it has been connected to the remote server, which is an ideal time
to configure various socket options.  The callback must return ARES_SUCCESS
if things are fine, or return -1 to signal an error. A returned error will
abort the ares operation.
.SH SEE ALSO
.BR ares_init_options (3), ares_set_socket_callback (3)
.SH AVAILABILITY
ares_set_socket_configure_callback(3) was added in c-ares 1.11.0
.SH AUTHOR
Andrew Ayer

