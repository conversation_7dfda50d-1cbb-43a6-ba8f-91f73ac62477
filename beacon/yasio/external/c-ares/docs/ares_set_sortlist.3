.\"
.\" Permission to use, copy, modify, and distribute this
.\" software and its documentation for any purpose and without
.\" fee is hereby granted, provided that the above copyright
.\" notice appear in all copies and that both that copyright
.\" notice and this permission notice appear in supporting
.\" documentation, and that the name of M.I.T. not be used in
.\" advertising or publicity pertaining to distribution of the
.\" software without specific, written prior permission.
.\" M.I.T. makes no representations about the suitability of
.\" this software for any purpose.  It is provided "as is"
.\" without express or implied warranty.
.\"
.TH ARES_SET_SORTLIST 3 "23 November 2015"
.SH NAME
ares_set_sortlist \- Initialize an ares_channel sortlist configuration
.SH SYNOPSIS
.nf
.B #include <ares.h>
.PP
.B int ares_set_sortlist(ares_channel \fIchannel\fP, const char *\fIsortstr\fP)
.fi
.SH DESCRIPTION
The \fBares_set_sortlist(3)\fP function initializes an address sortlist configuration
for the channel data identified by
.IR channel ,
so that addresses returned by \fBares_gethostbyname(3)\fP are sorted according to the
sortlist.  The provided
.IR sortstr
string that holds a space separated list of IP-address-netmask pairs.  The
netmask is optional but follows the address after a slash if present.  For example,
"*************/************* ***********".

This function replaces any potentially previously configured address sortlist
with the ones given in the configuration string.

.SH RETURN VALUES
.B ares_set_sortlist(3)
may return any of the following values:
.TP 15
.B ARES_SUCCESS
The sortlist configuration was successfully initialized.
.TP 15
.B ARES_ENOMEM
The process's available memory was exhausted.
.TP 15
.B ARES_ENODATA
The channel data identified by
.IR channel
was invalid.
.TP 15
.B ARES_ENOTINITIALIZED
c-ares library initialization not yet performed.
.SH SEE ALSO
.BR ares_init_options (3),
.BR ares_dup(3)
.SH AVAILABILITY
ares_set_sortlist(3) was added in c-ares 1.11.0
