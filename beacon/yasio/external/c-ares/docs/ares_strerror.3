.\"
.\" Copyright 1998 by the Massachusetts Institute of Technology.
.\"
.\" Permission to use, copy, modify, and distribute this
.\" software and its documentation for any purpose and without
.\" fee is hereby granted, provided that the above copyright
.\" notice appear in all copies and that both that copyright
.\" notice and this permission notice appear in supporting
.\" documentation, and that the name of M.I.T. not be used in
.\" advertising or publicity pertaining to distribution of the
.\" software without specific, written prior permission.
.\" M.I.T. makes no representations about the suitability of
.\" this software for any purpose.  It is provided "as is"
.\" without express or implied warranty.
.\"
.TH ARES_STRERROR 3 "25 July 1998"
.SH NAME
ares_strerror \- Get the description of an ares library error code
.SH SYNOPSIS
.nf
.B #include <ares.h>
.PP
.B const char *ares_strerror(int \fIcode\fP)
.fi
.SH DESCRIPTION
The
.B ares_strerror
function gets the description of the ares library error code
.IR code ,
returning the result as a NUL-terminated C string.
.SH NOTES
This function is not compatible with ares, it takes a different set of
arguments.
.SH AUTHOR
<PERSON>, MIT Information Systems
.br
Copyright 1998 by the Massachusetts Institute of Technology.
