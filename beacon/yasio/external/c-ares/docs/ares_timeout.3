.\"
.\" Copyright 1998 by the Massachusetts Institute of Technology.
.\"
.\" Permission to use, copy, modify, and distribute this
.\" software and its documentation for any purpose and without
.\" fee is hereby granted, provided that the above copyright
.\" notice appear in all copies and that both that copyright
.\" notice and this permission notice appear in supporting
.\" documentation, and that the name of M.I.T. not be used in
.\" advertising or publicity pertaining to distribution of the
.\" software without specific, written prior permission.
.\" M.I.T. makes no representations about the suitability of
.\" this software for any purpose.  It is provided "as is"
.\" without express or implied warranty.
.\"
.TH ARES_TIMEOUT 3 "25 July 1998"
.SH NAME
ares_timeout \- return maximum time to wait
.SH SYNOPSIS
.nf
#include <ares.h>

struct timeval *ares_timeout(ares_channel \fIchannel\fP,
                             struct timeval *\fImaxtv\fP,
                             struct timeval *\fItv\fP)
.fi
.SH DESCRIPTION
The \fBares_timeout(3)\fP function determines the maximum time for which the
caller should wait before invoking \fIares_process(3)\fP to process timeouts.
The parameter \fImaxtv\fP specifies a existing maximum timeout, or \fBNULL\fP
if the caller does not wish to apply a maximum timeout.  The parameter
\fItv\fP must point to a writable buffer of type \fBstruct timeval\fP It is
valid for \fImaxtv\fP and \fItv\fP to have the same value.

If no queries have timeouts pending sooner than the given maximum timeout,
\fBares_timeout(3)\fP returns the value of \fImaxtv\fP; otherwise
\fBares_timeout(3)\fP stores the appropriate timeout value into the buffer
pointed to by \fItv\fP and returns the value of \fItv\fP.
.SH SEE ALSO
.BR ares_fds (3),
.BR ares_process (3),
.BR ares_process_fd (3)
.SH AUTHOR
Greg Hudson, MIT Information Systems
.br
Copyright 1998 by the Massachusetts Institute of Technology.
