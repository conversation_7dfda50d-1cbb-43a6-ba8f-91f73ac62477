#***************************************************************************
# Project        ___       __ _ _ __ ___  ___ 
#               / __|____ / _` | '__/ _ \/ __|
#              | (_|_____| (_| | | |  __/\__ \
#               \___|     \__,_|_|  \___||___/
#
prefix=@prefix@
exec_prefix=@exec_prefix@
libdir=@libdir@
includedir=@includedir@

Name: c-ares
URL: http://daniel.haxx.se/projects/c-ares/
Description: asynchronous DNS lookup library
Version: @VERSION@
Requires: 
Requires.private: 
Cflags: -I${includedir} @CPPFLAG_CARES_STATICLIB@
Libs: -L${libdir} -lcares
Libs.private: @CARES_PRIVATE_LIBS@
