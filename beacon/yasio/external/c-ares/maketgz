#!/usr/bin/env perl

$version = $ARGV[0];

if($version eq "") {
    print "Enter version number!\n";
    exit;
}

if(!-f "include/ares.h") {
    print "run this script in the ares source root dir\n";
    exit;
}

my ($major, $minor, $patch)=split(/\./, $version);

$major += 0;
$minor += 0;
$patch += 0;

open(VER, "<include/ares_version.h") ||
    die "can't open include/ares_version.h";
open(NEWV, ">include/ares_version.h.dist");
while(<VER>) {
    $_ =~ s/^\#define ARES_VERSION_MAJOR .*/\#define ARES_VERSION_MAJOR $major/;
    $_ =~ s/^\#define ARES_VERSION_MINOR .*/\#define ARES_VERSION_MINOR $minor/;
    $_ =~ s/^\#define ARES_VERSION_PATCH .*/\#define ARES_VERSION_PATCH $patch/;
    $_ =~ s/^\#define ARES_VERSION_STR .*/\#define ARES_VERSION_STR \"$version\"/;

    print NEWV $_;
}
close(VER);
close(NEWV);
print "include/ares_version.h.dist created\n";

if(!-f "configure") {
    print "running buildconf\n";
    `./buildconf`;
}
print "adding $version in the configure.ac file\n";
`sed -e 's/AC_INIT.*/AC_INIT([c-ares], [$version],/' < configure.ac > configure.ac.dist`;

print "adding $version in the CMakeLists.txt file\n";
`sed -e 's/SET.*CARES_VERSION.*/SET (CARES_VERSION "$version")/' < CMakeLists.txt > CMakeLists.txt.dist && rm -f CMakeLists.txt && mv CMakeLists.txt.dist CMakeLists.txt`;

# now make a new configure script with this
print "makes a new configure script\n";
`autoconf configure.ac.dist >configure`;

# now run this new configure to get a fine makefile
print "running configure\n";
`./configure`;

print "produce CHANGES\n";
`git log --pretty=fuller --no-color --date=short --decorate=full -1000 | ./git2changes.pl > CHANGES.dist`;

# now make the actual tarball
print "running make dist\n";
`make dist VERSION=$version`;

# remove temporay sourced man pages
`make -s clean-sourced-manpages`;

print "removing temporary configure.ac file\n";
`rm configure.ac.dist`;
print "removing temporary ares_version.h file\n";
`rm include/ares_version.h.dist`;

print "NOTE: now tag this release!\n";
