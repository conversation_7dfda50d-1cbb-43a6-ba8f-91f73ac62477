#include "ares-test.h"
#include "dns-proto.h"

#include <sstream>
#include <vector>

namespace ares {
namespace test {

TEST_F(LibraryTest, ParseCaaReplyMultipleOK) {
  std::vector<byte> data = {
    0x27, 0x86, 0x81, 0x80, 0x00, 0x01, 0x00, 0x04,  0x00, 0x00, 0x00, 0x00, 0x09, 0x77, 0x69, 0x6B, // '............wik
    0x69, 0x70, 0x65, 0x64, 0x69, 0x61, 0x03, 0x6F,  0x72, 0x67, 0x00, 0x01, 0x01, 0x00, 0x01, 0xC0, // ipedia.org......
    0x0C, 0x01, 0x01, 0x00, 0x01, 0x00, 0x00, 0x02,  0x23, 0x00, 0x15, 0x00, 0x05, 0x69, 0x73, 0x73, // ........#....iss
    0x75, 0x65, 0x67, 0x6C, 0x6F, 0x62, 0x61, 0x6C,  0x73, 0x69, 0x67, 0x6E, 0x2E, 0x63, 0x6F, 0x6D, // ueglobalsign.com
    0xC0, 0x0C, 0x01, 0x01, 0x00, 0x01, 0x00, 0x00,  0x02, 0x23, 0x00, 0x13, 0x00, 0x05, 0x69, 0x73, // .........#....is
    0x73, 0x75, 0x65, 0x64, 0x69, 0x67, 0x69, 0x63,  0x65, 0x72, 0x74, 0x2E, 0x63, 0x6F, 0x6D, 0xC0, // suedigicert.com.
    0x0C, 0x01, 0x01, 0x00, 0x01, 0x00, 0x00, 0x02,  0x23, 0x00, 0x16, 0x00, 0x05, 0x69, 0x73, 0x73, // ........#....iss
    0x75, 0x65, 0x6C, 0x65, 0x74, 0x73, 0x65, 0x6E,  0x63, 0x72, 0x79, 0x70, 0x74, 0x2E, 0x6F, 0x72, // ueletsencrypt.or
    0x67, 0xC0, 0x0C, 0x01, 0x01, 0x00, 0x01, 0x00,  0x00, 0x02, 0x23, 0x00, 0x25, 0x00, 0x05, 0x69, // g.........#.%..i
    0x6F, 0x64, 0x65, 0x66, 0x6D, 0x61, 0x69, 0x6C,  0x74, 0x6F, 0x3A, 0x64, 0x6E, 0x73, 0x2D, 0x61, // odefmailto:dns-a
    0x64, 0x6D, 0x69, 0x6E, 0x40, 0x77, 0x69, 0x6B,  0x69, 0x6D, 0x65, 0x64, 0x69, 0x61, 0x2E, 0x6F, // dmin@wikimedia.o
    0x72, 0x67                                                                                       // rg
  };

  struct ares_caa_reply* caa = nullptr;
  EXPECT_EQ(ARES_SUCCESS, ares_parse_caa_reply(data.data(), data.size(), &caa));
  ASSERT_NE(nullptr, caa);
  ASSERT_NE(nullptr, caa->next);
  ASSERT_NE(nullptr, caa->next->next);
  ASSERT_NE(nullptr, caa->next->next->next);

  ares_free_data(caa);
}

TEST_F(LibraryTest, ParseCaaReplySingleOK) {
  std::vector<byte> data = {
    0x27, 0x86, 0x81, 0x80, 0x00, 0x01, 0x00, 0x01,  0x00, 0x00, 0x00, 0x00, 0x06, 0x67, 0x6F, 0x6F,  //  '............goo 
    0x67, 0x6C, 0x65, 0x03, 0x63, 0x6F, 0x6D, 0x00,  0x01, 0x01, 0x00, 0x01, 0xC0, 0x0C, 0x01, 0x01,  //  gle.com......... 
    0x00, 0x01, 0x00, 0x01, 0x43, 0xBE, 0x00, 0x0F,  0x00, 0x05, 0x69, 0x73, 0x73, 0x75, 0x65, 0x70,  //  ....C.....issuep 
    0x6B, 0x69, 0x2E, 0x67, 0x6F, 0x6F, 0x67                                                          //  ki.goog 
  };

  struct ares_caa_reply* caa = nullptr;
  EXPECT_EQ(ARES_SUCCESS, ares_parse_caa_reply(data.data(), data.size(), &caa));
  ASSERT_NE(nullptr, caa);

  EXPECT_EQ(caa->critical, 0);
  EXPECT_EQ(caa->plength, 5);
  EXPECT_STREQ((char *)caa->property, "issue");
  EXPECT_EQ(caa->length, 8);
  EXPECT_STREQ((char *)caa->value, "pki.goog");

  ares_free_data(caa);
}

TEST_F(LibraryTest, ParseCaaBogusReply1) {
  std::vector<byte> data = {
    0x27, 0x86, 0x81, 0x80, 0x00, 0x01, 0x00, 0x01,  0x00, 0x00, 0x00, 0x00, 0x06, 0x67, 0x6F, 0x6F,  //  '............goo 
    0x67, 0x6C, 0x65, 0x03, 0x63, 0x6F, 0x6D, 0x00,  0x01, 0x01, 0x00, 0x01, 0xC0, 0x0C, 0x01, 0x01,  //  gle.com......... 
    0x00, 0x01, 0x00, 0x01, 0x43, 0xBE, 0x00, 0x0F,  0x00, 0x00, 0x69, 0x73, 0x73, 0x75, 0x65, 0x70,  //  ....C.....issuep 
    0x6B, 0x69, 0x2E, 0x67, 0x6F, 0x6F, 0x67                                                          //  ki.goog 
  };

  struct ares_caa_reply* caa = nullptr;
  EXPECT_EQ(ARES_EBADRESP, ares_parse_caa_reply(data.data(), data.size(), &caa));
  ASSERT_EQ(nullptr, caa);
}

TEST_F(LibraryTest, ParseCaaBogusReply2) {
  std::vector<byte> data = {
    0x27, 0x86, 0x81, 0x80, 0x00, 0x01, 0x00, 0x01,  0x00, 0x00, 0x00, 0x00, 0x06, 0x67, 0x6F, 0x6F,  //  '............goo 
    0x67, 0x6C, 0x65, 0x03, 0x63, 0x6F, 0x6D, 0x00,  0x01, 0x01, 0x00, 0x01, 0xC0, 0x0C, 0x01, 0x01,  //  gle.com......... 
    0x00, 0x01, 0x00, 0x01, 0x43, 0xBE, 0x00, 0x0F,  0x00, 0x0e, 0x69, 0x73, 0x73, 0x75, 0x65, 0x70,  //  ....C.....issuep 
    0x6B, 0x69, 0x2E, 0x67, 0x6F, 0x6F, 0x67                                                          //  ki.goog 
  };

  struct ares_caa_reply* caa = nullptr;
  EXPECT_EQ(ARES_EBADRESP, ares_parse_caa_reply(data.data(), data.size(), &caa));
  ASSERT_EQ(nullptr, caa);
}

TEST_F(LibraryTest, ParseCaaBogusReply3) {
  std::vector<byte> data = {
    0x27, 0x86, 0x81, 0x80, 0x00, 0x01, 0x00, 0x01,  0x00, 0x00, 0x00, 0x00, 0x06, 0x67, 0x6F, 0x6F,  //  '............goo 
    0x67, 0x6C, 0x65, 0x03, 0x63, 0x6F, 0x6D, 0x00,  0x01, 0x01, 0x00, 0x01, 0xC0, 0x0C, 0x01, 0x01,  //  gle.com......... 
    0x00, 0x01, 0x00, 0x01, 0x43, 0xBE, 0x00, 0x10,  0x00, 0x05, 0x69, 0x73, 0x73, 0x75, 0x65, 0x70,  //  ....C.....issuep 
    0x6B, 0x69, 0x2E, 0x67, 0x6F, 0x6F, 0x67                                                          //  ki.goog 
  };

  struct ares_caa_reply* caa = nullptr;
  EXPECT_EQ(ARES_EBADRESP, ares_parse_caa_reply(data.data(), data.size(), &caa));
  ASSERT_EQ(nullptr, caa);
}

TEST_F(LibraryTest, ParseCaaEmptyReply) {
  std::vector<byte> data = {
    0x27, 0x86, 0x81, 0x80, 0x00, 0x01, 0x00, 0x00,  0x00, 0x01, 0x00, 0x00, 0x09, 0x77, 0x69, 0x6B,  //  '............wik 
    0x69, 0x70, 0x65, 0x64, 0x69, 0x61, 0x02, 0x64,  0x65, 0x00, 0x01, 0x01, 0x00, 0x01, 0xC0, 0x0C,  //  ipedia.de....... 
    0x00, 0x06, 0x00, 0x01, 0x00, 0x00, 0x02, 0x58,  0x00, 0x3B, 0x04, 0x6E, 0x73, 0x38, 0x31, 0x0D,  //  .......X.;.ns81. 
    0x64, 0x6F, 0x6D, 0x61, 0x69, 0x6E, 0x63, 0x6F,  0x6E, 0x74, 0x72, 0x6F, 0x6C, 0x03, 0x63, 0x6F,  //  domaincontrol.co 
    0x6D, 0x00, 0x03, 0x64, 0x6E, 0x73, 0x05, 0x6A,  0x6F, 0x6D, 0x61, 0x78, 0x03, 0x6E, 0x65, 0x74,  //  m..dns.jomax.net 
    0x00, 0x78, 0x67, 0xFE, 0x34, 0x00, 0x00, 0x70,  0x80, 0x00, 0x00, 0x1C, 0x20, 0x00, 0x09, 0x3A,  //  .xg.4..p.... ..: 
    0x80, 0x00, 0x00, 0x02, 0x58                                                                      //  ....X 
  };

  struct ares_caa_reply* caa = nullptr;
  EXPECT_EQ(ARES_ENODATA, ares_parse_caa_reply(data.data(), data.size(), &caa));
  ASSERT_EQ(nullptr, caa);
}

}  // namespace test
}  // namespace ares
