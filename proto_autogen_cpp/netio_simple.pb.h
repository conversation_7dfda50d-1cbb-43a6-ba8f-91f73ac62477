// 简化版 netio.pb.h - 避免protobuf版本兼容问题
#ifndef NETIO_SIMPLE_PB_H
#define NETIO_SIMPLE_PB_H

#include <string>
#include <vector>

// 添加c2命名空间以兼容现有代码
namespace c2 {

// 连接类型枚举
enum ConnType {
    CONNNAME_TCP = 0,
    CONNNAME_UDP = 1,
    CONNNAME_HTTP = 2
};

// 简化的网络消息类
class NetMessage {
public:
    NetMessage() = default;
    ~NetMessage() = default;
    
    // 基本方法
    void Clear() {}
    bool IsInitialized() const { return true; }
    std::string GetTypeName() const { return "NetMessage"; }
    
    // 字段访问方法
    void set_type(int value) { type_ = value; }
    int type() const { return type_; }
    
    void set_data(const std::string& value) { data_ = value; }
    const std::string& data() const { return data_; }
    
    void set_timestamp(long long value) { timestamp_ = value; }
    long long timestamp() const { return timestamp_; }
    
private:
    int type_ = 0;
    std::string data_;
    long long timestamp_ = 0;
};

class NetRequest {
public:
    NetRequest() = default;
    ~NetRequest() = default;
    
    void Clear() {}
    bool IsInitialized() const { return true; }
    std::string GetTypeName() const { return "NetRequest"; }
    
    void set_request_id(int value) { request_id_ = value; }
    int request_id() const { return request_id_; }
    
    void set_method(const std::string& value) { method_ = value; }
    const std::string& method() const { return method_; }
    
    void set_url(const std::string& value) { url_ = value; }
    const std::string& url() const { return url_; }
    
private:
    int request_id_ = 0;
    std::string method_;
    std::string url_;
};

class NetResponse {
public:
    NetResponse() = default;
    ~NetResponse() = default;
    
    void Clear() {}
    bool IsInitialized() const { return true; }
    std::string GetTypeName() const { return "NetResponse"; }
    
    void set_request_id(int value) { request_id_ = value; }
    int request_id() const { return request_id_; }
    
    void set_status_code(int value) { status_code_ = value; }
    int status_code() const { return status_code_; }
    
    void set_body(const std::string& value) { body_ = value; }
    const std::string& body() const { return body_; }
    
private:
    int request_id_ = 0;
    int status_code_ = 200;
    std::string body_;
};

} // namespace c2

#endif // NETIO_SIMPLE_PB_H
