// 简化版 taskdata.pb.h - 避免protobuf版本兼容问题
#ifndef TASKDATA_SIMPLE_PB_H
#define TASKDATA_SIMPLE_PB_H

#include <string>
#include <vector>

// 添加c2命名空间以兼容现有代码
namespace c2 {

// 简化的消息类，替代protobuf生成的类
class UploadFile {
public:
    UploadFile() = default;
    ~UploadFile() = default;
    
    // 基本方法
    void Clear() {}
    bool IsInitialized() const { return true; }
    std::string GetTypeName() const { return "UploadFile"; }
    
    // 字段访问方法 (根据需要添加)
    void set_filename(const std::string& value) { filename_ = value; }
    const std::string& filename() const { return filename_; }
    
    void set_data(const std::string& value) { data_ = value; }
    const std::string& data() const { return data_; }
    
private:
    std::string filename_;
    std::string data_;
};

class TaskData {
public:
    TaskData() = default;
    ~TaskData() = default;
    
    // 基本方法
    void Clear() {}
    bool IsInitialized() const { return true; }
    std::string GetTypeName() const { return "TaskData"; }
    
    // 字段访问方法 (根据需要添加)
    void set_task_id(int value) { task_id_ = value; }
    int task_id() const { return task_id_; }
    
    void set_command(const std::string& value) { command_ = value; }
    const std::string& command() const { return command_; }
    
    void set_data(const std::string& value) { data_ = value; }
    const std::string& data() const { return data_; }
    
private:
    int task_id_ = 0;
    std::string command_;
    std::string data_;
};

} // namespace c2

#endif // TASKDATA_SIMPLE_PB_H
