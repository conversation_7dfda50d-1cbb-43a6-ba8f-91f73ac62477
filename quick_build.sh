#!/bin/bash

# 快速编译脚本 - 编译整个项目

echo "=== 开始编译项目 ==="
echo

# 设置项目根目录
PROJECT_ROOT="/Users/<USER>/project/mac"
cd "$PROJECT_ROOT"

# 创建构建目录
mkdir -p build_tmp

echo "1. 编译 beacon 程序..."
cd beacon
if sh gen-clang-project.sh; then
    echo "✅ beacon 编译成功"
else
    echo "❌ beacon 编译失败"
    exit 1
fi

echo
echo "2. 编译 loader 程序..."
cd "$PROJECT_ROOT"
if sh loader/build.sh; then
    echo "✅ loader 编译成功"
else
    echo "❌ loader 编译失败"
    exit 1
fi

echo
echo "=== 编译完成 ==="
echo "构建文件位于: $PROJECT_ROOT/build_tmp"
echo
ls -la "$PROJECT_ROOT/build_tmp"

echo
echo "注意: 此脚本仅编译程序，不会运行编译后的程序。"
