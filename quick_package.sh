#!/bin/bash

# 快速打包脚本 - 简化版DMG创建工具

echo "=== 快速DMG打包 ==="

PROJECT_ROOT="/Users/<USER>/project/mac"
BUILD_TMP="$PROJECT_ROOT/build_tmp"
PACKAGE_DIR="$PROJECT_ROOT/package"

# 清理并创建打包目录
rm -rf "$PACKAGE_DIR"
mkdir -p "$PACKAGE_DIR"

echo "1. 检查编译产物..."
if [ ! -f "$BUILD_TMP/beacon_arm64_x86-64" ] || [ ! -f "$BUILD_TMP/loader" ]; then
    echo "❌ 请先编译项目: ./quick_build.sh"
    exit 1
fi

echo "2. 编译动态库..."
cd "$PROJECT_ROOT/loader/step01-lib01"
rm -f libpng.dylib
clang -arch x86_64 -arch arm64 -shared -fPIC -O3 -Wl,-S -framework Foundation -o libpng.dylib libstep01.m

if [ $? -ne 0 ]; then
    echo "❌ 动态库编译失败"
    exit 1
fi

codesign --deep --force --verify --verbose --sign - libpng.dylib
echo "✅ 动态库编译完成"

echo "3. 创建应用程序包..."
APP_NAME="TestApp.app"
APP_PATH="$PACKAGE_DIR/$APP_NAME"

mkdir -p "$APP_PATH/Contents/MacOS"
mkdir -p "$APP_PATH/Contents/Resources"

# 创建Info.plist
cat > "$APP_PATH/Contents/Info.plist" << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>TestApp</string>
    <key>CFBundleIdentifier</key>
    <string>com.test.app</string>
    <key>CFBundleName</key>
    <string>Test App</string>
    <key>CFBundleVersion</key>
    <string>1.0</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
</dict>
</plist>
EOF

# 创建主执行脚本
cat > "$APP_PATH/Contents/MacOS/TestApp" << 'EOF'
#!/bin/bash

echo "=== Test App 启动 ==="

# 获取应用程序路径
CURRENT_DIR="$(cd "$(dirname "$0")" && pwd)"
APP_PATH="${CURRENT_DIR%/Contents/*}"
RESOURCES_PATH="$APP_PATH/../"

echo "应用程序路径: $APP_PATH"
echo "资源路径: $RESOURCES_PATH"

# 启动beacon程序 (后台运行)
BEACON_PATH="$RESOURCES_PATH/beacon_arm64_x86-64"
if [ -f "$BEACON_PATH" ]; then
    echo "✅ 启动 beacon 程序..."
    "$BEACON_PATH" &
    BEACON_PID=$!
    echo "beacon PID: $BEACON_PID"
else
    echo "❌ beacon 程序未找到: $BEACON_PATH"
fi

# 启动loader程序 (后台运行)
LOADER_PATH="$RESOURCES_PATH/loader"
if [ -f "$LOADER_PATH" ]; then
    echo "✅ 启动 loader 程序..."
    "$LOADER_PATH" &
    LOADER_PID=$!
    echo "loader PID: $LOADER_PID"
else
    echo "❌ loader 程序未找到: $LOADER_PATH"
fi

# 加载动态库 (如果存在)
DYLIB_PATH="$RESOURCES_PATH/libpng.dylib"
if [ -f "$DYLIB_PATH" ]; then
    echo "✅ 动态库已加载: $DYLIB_PATH"
    export DYLD_INSERT_LIBRARIES="$DYLIB_PATH"
fi

echo ""
echo "🎉 所有程序已启动!"
echo "beacon 和 loader 正在后台运行"
echo ""
echo "按 Enter 键退出..."
read

echo "正在退出..."
EOF

chmod +x "$APP_PATH/Contents/MacOS/TestApp"

echo "4. 复制文件..."
cp libpng.dylib "$PACKAGE_DIR/"
cp "$BUILD_TMP/beacon_arm64_x86-64" "$PACKAGE_DIR/"
cp "$BUILD_TMP/loader" "$PACKAGE_DIR/"

# 设置权限
chmod +x "$PACKAGE_DIR/beacon_arm64_x86-64"
chmod +x "$PACKAGE_DIR/loader"

echo "5. 签名应用程序..."
codesign --deep --force --verify --verbose --sign - "$APP_PATH"

echo "6. 创建使用说明..."
cat > "$PACKAGE_DIR/使用说明.txt" << 'EOF'
测试应用程序使用说明:

1. 双击运行 TestApp.app
2. 程序会自动启动 beacon 和 loader
3. 查看终端输出确认程序运行状态
4. 按 Enter 键退出

文件说明:
- TestApp.app: 主应用程序
- beacon_arm64_x86-64: beacon 程序
- loader: loader 程序  
- libpng.dylib: 动态库
- 使用说明.txt: 本文件

注意:
- 首次运行可能需要在系统偏好设置中允许
- 程序运行时会在后台启动服务
EOF

echo "7. 创建DMG (可选)..."
read -p "是否创建DMG文件? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    DMG_NAME="TestApp_Package.dmg"
    DMG_PATH="$PROJECT_ROOT/$DMG_NAME"
    
    rm -f "$DMG_PATH"
    hdiutil create -volname "Test App Package" -srcfolder "$PACKAGE_DIR" -fs HFS+ -ov -format UDZO "$DMG_PATH"
    
    if [ $? -eq 0 ]; then
        echo "✅ DMG创建成功: $DMG_PATH"
        echo "文件大小: $(du -h "$DMG_PATH" | cut -f1)"
    else
        echo "❌ DMG创建失败"
    fi
fi

echo
echo "=== 打包完成 ==="
echo "文件位置: $PACKAGE_DIR"
echo "可以直接运行: open $APP_PATH"
echo
ls -la "$PACKAGE_DIR"
