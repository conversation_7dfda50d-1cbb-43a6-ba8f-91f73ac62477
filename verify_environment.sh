#!/bin/bash

# 环境验证脚本
# 用于检查编译环境是否正确设置

echo "=== macOS M芯片编译环境验证 ==="
echo

# 检查系统信息
echo "1. 系统信息:"
echo "   架构: $(uname -m)"
echo "   系统版本: $(sw_vers -productVersion)"
echo

# 检查基础工具
echo "2. 基础工具检查:"

if command -v clang &> /dev/null; then
    echo "   ✅ clang: $(clang --version | head -n1)"
else
    echo "   ❌ clang 未安装"
fi

if command -v cmake &> /dev/null; then
    echo "   ✅ cmake: $(cmake --version | head -n1)"
else
    echo "   ❌ cmake 未安装"
fi

if command -v pkg-config &> /dev/null; then
    echo "   ✅ pkg-config: $(pkg-config --version)"
else
    echo "   ❌ pkg-config 未安装"
fi

if command -v brew &> /dev/null; then
    echo "   ✅ homebrew: $(brew --version | head -n1)"
else
    echo "   ❌ homebrew 未安装"
fi

echo

# 检查 vcpkg
echo "3. vcpkg 检查:"
VCPKG_DIR="/Users/<USER>/vcpkg"
if [ -d "$VCPKG_DIR" ]; then
    echo "   ✅ vcpkg 目录存在: $VCPKG_DIR"
    if [ -f "$VCPKG_DIR/vcpkg" ]; then
        echo "   ✅ vcpkg 可执行文件存在"
    else
        echo "   ❌ vcpkg 可执行文件不存在"
    fi
else
    echo "   ❌ vcpkg 目录不存在: $VCPKG_DIR"
fi

echo

# 检查依赖库
echo "4. 依赖库检查:"

# 检查 x64-osx 库
X64_LIB_DIR="$VCPKG_DIR/installed/x64-osx/lib"
if [ -d "$X64_LIB_DIR" ]; then
    echo "   ✅ x64-osx 库目录存在"
    
    if [ -f "$X64_LIB_DIR/libprotobuf-lite.a" ]; then
        echo "   ✅ x64-osx protobuf-lite 库存在"
    else
        echo "   ❌ x64-osx protobuf-lite 库不存在"
    fi
    
    if [ -f "$X64_LIB_DIR/libcryptopp.a" ]; then
        echo "   ✅ x64-osx cryptopp 库存在"
    else
        echo "   ❌ x64-osx cryptopp 库不存在"
    fi
else
    echo "   ❌ x64-osx 库目录不存在"
fi

# 检查 arm64-osx 库
ARM64_LIB_DIR="$VCPKG_DIR/installed/arm64-osx/lib"
if [ -d "$ARM64_LIB_DIR" ]; then
    echo "   ✅ arm64-osx 库目录存在"
    
    if [ -f "$ARM64_LIB_DIR/libprotobuf-lite.a" ]; then
        echo "   ✅ arm64-osx protobuf-lite 库存在"
    else
        echo "   ❌ arm64-osx protobuf-lite 库不存在"
    fi
    
    if [ -f "$ARM64_LIB_DIR/libcryptopp.a" ]; then
        echo "   ✅ arm64-osx cryptopp 库存在"
    else
        echo "   ❌ arm64-osx cryptopp 库不存在"
    fi
else
    echo "   ❌ arm64-osx 库目录不存在"
fi

echo

# 检查项目文件
echo "5. 项目文件检查:"
PROJECT_DIR="/Users/<USER>/project/mac"

if [ -f "$PROJECT_DIR/beacon/CMakeLists.txt" ]; then
    echo "   ✅ beacon CMakeLists.txt 存在"
else
    echo "   ❌ beacon CMakeLists.txt 不存在"
fi

if [ -f "$PROJECT_DIR/beacon/gen-clang-project.sh" ]; then
    echo "   ✅ beacon 编译脚本存在"
    if [ -x "$PROJECT_DIR/beacon/gen-clang-project.sh" ]; then
        echo "   ✅ beacon 编译脚本可执行"
    else
        echo "   ⚠️  beacon 编译脚本不可执行 (需要 chmod +x)"
    fi
else
    echo "   ❌ beacon 编译脚本不存在"
fi

if [ -f "$PROJECT_DIR/loader/build.sh" ]; then
    echo "   ✅ loader 编译脚本存在"
    if [ -x "$PROJECT_DIR/loader/build.sh" ]; then
        echo "   ✅ loader 编译脚本可执行"
    else
        echo "   ⚠️  loader 编译脚本不可执行 (需要 chmod +x)"
    fi
else
    echo "   ❌ loader 编译脚本不存在"
fi

echo
echo "=== 验证完成 ==="
echo
echo "如果所有项目都显示 ✅，说明环境配置正确，可以开始编译。"
echo "如果有 ❌ 或 ⚠️ 项目，请根据提示解决相应问题。"
