# macOS M芯片编译环境搭建文档

本文档详细介绍如何在搭载Apple Silicon (M系列芯片) 的Mac上设置编译环境，以便成功编译项目代码。

## 系统要求

- macOS 12.0 或更高版本
- Apple Silicon (M1/M2/M3系列芯片) 或 Intel 处理器
- 至少 8GB 内存
- 至少 20GB 可用磁盘空间

## 安装步骤

### 1. 安装 Xcode Command Line Tools

Xcode Command Line Tools 提供了编译所需的基本工具，包括 clang 编译器。

```bash
xcode-select --install
```

在弹出的对话框中点击"安装"，等待安装完成。

### 2. 安装 Homebrew

Homebrew 是 macOS 的包管理器，我们将用它来安装其他工具。

```bash
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

安装完成后，按照终端提示将 Homebrew 添加到 PATH：

```bash
echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zprofile
eval "$(/opt/homebrew/bin/brew shellenv)"
```

### 3. 安装 CMake 和 pkg-config

CMake 是项目使用的构建系统，pkg-config 用于管理库的编译和链接标志。

```bash
brew install cmake pkg-config
```

验证安装：

```bash
cmake --version
pkg-config --version
```

### 4. 安装 vcpkg

vcpkg 是 Microsoft 的 C++ 库管理器，用于安装项目依赖。

```bash
cd ~
git clone https://github.com/Microsoft/vcpkg.git
cd vcpkg
./bootstrap-vcpkg.sh
```

### 5. 安装项目依赖库

使用 vcpkg 安装项目所需的依赖库，需要同时安装 x64 和 arm64 架构版本：

```bash
# 安装 protobuf
./vcpkg install protobuf:x64-osx
./vcpkg install protobuf:arm64-osx

# 安装 cryptopp
./vcpkg install cryptopp:x64-osx
./vcpkg install cryptopp:arm64-osx
```

## 编译项目

### 1. 编译 beacon 程序

```bash
cd /path/to/project/beacon
sh gen-clang-project.sh
```

这个脚本会：
1. 清理旧的构建文件
2. 使用 CMake 配置项目
3. 编译 x86_64 架构版本
4. 编译 arm64 架构版本
5. 使用 lipo 工具将两个架构合并为通用二进制文件

### 2. 编译 loader 程序

```bash
cd /path/to/project/loader
sh build.sh
```

## 注意事项

1. 项目脚本中的路径可能需要根据实际情况调整，特别是 `/Users/<USER>/src` 这样的硬编码路径。

2. 如果遇到权限问题，可能需要使用 `chmod +x` 命令使脚本可执行：
   ```bash
   chmod +x gen-clang-project.sh
   chmod +x build.sh
   ```

3. 编译过程中可能会出现警告，这通常不影响最终的编译结果。

4. 项目使用了 vcpkg 的特定路径 (`/Users/<USER>/vcpkg`)，请确保按照文档中的路径安装 vcpkg。

## 故障排除

### 找不到依赖库

如果编译时报错找不到某个依赖库，请检查：

1. vcpkg 是否正确安装在 `/Users/<USER>/vcpkg` 目录
2. 依赖库是否已通过 vcpkg 安装
3. CMake 配置中的 vcpkg 工具链文件路径是否正确

### 编译错误

如果遇到编译错误：

1. 确保已安装最新版本的 Xcode Command Line Tools
2. 检查是否安装了正确架构的依赖库 (x64-osx 和 arm64-osx)
3. 查看具体错误信息，解决相应问题

## 环境验证

完成所有安装后，可以运行以下命令验证环境：

```bash
clang --version
cmake --version
ls -la ~/vcpkg/installed/x64-osx/lib/
ls -la ~/vcpkg/installed/arm64-osx/lib/
```

确保能看到已安装的 protobuf 和 cryptopp 库文件。

## DMG打包说明

项目支持将编译好的程序打包成DMG安装包，实现运行DMG中的应用时同时启动原程序和我们的程序。

### 打包原理

1. **动态库注入**: 使用 `libpng.dylib` 动态库注入到目标应用程序
2. **应用程序包装**: 创建包装应用程序，启动时同时运行多个程序
3. **DMG分发**: 将所有文件打包成DMG镜像便于分发

### 打包步骤

#### 1. 基础打包 (创建示例应用)

```bash
# 先编译项目
./quick_build.sh

# 创建基础DMG包
chmod +x create_dmg_package.sh
./create_dmg_package.sh
```

#### 2. 高级打包 (注入真实应用)

```bash
# 安装 insert_dylib 工具
brew install insert_dylib

# 打包指定的应用程序
chmod +x advanced_dmg_creator.sh
./advanced_dmg_creator.sh /Applications/目标应用.app
```

### 使用方法

1. **安装DMG**: 双击生成的DMG文件
2. **拖拽安装**: 将应用程序拖拽到Applications文件夹
3. **运行程序**: 启动应用程序时会自动运行后台服务

### 技术细节

- **dylib注入**: 使用 `@rpath/libpng.dylib` 路径注入动态库
- **多架构支持**: 同时支持 x86_64 和 arm64 架构
- **代码签名**: 自动处理应用程序的代码签名
- **权限处理**: 自动设置必要的执行权限
